from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi import status
from typing import Optional, List
from tortoise.queryset import Q
import logging
from datetime import datetime, timedelta
from collections import Counter

from app.models.resource import PanResource
from app.models.feedback import ResourceInvalidFeedback
from app.models.user import User
from app.models.enums import TimeFilter
from app.core.permissions import (
    PermissionChecker,
    Permissions,
    get_current_user_optional,
)
from app.services.local_search_service import local_search_service
from app.api.resource import get_resource_details
from app.api.stats import get_resource_stats

logger = logging.getLogger("admin-resources-api")
router = APIRouter(tags=["管理员-资源管理"])

# 权限检查器
RequireResourceManage = PermissionChecker(Permissions.RESOURCE_MANAGE)


def get_pan_type_name(pan_type: int) -> str:
    """获取网盘类型名称"""
    mapping = {1: "百度网盘", 2: "夸克网盘", 3: "阿里云盘", 4: "迅雷网盘"}
    return mapping.get(pan_type, "未知网盘")


async def calculate_admin_info(resource_key: str) -> dict:
    """基于现有字段计算管理员专用信息"""
    # 统计相关反馈数量
    feedback_count = await ResourceInvalidFeedback.filter(
        resource_id=resource_key
    ).count()

    # 获取最近反馈时间
    latest_feedback = (
        await ResourceInvalidFeedback.filter(resource_id=resource_key)
        .order_by("-created_at")
        .first()
    )

    return {
        "feedback_count": feedback_count,
        "latest_feedback_time": latest_feedback.created_at if latest_feedback else None,
        "has_pending_feedback": feedback_count > 0,
    }


@router.get(
    "/resources/stats",
    summary="管理员资源统计",
    description="获取详细的资源统计信息，扩展现有resource_stats",
)
async def get_admin_resource_stats(current_user: User = Depends(RequireResourceManage)):
    """
    管理员资源统计 - 扩展现有resource_stats
    """
    try:
        # 复用现有的基础统计
        basic_stats = await get_resource_stats()

        # 扩展管理员专用统计
        admin_stats = await calculate_admin_stats()

        return {"status": "success", "data": {**basic_stats, **admin_stats}}

    except Exception as e:
        logger.error(f"获取管理员资源统计失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取资源统计失败: {str(e)}",
        )


@router.get(
    "/resources",
    summary="管理员资源列表",
    description="获取资源列表，支持高级筛选和搜索，复用cached_resources逻辑",
)
async def get_admin_resources(
    keyword: Optional[str] = Query(
        None, description="搜索关键词（标题/作者/resource_key）"
    ),
    pan_type: Optional[int] = Query(
        None, description="网盘类型筛选: 1=百度网盘, 2=夸克网盘, 3=阿里云盘, 4=迅雷网盘"
    ),
    file_type: Optional[str] = Query(
        None, description="文件类型筛选: video/audio/image/document/archive/application"
    ),
    status: Optional[str] = Query(
        None, description="资源状态筛选: valid/invalid/unknown"
    ),
    is_mine: Optional[bool] = Query(None, description="是否本人上传筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query(
        "updated_at", description="排序字段: updated_at/created_at/access_count/title"
    ),
    sort_order: str = Query("desc", description="排序方向: asc/desc"),
    time_filter: TimeFilter = Query(TimeFilter.ALL, description="时间范围筛选"),
    current_user: User = Depends(RequireResourceManage),
):
    """
    管理员资源列表 - 复用cached_resources逻辑
    """
    try:
        # 参数转换逻辑，映射到local_search_service参数
        search_params = {
            "keyword": keyword or "",  # 确保keyword不为None
            "pan_type": pan_type,
            "file_type": file_type,
            "sort_by": sort_by,
            "sort_order": sort_order,
            "time_filter": time_filter,
            "limit": size * page,  # 获取更多数据用于分页
        }

        # 状态筛选转换
        if status == "valid":
            search_params["valid_only"] = True
        elif status == "invalid":
            search_params["valid_only"] = False

        # 调用现有的本地搜索服务逻辑
        search_result = await local_search_service.search_local(**search_params)

        # 获取资源数据
        resources_data = search_result.get("results", [])
        total = search_result.get("totals", 0)  # 使用正确的字段名

        # 手动分页处理
        start_index = (page - 1) * size
        end_index = start_index + size
        resources_data = resources_data[start_index:end_index]

        # 处理is_mine筛选（在结果中过滤）
        if is_mine is not None:
            filtered_resources = []
            for resource_dict in resources_data:
                # 从数据库获取完整的资源信息以检查is_mine字段
                resource = await PanResource.filter(
                    resource_key=resource_dict.get("resource_id")
                    or resource_dict.get("resource_key")
                ).first()
                if resource and resource.is_mine == is_mine:
                    filtered_resources.append(resource_dict)
            resources_data = filtered_resources
            total = len(filtered_resources)  # 重新计算总数

        # 转换为管理员专用格式
        admin_resources = []
        for resource_dict in resources_data:
            resource_key = resource_dict.get("resource_id") or resource_dict.get(
                "resource_key"
            )

            # 获取完整的资源信息
            resource = await PanResource.filter(resource_key=resource_key).first()
            if not resource:
                continue

            admin_resource = {
                "id": resource.id,
                "resource_key": resource.resource_key,
                "title": resource.title,
                "pan_type": resource.pan_type,
                "text_content": resource.text_content,
                "author": resource.author,
                "updated_at": resource.updated_at,
                "source": "admin_upload" if resource.is_mine else "user_submit",
            }
            admin_resources.append(admin_resource)

        return {
            "status": "success",
            "data": {
                "resources": admin_resources,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size if size > 0 else 0,
                },
            },
        }

    except Exception as e:
        logger.error(f"获取管理员资源列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取资源列表失败: {str(e)}",
        )


@router.get(
    "/resources/{resource_key}",
    summary="管理员资源详情",
    description="获取单个资源的详细信息，包含管理员专用信息",
)
async def get_admin_resource_detail(
    resource_key: str, current_user: User = Depends(RequireResourceManage)
):
    """
    管理员资源详情 - 直接复用resource_details并扩展管理员信息
    """
    try:
        # 直接调用现有的get_resource_details函数
        detail = await get_resource_details(resource_key)

        # 管理员专用信息扩展（基于现有字段计算）
        admin_info = await calculate_admin_info(resource_key)

        return {
            "status": "success",
            "data": {**detail.model_dump(), "admin_info": admin_info},
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取管理员资源详情失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取资源详情失败: {str(e)}",
        )


@router.delete(
    "/resources/{resource_id}",
    summary="删除单个资源",
    description="删除指定的资源及其相关反馈",
)
async def delete_admin_resource(
    resource_id: int, current_user: User = Depends(RequireResourceManage)
):
    """
    删除单个资源
    """
    try:
        resource = await PanResource.get_or_none(id=resource_id)
        if not resource:
            raise HTTPException(status_code=404, detail="资源不存在")

        resource_key = resource.resource_key
        resource_title = resource.title

        # 删除资源
        await resource.delete()

        # 删除相关反馈（级联删除）
        deleted_feedback_count = await ResourceInvalidFeedback.filter(
            resource_id=resource_key
        ).count()
        await ResourceInvalidFeedback.filter(resource_id=resource_key).delete()

        logger.info(
            f"管理员 {current_user.username} 删除了资源: {resource_title} (ID: {resource_id}), "
            f"同时删除了 {deleted_feedback_count} 条相关反馈"
        )

        return {
            "status": "success",
            "message": f"资源删除成功，同时删除了 {deleted_feedback_count} 条相关反馈",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资源失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"删除资源失败: {str(e)}",
        )


@router.post(
    "/resources/batch-delete",
    summary="批量删除资源",
    description="批量删除多个资源及其相关反馈",
)
async def batch_delete_resources(
    resource_ids: List[int], current_user: User = Depends(RequireResourceManage)
):
    """
    批量删除资源
    """
    try:
        if not resource_ids:
            raise HTTPException(status_code=400, detail="资源ID列表不能为空")

        if len(resource_ids) > 100:
            raise HTTPException(
                status_code=400,
                detail="单次最多只能删除100个资源",
            )

        # 获取要删除的资源
        resources = await PanResource.filter(id__in=resource_ids)
        resource_keys = [r.resource_key for r in resources]

        if not resources:
            raise HTTPException(status_code=404, detail="未找到要删除的资源")

        # 统计相关反馈数量
        total_feedback_count = await ResourceInvalidFeedback.filter(
            resource_id__in=resource_keys
        ).count()

        # 批量删除
        await PanResource.filter(id__in=resource_ids).delete()
        await ResourceInvalidFeedback.filter(resource_id__in=resource_keys).delete()

        logger.info(
            f"管理员 {current_user.username} 批量删除了 {len(resources)} 个资源，"
            f"同时删除了 {total_feedback_count} 条相关反馈"
        )

        return {
            "status": "success",
            "message": f"成功删除 {len(resources)} 个资源和 {total_feedback_count} 条相关反馈",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除资源失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"批量删除资源失败: {str(e)}",
        )


async def calculate_admin_stats() -> dict:
    """基于现有字段计算管理员统计"""
    # 按网盘类型统计
    by_pan_type = {}
    for pan_type in [1, 2, 3, 4]:
        count = await PanResource.filter(pan_type=pan_type).count()
        by_pan_type[f"pan_type_{pan_type}"] = count

    # 按文件类型统计
    file_type_stats = await PanResource.all().values_list("file_type", flat=True)
    by_file_type = {}
    for file_type in set(filter(None, file_type_stats)):
        count = await PanResource.filter(file_type=file_type).count()
        by_file_type[file_type] = count

    # 按验证状态统计
    by_status = {
        "valid": await PanResource.filter(verified_status="valid").count(),
        "invalid": await PanResource.filter(verified_status="invalid").count(),
        "unknown": await PanResource.filter(verified_status__isnull=True).count(),
    }

    # 按is_mine统计
    by_source = {
        "admin_uploaded": await PanResource.filter(is_mine=True).count(),
        "user_submitted": await PanResource.filter(is_mine=False).count(),
    }

    return {
        "by_pan_type": by_pan_type,
        "by_file_type": by_file_type,
        "by_status": by_status,
        "by_source": by_source,
    }
