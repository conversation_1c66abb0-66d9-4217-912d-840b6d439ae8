#!/bin/bash

# 创建第一个管理员用户的Shell脚本
# 用于生产环境快速初始化管理员账户

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "app/config.yaml" ]; then
        print_error "请在项目根目录运行此脚本"
        print_error "当前目录应包含 app/config.yaml 文件"
        exit 1
    fi
    
    if [ ! -f "create_admin.py" ]; then
        print_error "未找到 create_admin.py 脚本"
        print_error "请确保该文件存在于项目根目录"
        exit 1
    fi
}

# 检查Python环境
check_python_env() {
    print_info "检查Python环境..."
    
    # 检查虚拟环境
    if [ -d "venv" ]; then
        print_info "发现虚拟环境，激活中..."
        source venv/bin/activate
    else
        print_warning "未发现虚拟环境，使用系统Python"
    fi
    
    # 检查Python版本
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_info "Python版本: $python_version"
    
    # 检查必要的包
    if ! python3 -c "import tortoise" 2>/dev/null; then
        print_error "未安装 tortoise-orm，请先安装依赖:"
        print_error "pip install -r requirements.txt"
        exit 1
    fi
}

# 检查数据库连接
check_database() {
    print_info "检查数据库连接..."
    
    # 尝试连接数据库
    python3 -c "
import asyncio
from tortoise import Tortoise
from app.core.tortoise_config import TORTOISE_ORM

async def test_db():
    try:
        await Tortoise.init(config=TORTOISE_ORM)
        print('数据库连接成功')
        await Tortoise.close_connections()
        return True
    except Exception as e:
        print(f'数据库连接失败: {e}')
        return False

result = asyncio.run(test_db())
exit(0 if result else 1)
" 2>/dev/null

    if [ $? -eq 0 ]; then
        print_success "数据库连接正常"
    else
        print_error "数据库连接失败，请检查配置"
        print_error "请确保:"
        print_error "1. PostgreSQL服务正在运行"
        print_error "2. app/config.yaml 中的数据库配置正确"
        print_error "3. 数据库用户有足够的权限"
        exit 1
    fi
}

# 创建管理员用户
create_admin() {
    print_info "开始创建管理员用户..."
    print_info "用户名: admin"
    print_info "邮箱: <EMAIL>"
    print_info "密码: Wsk1998107..."
    
    echo ""
    read -p "确认创建管理员用户? (y/N): " confirm
    
    if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
        print_warning "操作已取消"
        exit 0
    fi
    
    # 执行Python脚本
    python3 create_admin.py
    
    if [ $? -eq 0 ]; then
        print_success "管理员用户创建完成！"
        echo ""
        echo "=========================================="
        echo "管理员登录信息:"
        echo "用户名: admin"
        echo "密码: Wsk1998107..."
        echo "邮箱: <EMAIL>"
        echo "=========================================="
        echo ""
        print_info "您现在可以使用这些信息登录管理后台"
    else
        print_error "管理员用户创建失败"
        exit 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "🚀 Pan-So 管理员用户创建工具"
    echo "=========================================="
    echo ""
    
    # 执行检查
    check_directory
    check_python_env
    check_database
    
    # 创建管理员
    create_admin
    
    print_success "脚本执行完成！"
}

# 执行主函数
main "$@"
