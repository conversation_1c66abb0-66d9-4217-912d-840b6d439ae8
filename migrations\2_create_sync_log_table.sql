-- File: migrations/2_create_sync_log_table.sql
-- Description: 创建Meilisearch同步日志表，替换实时触发器机制

-- 1. 创建同步日志表
CREATE TABLE IF NOT EXISTS meilisearch_sync_log (
    id SERIAL PRIMARY KEY,
    resource_key VARCHAR(255) NOT NULL,
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    created_at TIMESTAMP DEFAULT NOW(),
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT NULL
);

-- 2. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_sync_log_unprocessed 
ON meilisearch_sync_log (processed, created_at) 
WHERE processed = FALSE;

CREATE INDEX IF NOT EXISTS idx_sync_log_resource_key 
ON meilisearch_sync_log (resource_key, created_at DESC);

-- 3. 创建或替换触发器函数（记录到日志表而不是发送通知）
CREATE OR REPLACE FUNCTION log_resource_change() RETURNS TRIGGER AS $$
BEGIN
  -- 根据操作类型确定使用哪个记录
  IF (TG_OP = 'DELETE') THEN
    INSERT INTO meilisearch_sync_log (resource_key, operation)
    VALUES (OLD.resource_key, TG_OP);
    RETURN OLD;
  ELSE
    INSERT INTO meilisearch_sync_log (resource_key, operation)
    VALUES (NEW.resource_key, TG_OP);
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 4. 删除旧的实时通知触发器
DROP TRIGGER IF EXISTS pan_resources_notify_trigger ON pan_resources;

-- 5. 创建新的日志记录触发器（如果不存在）
DROP TRIGGER IF EXISTS pan_resources_log_trigger ON pan_resources;
CREATE TRIGGER pan_resources_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON pan_resources
FOR EACH ROW EXECUTE FUNCTION log_resource_change();

-- 6. 添加注释
COMMENT ON TABLE meilisearch_sync_log 
IS 'Meilisearch同步日志表，记录资源变更用于批量同步';

COMMENT ON TRIGGER pan_resources_log_trigger ON pan_resources
IS '记录资源变更到同步日志表，用于定时批量同步到Meilisearch';

-- 7. 创建清理旧日志的函数（保留7天）
CREATE OR REPLACE FUNCTION cleanup_old_sync_logs() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM meilisearch_sync_log 
    WHERE processed = TRUE 
    AND processed_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_old_sync_logs() 
IS '清理7天前已处理的同步日志记录';
