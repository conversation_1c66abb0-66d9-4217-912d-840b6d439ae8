"""
个人信息管理相关的Pydantic模型
"""

from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, List
from datetime import datetime
import re


class UserProfileDetailResponse(BaseModel):
    """用户详细资料响应模型"""

    id: int
    username: str
    email: str
    nickname: Optional[str]
    avatar: Optional[str]
    status: str
    email_verified: bool
    points: int = Field(default=0, description="用户积分")
    title: str = Field(default="资源拾荒者", description="用户头衔")
    last_login_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    # 个人信息管理相关字段
    last_nickname_change: Optional[datetime] = Field(
        None, description="上次昵称修改时间"
    )
    nickname_change_count: int = Field(0, description="昵称修改次数")
    can_change_nickname: bool = Field(True, description="是否可以修改昵称")
    next_nickname_change_date: Optional[datetime] = Field(
        None, description="下次可修改昵称时间"
    )

    class Config:
        from_attributes = True


class UserProfileUpdateRequest(BaseModel):
    """用户资料批量更新请求模型"""

    nickname: Optional[str] = Field(None, max_length=100, description="昵称")


class ChangeEmailRequest(BaseModel):
    """更改邮箱请求模型"""

    new_email: EmailStr = Field(..., description="新邮箱地址")
    password: str = Field(..., description="当前密码")


class VerifyEmailChangeRequest(BaseModel):
    """验证邮箱更改请求模型"""

    token: str = Field(..., description="验证令牌")


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""

    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")

    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if not re.search(r"[A-Z]", v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class ChangeNicknameRequest(BaseModel):
    """修改昵称请求模型"""

    nickname: str = Field(..., min_length=2, max_length=100, description="新昵称")
    reason: Optional[str] = Field(None, max_length=255, description="修改原因")

    @field_validator("nickname")
    @classmethod
    def validate_nickname(cls, v):
        """验证昵称格式"""
        # 基本格式检查
        if not re.match(r"^[\u4e00-\u9fa5a-zA-Z0-9_-]+$", v):
            raise ValueError("昵称只能包含中文、字母、数字、下划线和连字符")

        # 敏感词检查（这里只是示例，实际应该从数据库或配置文件读取）
        sensitive_words = ["admin", "管理员", "系统", "客服", "官方"]
        for word in sensitive_words:
            if word.lower() in v.lower():
                raise ValueError(f"昵称不能包含敏感词：{word}")

        return v


class AvatarUploadResponse(BaseModel):
    """头像上传响应模型"""

    avatar_id: int
    file_path: str
    cdn_url: Optional[str]
    file_size: int
    width: Optional[int]
    height: Optional[int]
    mime_type: str = Field(
        default="image/webp", description="文件MIME类型，统一为WebP格式"
    )
    storage_type: str = Field(default="r2", description="存储类型")

    class Config:
        json_schema_extra = {
            "example": {
                "avatar_id": 123,
                "file_path": "avatars/20250802/456/uuid.webp",
                "cdn_url": "https://pub-panso.r2.dev/avatars/20250802/456/uuid.webp",
                "file_size": 4540,
                "width": 400,
                "height": 400,
                "mime_type": "image/webp",
                "storage_type": "r2",
            }
        }


class UserPointsHistoryResponse(BaseModel):
    """用户积分历史响应模型"""

    total: int
    page: int
    size: int
    pages: int
    transactions: List[dict]


class UserHelpRequestsResponse(BaseModel):
    """用户求助列表响应模型"""

    total: int
    page: int
    size: int
    pages: int
    requests: List[dict]


class UserAnswersResponse(BaseModel):
    """用户回答列表响应模型"""

    total: int
    page: int
    size: int
    pages: int
    answers: List[dict]


class NicknameHistoryResponse(BaseModel):
    """昵称修改历史响应模型"""

    id: int
    old_nickname: Optional[str]
    new_nickname: str
    change_reason: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class AvatarHistoryResponse(BaseModel):
    """头像历史响应模型"""

    id: int
    original_filename: str
    file_path: str
    cdn_url: Optional[str]
    file_size: int
    width: Optional[int]
    height: Optional[int]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class EmailChangeHistoryResponse(BaseModel):
    """邮箱更改历史响应模型"""

    id: int
    old_email: str
    new_email: str
    is_verified: bool
    verified_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True
