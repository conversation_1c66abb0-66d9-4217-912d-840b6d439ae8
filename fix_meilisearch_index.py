#!/usr/bin/env python3
"""
修复 Meilisearch 索引配置
解决 "Attribute `pan_type` is not filterable" 错误
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.meilisearch_service import meilisearch_service

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def wait_for_task(task):
    """等待 Meilisearch 任务完成"""
    task_uid = task.get("taskUid")
    if not task_uid:
        logger.error("任务没有返回 taskUid")
        return False
    
    logger.info(f"等待任务完成: {task_uid}")
    
    # 轮询任务状态
    max_attempts = 60  # 最多等待60次，每次1秒
    for attempt in range(max_attempts):
        try:
            task_status = meilisearch_service.client.get_task(task_uid)
            status = task_status.get("status")
            
            if status == "succeeded":
                logger.info(f"任务 {task_uid} 完成成功")
                return True
            elif status == "failed":
                error = task_status.get("error", {})
                logger.error(f"任务 {task_uid} 失败: {error}")
                return False
            elif status in ["enqueued", "processing"]:
                logger.info(f"任务 {task_uid} 状态: {status}, 继续等待...")
                await asyncio.sleep(1)
            else:
                logger.warning(f"任务 {task_uid} 未知状态: {status}")
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"检查任务状态时出错: {e}")
            await asyncio.sleep(1)
    
    logger.error(f"任务 {task_uid} 等待超时")
    return False


async def check_current_settings():
    """检查当前索引设置"""
    logger.info("检查当前 Meilisearch 索引设置...")
    
    try:
        settings = meilisearch_service.index.get_settings()
        filterable = settings.get('filterableAttributes', [])
        sortable = settings.get('sortableAttributes', [])
        
        logger.info(f"当前可过滤属性: {filterable}")
        logger.info(f"当前可排序属性: {sortable}")
        
        # 检查必要的字段是否存在
        required_filterable = ['pan_type', 'file_type', 'verified_status', 'author', 'updated_at']
        missing_filterable = [field for field in required_filterable if field not in filterable]
        
        if missing_filterable:
            logger.warning(f"缺少可过滤属性: {missing_filterable}")
            return False
        else:
            logger.info("✅ 所有必要的可过滤属性都已配置")
            return True
            
    except Exception as e:
        logger.error(f"检查设置时出错: {e}")
        return False


async def update_index_settings():
    """更新索引设置"""
    logger.info("开始更新 Meilisearch 索引设置...")
    
    try:
        # 配置可过滤和可排序属性
        filterable_attributes = [
            "pan_type",
            "file_type",
            "verified_status",
            "author",
            "updated_at",
        ]
        
        sortable_attributes = [
            "access_count",
            "created_at", 
            "updated_at",
            "title"
        ]
        
        logger.info(f"设置可过滤属性: {filterable_attributes}")
        logger.info(f"设置可排序属性: {sortable_attributes}")
        
        # 更新设置
        task = meilisearch_service.index.update_settings({
            "filterableAttributes": filterable_attributes,
            "sortableAttributes": sortable_attributes,
        })
        
        # 等待任务完成
        success = await wait_for_task(task)
        
        if success:
            logger.info("✅ 索引设置更新成功！")
            return True
        else:
            logger.error("❌ 索引设置更新失败")
            return False
            
    except Exception as e:
        logger.error(f"更新索引设置时出错: {e}")
        return False


async def test_search():
    """测试搜索功能"""
    logger.info("测试搜索功能...")
    
    try:
        # 测试基本搜索
        result = meilisearch_service.index.search("test", {
            "limit": 1,
            "filter": "pan_type = 1"
        })
        
        logger.info("✅ 搜索测试成功，pan_type 过滤器工作正常")
        return True
        
    except Exception as e:
        logger.error(f"搜索测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("Meilisearch 索引配置修复脚本")
    logger.info("=" * 50)
    
    try:
        # 1. 检查当前设置
        current_ok = await check_current_settings()
        
        # 2. 如果设置不正确，更新设置
        if not current_ok:
            logger.info("需要更新索引设置...")
            settings_ok = await update_index_settings()
            
            if not settings_ok:
                logger.error("索引设置更新失败，退出")
                return 1
            
            # 重新检查设置
            logger.info("重新检查索引设置...")
            await asyncio.sleep(2)  # 等待设置生效
            updated_ok = await check_current_settings()
            
            if not updated_ok:
                logger.error("设置更新后仍然不正确")
                return 1
        else:
            logger.info("索引设置已经正确，跳过更新")
        
        # 3. 测试搜索功能
        test_ok = await test_search()
        
        if test_ok:
            logger.info("🎉 修复完成！现在可以正常使用搜索功能了")
            return 0
        else:
            logger.error("搜索测试失败，可能需要重建索引")
            return 1
            
    except Exception as e:
        logger.error(f"修复过程中出错: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("用户中断修复")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)
