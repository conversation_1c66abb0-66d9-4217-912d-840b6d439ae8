#!/usr/bin/env python3
"""
检查 Meilisearch 当前状态和同步进度的脚本
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime

# Meilisearch 配置
MEILI_HOST = "http://127.0.0.1:7700"
MEILI_API_KEY = "M3lPuLTZeJeA7urKBe6YN7nAPJxBZxA7Lr7kamVXW_k"
INDEX_NAME = "resources"

async def check_meilisearch_status():
    """检查 Meilisearch 状态"""
    print("=" * 60)
    print("🔍 Meilisearch 状态检查")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 检查服务健康状态
            print("\n📊 服务状态:")
            async with session.get(f"{MEILI_HOST}/health") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"   状态: ✅ {health.get('status', 'unknown')}")
                else:
                    print(f"   状态: ❌ HTTP {response.status}")
                    return
            
            # 2. 检查版本信息
            async with session.get(f"{MEILI_HOST}/version") as response:
                if response.status == 200:
                    version = await response.json()
                    print(f"   版本: {version.get('pkgVersion', 'unknown')}")
            
            # 3. 检查索引状态
            print(f"\n📚 索引状态 ({INDEX_NAME}):")
            headers = {"Authorization": f"Bearer {MEILI_API_KEY}"}
            
            async with session.get(f"{MEILI_HOST}/indexes/{INDEX_NAME}", headers=headers) as response:
                if response.status == 200:
                    index_info = await response.json()
                    print(f"   文档数量: {index_info.get('numberOfDocuments', 0):,}")
                    print(f"   主键: {index_info.get('primaryKey', 'unknown')}")
                    print(f"   创建时间: {index_info.get('createdAt', 'unknown')}")
                    print(f"   更新时间: {index_info.get('updatedAt', 'unknown')}")
                else:
                    print(f"   ❌ 无法获取索引信息: HTTP {response.status}")
                    return
            
            # 4. 检查正在进行的任务
            print(f"\n⚙️ 正在进行的任务:")
            async with session.get(f"{MEILI_HOST}/tasks?status=processing,enqueued", headers=headers) as response:
                if response.status == 200:
                    tasks_data = await response.json()
                    tasks = tasks_data.get('results', [])
                    
                    if tasks:
                        print(f"   发现 {len(tasks)} 个活跃任务:")
                        for task in tasks[:10]:  # 只显示前10个
                            task_type = task.get('type', 'unknown')
                            status = task.get('status', 'unknown')
                            uid = task.get('uid', 'unknown')
                            enqueued_at = task.get('enqueuedAt', 'unknown')
                            
                            print(f"     - 任务 {uid}: {task_type} ({status})")
                            print(f"       入队时间: {enqueued_at}")
                            
                            # 如果是文档相关任务，显示详细信息
                            if 'details' in task:
                                details = task['details']
                                if 'receivedDocuments' in details:
                                    print(f"       文档数量: {details['receivedDocuments']}")
                                if 'indexedDocuments' in details:
                                    print(f"       已索引: {details['indexedDocuments']}")
                    else:
                        print("   ✅ 没有正在进行的任务")
                else:
                    print(f"   ❌ 无法获取任务信息: HTTP {response.status}")
            
            # 5. 检查最近完成的任务
            print(f"\n📋 最近完成的任务:")
            async with session.get(f"{MEILI_HOST}/tasks?status=succeeded,failed&limit=5", headers=headers) as response:
                if response.status == 200:
                    tasks_data = await response.json()
                    tasks = tasks_data.get('results', [])
                    
                    if tasks:
                        for task in tasks:
                            task_type = task.get('type', 'unknown')
                            status = task.get('status', 'unknown')
                            uid = task.get('uid', 'unknown')
                            finished_at = task.get('finishedAt', 'unknown')
                            
                            status_icon = "✅" if status == "succeeded" else "❌"
                            print(f"   {status_icon} 任务 {uid}: {task_type} ({status})")
                            print(f"     完成时间: {finished_at}")
                            
                            if status == "failed" and 'error' in task:
                                error = task['error']
                                print(f"     错误: {error.get('message', 'unknown')}")
                    else:
                        print("   没有最近完成的任务")
            
            # 6. 检查索引设置
            print(f"\n⚙️ 索引设置:")
            async with session.get(f"{MEILI_HOST}/indexes/{INDEX_NAME}/settings", headers=headers) as response:
                if response.status == 200:
                    settings = await response.json()
                    
                    filterable = settings.get('filterableAttributes', [])
                    sortable = settings.get('sortableAttributes', [])
                    searchable = settings.get('searchableAttributes', [])
                    
                    print(f"   可过滤属性: {len(filterable)} 个")
                    print(f"   可排序属性: {len(sortable)} 个")
                    print(f"   可搜索属性: {len(searchable)} 个")
                    
                    if filterable:
                        print(f"     过滤属性: {', '.join(filterable[:5])}{'...' if len(filterable) > 5 else ''}")
            
            # 7. 检查统计信息
            print(f"\n📈 统计信息:")
            async with session.get(f"{MEILI_HOST}/stats", headers=headers) as response:
                if response.status == 200:
                    stats = await response.json()
                    
                    database_size = stats.get('databaseSize', 0)
                    last_update = stats.get('lastUpdate', 'unknown')
                    
                    # 转换字节为可读格式
                    if database_size > 0:
                        if database_size > 1024**3:  # GB
                            size_str = f"{database_size / (1024**3):.2f} GB"
                        elif database_size > 1024**2:  # MB
                            size_str = f"{database_size / (1024**2):.2f} MB"
                        else:
                            size_str = f"{database_size / 1024:.2f} KB"
                    else:
                        size_str = "0 B"
                    
                    print(f"   数据库大小: {size_str}")
                    print(f"   最后更新: {last_update}")
                    
                    # 索引统计
                    indexes = stats.get('indexes', {})
                    if INDEX_NAME in indexes:
                        index_stats = indexes[INDEX_NAME]
                        print(f"   索引文档数: {index_stats.get('numberOfDocuments', 0):,}")
                        print(f"   索引中的字段数: {index_stats.get('fieldDistribution', {})}")
        
        except Exception as e:
            print(f"❌ 检查过程中出错: {e}")
            return False
    
    print("\n" + "=" * 60)
    print(f"检查完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    return True

if __name__ == "__main__":
    try:
        asyncio.run(check_meilisearch_status())
    except KeyboardInterrupt:
        print("\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"脚本执行失败: {e}")
        sys.exit(1)
