import logging
from contextlib import asynccontextmanager
from tortoise import Tortoise
from tortoise.transactions import in_transaction
import yaml
from app.core.tortoise_config import TORTOISE_ORM as CORE_TORTOISE_ORM

logger = logging.getLogger(__name__)

# 定义配置文件的路径
CONFIG_PATH = "app/config.yaml"


def load_tortoise_config():
    """
    从 YAML 文件加载并返回 Tortoise ORM 的配置字典。
    """
    try:
        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)

        # 构建正确的 Tortoise ORM 配置格式
        database_config = config.get("database", {})

        # 构建 Tortoise ORM 配置
        tortoise_config = {
            "connections": {"default": database_config.get("url", "")},
            "apps": database_config.get("apps", {}),
        }

        logger.info(f"从YAML加载数据库配置成功: {tortoise_config}")
        return tortoise_config

    except FileNotFoundError:
        logger.warning(f"数据库配置文件未找到: {CONFIG_PATH}，使用核心配置")
        return CORE_TORTOISE_ORM
    except Exception as e:
        logger.error(f"加载数据库配置时出错: {e}，使用核心配置")
        return CORE_TORTOISE_ORM


TORTOISE_ORM = load_tortoise_config()

# --- For FastAPI App Lifespan ---


async def init_db():
    """
    为FastAPI应用程序的生命周期初始化数据库连接。
    在应用启动时调用。
    """
    logger.info("FastAPI 应用正在连接到数据库...")
    try:
        await Tortoise.init(config=TORTOISE_ORM)
        # 使用安全模式生成数据库模式，避免与现有表冲突
        await Tortoise.generate_schemas(safe=True)
        logger.info("数据库连接成功并生成模式。")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}", exc_info=True)
        raise


async def close_db():
    """
    为FastAPI应用程序的生命周期关闭数据库连接。
    在应用关闭时调用。
    """
    logger.info("正在关闭数据库连接...")
    await Tortoise.close_connections()
    logger.info("数据库连接已关闭。")


# --- For Celery Worker Tasks ---


@asynccontextmanager
async def db_transaction():
    """
    为Celery任务提供一个数据库事务的异步上下文管理器。

    针对隔离线程环境优化：
    1. 每次都重新初始化连接，确保线程安全
    2. 完整的连接生命周期管理
    3. 强制关闭连接，避免跨线程问题

    用法:
    async with db_transaction():
        # 在这里执行所有数据库操作
        await MyModel.create(...)
    """
    connection_initialized = False

    try:
        # 1. 强制初始化新的数据库连接（适用于隔离线程）
        try:
            logger.debug("正在为隔离线程初始化数据库连接...")
            await Tortoise.init(config=TORTOISE_ORM)
            connection_initialized = True
            logger.debug("数据库连接初始化成功")

        except Exception as e:
            if "Event loop is closed" in str(e):
                logger.error("无法初始化数据库连接：事件循环已关闭")
                raise RuntimeError("数据库连接初始化失败：事件循环已关闭") from e
            else:
                logger.error(f"数据库连接初始化失败: {e}", exc_info=True)
                raise

        # 2. 开始事务
        async with in_transaction() as connection:
            logger.debug("数据库事务已开始")
            yield connection  # 将连接对象暴露给 'with' 语句块

        logger.debug("数据库事务提交成功")

    except Exception as e:
        # 如果发生异常，事务会自动回滚
        logger.error(f"数据库事务失败并已回滚: {e}", exc_info=True)
        raise  # 重新抛出异常，以便上层代码可以捕获它
    finally:
        # 3. 强制关闭连接（适用于隔离线程）
        if connection_initialized:
            try:
                await Tortoise.close_connections()
                logger.debug("数据库连接已关闭")
            except RuntimeError as e:
                if "Event loop is closed" in str(e):
                    logger.warning("事件循环已关闭，无法正常关闭数据库连接")
                else:
                    logger.error(f"关闭数据库连接时发生运行时错误: {e}")
            except Exception as e:
                logger.error(f"关闭数据库连接时发生未知错误: {e}", exc_info=True)
