"""
SEO相关工具函数，用于为资源页面生成元数据。
"""

from typing import Dict
import jieba
import re
from collections import Counter
from app.models.resource import PanResource
from app.models.enums import PanType


def _get_pan_type_name(pan_type: int) -> str:
    """
    内部辅助函数，将网盘类型的枚举值转换为用户可读的字符串名称。

    Args:
        pan_type: 网盘类型的整数值。

    Returns:
        对应的网盘名称字符串，如"百度网盘"。
    """
    # 使用 .get() 方法避免因未定义类型而引发KeyError
    pan_map = {
        PanType.BAIDU.value: "百度网盘",
        PanType.QUARK.value: "夸克网盘",
        PanType.ALIYUN.value: "阿里云盘",
        PanType.XUNLEI.value: "迅雷网盘",
    }
    return pan_map.get(pan_type, "")


def generate_seo_metadata(
    resource: PanResource, site_name: str = "97盘搜"
) -> Dict[str, str]:
    """
    为给定的资源对象生成SEO优化的标题(title)、描述(description)和关键词(keywords)。

    优化点：
    1. 解决标题重复问题：智能组合不同元素，严格控制长度
    2. 解决描述重复问题：多样化模板系统，基于资源ID选择不同模板
    3. 添加关键词生成：利用jieba分词提取有意义的关键词
    4. 提高唯一性：使用resource_key而非ID作为唯一标识
    """
    title = resource.title or "未知资源"
    pan_type_name = _get_pan_type_name(resource.pan_type)

    # 1. 智能标题生成（50-60字符优化）
    seo_title = _generate_unique_title(resource, pan_type_name, site_name)

    # 2. 差异化描述生成（150-160字符）
    seo_description = _generate_unique_description(resource, pan_type_name)

    # 3. 关键词生成（利用jieba分词逻辑）
    keywords = _generate_seo_keywords(resource)

    return {
        "seo_title": seo_title,
        "seo_description": seo_description,
        "seo_keywords": keywords,
    }


def _generate_unique_title(
    resource: PanResource, pan_type_name: str, site_name: str
) -> str:
    """生成高度差异化的唯一标题，避免重复"""
    title = resource.title or "未知资源"

    # 基于资源ID的差异化策略
    variation_id = resource.id % 8  # 8种不同的标题风格

    # 智能意图词选择（更多样化）
    intent_variations = {
        "video": ["在线观看", "高清下载", "免费播放", "资源获取"],
        "audio": ["免费听", "音频下载", "在线播放", "音乐获取"],
        "document": ["阅读", "文档下载", "学习资料", "电子书"],
        "archive": ["下载", "软件获取", "资源包", "安装文件"],
        "application": ["安装", "软件下载", "应用获取", "程序文件"],
    }

    intent_options = intent_variations.get(
        resource.file_type, ["下载", "获取", "资源", "分享"]
    )
    intent_word = intent_options[variation_id % len(intent_options)]

    # 差异化修饰词
    quality_modifiers = ["", "高清", "完整版", "精品", "热门", "最新", "经典", "优质"]
    source_modifiers = [
        "",
        "官方",
        "原版",
        "正版",
        "无损",
        "高品质",
        "珍藏版",
        "特别版",
    ]

    quality_mod = quality_modifiers[variation_id % len(quality_modifiers)]
    source_mod = source_modifiers[(variation_id + resource.id) % len(source_modifiers)]

    # 8种不同的标题结构（强化网盘类型元素，去除时间元素）
    if variation_id == 0:
        # 标准格式：标题 + 修饰词 + 网盘类型 + 意图
        parts = [title[:22]]
        if quality_mod:
            parts.append(quality_mod)
        parts.append(f"{pan_type_name}{intent_word}")
    elif variation_id == 1:
        # 强调文件大小：标题 + 大小 + 网盘类型 + 意图
        parts = [title[:20]]
        if resource.file_size and resource.file_size != "未知大小":
            size_clean = resource.file_size.replace("文件大小", "").strip()[:8]
            parts.append(size_clean)
        parts.append(f"{pan_type_name}{intent_word}")
    elif variation_id == 2:
        # 强调作者：标题 + 作者版 + 网盘平台
        parts = [title[:20]]
        if resource.author:
            parts.append(f"{resource.author[:8]}版")
        else:
            parts.append(f"{source_mod}版" if source_mod else "精选版")
        parts.append(f"{pan_type_name}平台")
    elif variation_id == 3:
        # 强调平台：网盘类型 + 标题 + 专属 + 意图
        parts = [f"{pan_type_name}专属"]
        parts.append(title[:20])
        parts.append(intent_word)
    elif variation_id == 4:
        # 强调质量：修饰词 + 标题 + 网盘 + 下载
        if quality_mod:
            parts = [f"{quality_mod}{title[:18]}"]
        else:
            parts = [title[:22]]
        parts.append(f"{pan_type_name}下载")
    elif variation_id == 5:
        # 网盘优先：标题 + 网盘类型 + 哪里 + 意图
        parts = [title[:20]]
        parts.append(f"{pan_type_name}哪里{intent_word}")
    elif variation_id == 6:
        # 双重网盘：网盘 + 标题 + 网盘类型
        parts = [f"{pan_type_name}"]
        parts.append(title[:20])
        parts.append("网盘资源")
    else:  # variation_id == 7
        # 简洁格式：标题 + 网盘类型 + 直接下载
        parts = [title[:22]]
        parts.append(f"{pan_type_name}直接下载")

    # 组装标题
    title_text = " ".join([p for p in parts if p])
    final_title = f"{title_text} - {site_name}"

    # 长度控制
    if len(final_title) > 60:
        # 计算需要截断的长度
        excess = len(final_title) - 57  # 留3个字符给"..."
        if len(title_text) > excess:
            title_text = title_text[:-excess] + "..."
            final_title = f"{title_text} - {site_name}"

    return final_title


def _generate_unique_description(resource: PanResource, pan_type_name: str) -> str:
    """生成高质量、唯一性描述，解决必应站长工具的重复内容问题"""
    title = resource.title or "未知资源"

    # 基于资源ID的高度差异化策略（12种完全不同的风格）
    variation_id = resource.id % 12

    # 差异化开头词
    openings = [
        "立即获取",
        "免费下载",
        "在线获取",
        "高速下载",
        "直接获取",
        "快速下载",
        "一键下载",
        "轻松获取",
        "便捷下载",
        "安全获取",
        "稳定下载",
        "优质获取",
    ]

    # 差异化连接词
    connectors = [
        "，这里有",
        "，为您提供",
        "，包含",
        "，收录",
        "，汇集",
        "，整理",
        "，精选",
        "，推荐",
        "，分享",
        "，提供",
        "，收集",
        "，准备",
    ]

    # 差异化结尾词
    endings = [
        "欢迎下载体验",
        "点击即可获取",
        "支持在线预览",
        "多格式可选",
        "更新及时稳定",
        "资源真实有效",
        "下载速度极快",
        "完全免费使用",
        "无需注册登录",
        "支持断点续传",
        "提供技术支持",
        "定期更新维护",
    ]

    opening = openings[variation_id % len(openings)]
    connector = connectors[variation_id % len(connectors)]
    ending = endings[variation_id % len(endings)]

    # 增强的内容预览（充分利用text_content）
    content_preview = ""
    detailed_preview = ""

    if resource.text_content:
        # 根据variation_id选择不同的预览策略，增加更多样化
        if variation_id % 4 == 0:
            # 前部分预览 + 关键信息提取
            preview_text = resource.text_content[:25].strip()
            # 提取数字、年份等关键信息
            numbers = re.findall(r"\d{4}|\d+[GMKB]+|\d+\.\d+", resource.text_content)
            if numbers:
                preview_text += f"，含{numbers[0]}"
        elif variation_id % 4 == 1:
            # 中间部分预览 + 特色词汇
            mid_start = len(resource.text_content) // 3
            preview_text = resource.text_content[mid_start : mid_start + 25].strip()
            # 查找特色词汇
            special_words = re.findall(
                r"[高清|蓝光|4K|HD|无损|完整|正版|官方|中文|英文|双语]",
                resource.text_content,
            )
            if special_words:
                preview_text += f"，{special_words[0]}版本"
        elif variation_id % 4 == 2:
            # 关键词提取预览 + 内容分析
            words = jieba.lcut(resource.text_content)
            key_words = [
                w
                for w in words
                if len(w) >= 2
                and w
                not in {
                    "的",
                    "了",
                    "和",
                    "与",
                    "或",
                    "等",
                    "是",
                    "在",
                    "以",
                    "及",
                    "为",
                    "有",
                }
            ][:4]
            preview_text = "、".join(key_words)
            # 添加内容长度信息
            if len(resource.text_content) > 100:
                preview_text += "，详细介绍"
        else:
            # 尾部预览 + 总结性信息
            tail_start = max(0, len(resource.text_content) - 30)
            preview_text = resource.text_content[tail_start:].strip()
            # 添加内容丰富度描述
            word_count = len(jieba.lcut(resource.text_content))
            if word_count > 50:
                preview_text += f"，{word_count}字详述"

        if preview_text:
            content_preview = f"{preview_text}。"

        # 生成详细预览（用于某些描述模板）
        if len(resource.text_content) > 50:
            # 提取前50字符作为详细预览
            detailed_preview = resource.text_content[:50].strip()
            if not detailed_preview.endswith("。"):
                detailed_preview += "..."
            detailed_preview = f"内容简介：{detailed_preview} "

    # 高度差异化的文件类型描述
    type_descriptions = {
        "video": [
            "超清画质影视",
            "高分辨率视频",
            "蓝光品质影片",
            "4K超高清视频",
            "HDR影视资源",
            "杜比音效电影",
            "IMAX版本影片",
            "导演剪辑版",
        ],
        "audio": [
            "无损音质音频",
            "Hi-Fi品质音乐",
            "立体声音频",
            "环绕声资源",
            "原声带音乐",
            "现场录音版本",
            "数字母带音质",
            "专业混音版",
        ],
        "document": [
            "高清扫描文档",
            "可编辑文本",
            "完整版资料",
            "精装版电子书",
            "互动式文档",
            "多媒体教材",
            "专业学术资料",
            "实用工具书",
        ],
        "archive": [
            "完整安装包",
            "绿色免安装版",
            "便携式软件",
            "多语言版本",
            "专业工具集",
            "开发者版本",
            "企业级软件",
            "定制化程序",
        ],
        "application": [
            "最新版应用",
            "破解完整版",
            "专业版软件",
            "企业级应用",
            "多平台兼容",
            "云端同步版",
            "离线可用版",
            "增强功能版",
        ],
    }

    type_desc_list = type_descriptions.get(
        resource.file_type, ["优质资源", "精品内容", "热门资源", "实用工具"]
    )
    type_desc = type_desc_list[variation_id % len(type_desc_list)]

    # 添加精确到分秒的分享时间信息
    share_time_info = ""
    if resource.created_at:
        if variation_id % 6 == 0:
            share_time_info = (
                f"于{resource.created_at.strftime('%Y年%m月%d日%H时%M分')}分享，"
            )
        elif variation_id % 6 == 1:
            share_time_info = (
                f"{resource.created_at.strftime('%m月%d日%H:%M')}最新上传，"
            )
        elif variation_id % 6 == 2:
            share_time_info = (
                f"更新时间{resource.created_at.strftime('%m-%d %H:%M:%S')}，"
            )
        elif variation_id % 6 == 3:
            share_time_info = (
                f"发布于{resource.created_at.strftime('%Y-%m-%d %H:%M')}，"
            )
        elif variation_id % 6 == 4:
            share_time_info = f"{resource.created_at.strftime('%m/%d %H:%M:%S')}上线，"
        else:
            share_time_info = f"时间戳{resource.created_at.strftime('%Y%m%d%H%M%S')}，"

    # 12种完全不同的描述结构（增强text_content利用）
    if variation_id == 0:
        description = f"{opening}《{title[:18]}》{type_desc}{connector}{detailed_preview}{content_preview}{share_time_info}文件大小{resource.file_size or '适中'}，{pan_type_name}直链分享。{ending}！"
    elif variation_id == 1:
        description = f"寻找《{title[:16]}》资源？{connector}{type_desc}完整版本，{detailed_preview}{content_preview}{share_time_info}{pan_type_name}高速通道，{resource.access_count or 0}+用户已下载。{ending}。"
    elif variation_id == 2:
        description = f"《{title[:18]}》{type_desc}现已上线！{content_preview}{detailed_preview}{share_time_info}支持{pan_type_name}秒传，{resource.file_size or '大小合适'}，{ending}。"
    elif variation_id == 3:
        description = f"推荐下载：{type_desc}《{title[:16]}》，{content_preview}{detailed_preview}{share_time_info}来源{pan_type_name}官方分享，{ending}，值得收藏！"
    elif variation_id == 4:
        description = f"热门资源《{title[:17]}》{type_desc}版本{connector}{detailed_preview}{content_preview}{share_time_info}通过{pan_type_name}平台分享，{ending}。"
    elif variation_id == 5:
        description = f"《{title[:18]}》完整{type_desc}资源包，{content_preview}{detailed_preview}{share_time_info}{pan_type_name}云盘直链，无需等待，{ending}！"
    elif variation_id == 6:
        description = f"精品推荐：{type_desc}《{title[:16]}》{connector}{detailed_preview}{content_preview}{share_time_info}文件完整性已验证，{pan_type_name}高速下载。{ending}。"
    elif variation_id == 7:
        description = f"《{title[:18]}》{type_desc}资源分享中{connector}{content_preview}{detailed_preview}{share_time_info}{pan_type_name}网盘存储，{ending}，机会难得！"
    elif variation_id == 8:
        description = f"独家分享《{title[:16]}》{type_desc}完整版，{detailed_preview}{content_preview}{share_time_info}支持{pan_type_name}批量下载，{ending}。"
    elif variation_id == 9:
        description = f"《{title[:18]}》{type_desc}现货供应{connector}{content_preview}{detailed_preview}{share_time_info}{pan_type_name}官方链接，{ending}，限时分享！"
    elif variation_id == 10:
        description = f"优质资源《{title[:16]}》{type_desc}版本{connector}{detailed_preview}{content_preview}{share_time_info}经{pan_type_name}安全检测，{ending}。"
    else:  # variation_id == 11
        description = f"《{title[:18]}》{type_desc}资源库{connector}{content_preview}{detailed_preview}{share_time_info}{pan_type_name}永久链接，{ending}，欢迎分享！"

    # 添加唯一标识和作者信息
    if resource.author:
        description += f" 分享者：{resource.author}。"

    # 添加resource_key唯一标识
    description += f" 资源编号：{resource.resource_key[-6:]}"

    # 强化长度控制，确保描述在150-160字符之间
    # 第一轮扩展：添加详细功能描述
    if len(description) < 100:
        expansion_templates = [
            "该资源经过严格筛选和质量检测，确保内容完整无损，支持多种设备和播放器兼容。",
            "资源来源权威可靠，下载速度极快，采用CDN加速技术，无需担心链接失效问题。",
            "提供多种清晰度和格式选择，兼容性强，适合不同设备和用户的个性化需求。",
            "资源质量上乘，内容丰富完整，经过专业团队精心整理，是同类资源中的精品之选。",
            "支持在线预览和批量下载功能，下载前可先查看内容目录，确保资源准确性和完整性。",
            "资源更新及时，长期维护，专业团队24小时监控，为用户提供持续稳定的优质服务。",
            "采用先进压缩和编码技术，在保证原始质量的同时有效减小文件体积，节省存储空间。",
            "资源分类详细，标签完整，搜索功能强大，方便用户快速找到所需的特定内容。",
            "提供详细使用说明和安装教程，即使是新手用户也能轻松上手，快速掌握操作方法。",
            "资源安全无毒，经过多重安全检测和病毒扫描，用户可完全放心下载和使用。",
            "支持断点续传和多线程下载，网络不稳定时也能顺利完成下载任务，提升用户体验。",
            "资源库持续更新，定期添加最新热门内容，满足用户多样化和个性化的资源需求。",
        ]
        expansion = expansion_templates[variation_id % len(expansion_templates)]
        description += f" {expansion}"

    # 第二轮扩展：如果长度仍不足150字符，继续添加SEO内容
    if len(description) < 150:
        seo_extensions = [
            "平台提供7×24小时技术支持，专业客服团队随时为用户解答疑问。",
            "所有资源均经过人工审核，确保内容健康合规，为用户营造安全的下载环境。",
            "支持多种支付方式，会员享受更多特权，包括高速通道和独家资源访问权限。",
            "建立完善的用户反馈机制，持续优化服务质量，不断提升用户满意度。",
            "采用分布式存储技术，多重备份保障，确保资源永久有效，避免数据丢失。",
            "提供移动端APP和网页版双重访问方式，随时随地享受便捷的资源获取服务。",
            "建立严格的版权保护机制，尊重知识产权，为用户提供合法合规的资源分享平台。",
            "定期举办用户活动，提供免费资源和优惠福利，回馈广大用户的支持和信任。",
            "拥有专业的内容审核团队，确保资源质量和安全性，为用户筛选最优质的内容。",
            "支持用户自定义分类和收藏功能，打造个性化的资源管理和使用体验。",
            "提供详细的下载统计和使用分析，帮助用户更好地管理和利用下载的资源。",
            "建立用户社区和交流平台，促进用户间的经验分享和互助，共同构建和谐生态。",
        ]
        extension = seo_extensions[variation_id % len(seo_extensions)]
        if len(description) + len(extension) + 1 <= 160:
            description += f" {extension}"

    # 最终长度控制：确保在150-160字符之间
    if len(description) > 160:
        description = description[:157] + "..."
    elif len(description) < 150:
        # 如果还是不够150字符，添加简短的补充内容
        short_supplements = ["欢迎体验", "值得信赖", "用户首选", "品质保证", "服务优质"]
        supplement = short_supplements[variation_id % len(short_supplements)]
        if len(description) + len(supplement) + 1 <= 160:
            description += f" {supplement}"

    return description


def _generate_seo_keywords(resource: PanResource) -> str:
    """生成完整的SEO关键词，包含标题、分词后的标题、网盘类型等"""
    keywords = []  # 使用列表保持顺序
    keywords_set = set()  # 用于去重

    # 停用词列表
    stopwords = {
        "的",
        "了",
        "和",
        "与",
        "或",
        "等",
        "是",
        "在",
        "以",
        "及",
        "为",
        "有",
        "从",
        "到",
        "将",
        "被",
        "把",
        "对",
        "向",
        "给",
        "这",
        "那",
        "一个",
        "一些",
    }

    # 1. 完整标题作为关键词（最重要）
    if resource.title:
        title_clean = resource.title.strip()
        if title_clean and title_clean not in keywords_set:
            keywords.append(title_clean)
            keywords_set.add(title_clean)

    # 2. 分词后的标题关键词（按重要性排序）
    if resource.title:
        title_words = jieba.lcut(resource.title)
        # 过滤有意义的词汇，按长度和重要性排序
        meaningful_words = [
            w for w in title_words if len(w) >= 2 and w not in stopwords and w.strip()
        ]
        # 按词汇长度排序，长词汇优先
        meaningful_words.sort(key=len, reverse=True)

        for word in meaningful_words[:6]:  # 取前6个重要词汇
            if word not in keywords_set:
                keywords.append(word)
                keywords_set.add(word)

    # 3. 网盘类型关键词（必须包含）
    pan_type_name = _get_pan_type_name(resource.pan_type)
    if pan_type_name and pan_type_name not in keywords_set:
        keywords.append(pan_type_name)
        keywords_set.add(pan_type_name)

    # 添加网盘相关词汇
    pan_related_words = ["网盘", "云盘", "网盘下载", "云存储"]
    for word in pan_related_words:
        if word not in keywords_set and len(keywords) < 15:
            keywords.append(word)
            keywords_set.add(word)
            break

    # 4. 文件类型关键词（高优先级）
    type_keywords = {
        "video": ["视频", "电影", "电视剧", "高清", "在线观看", "影视"],
        "audio": ["音频", "音乐", "有声书", "MP3", "音乐下载", "声音"],
        "document": ["文档", "PDF", "电子书", "资料", "学习", "书籍"],
        "archive": ["软件", "游戏", "压缩包", "安装包", "工具", "程序"],
        "application": ["软件", "应用", "程序", "工具", "APP", "应用程序"],
    }

    if resource.file_type in type_keywords:
        for word in type_keywords[resource.file_type][:4]:
            if word not in keywords_set and len(keywords) < 15:
                keywords.append(word)
                keywords_set.add(word)

    # 5. 作者关键词（如果有）
    if resource.author and resource.author not in keywords_set:
        keywords.append(resource.author)
        keywords_set.add(resource.author)

    # 6. 从内容提取关键词
    if resource.text_content:
        content_words = jieba.lcut(resource.text_content[:150])  # 分析前150字符
        meaningful_content = [
            w for w in content_words if len(w) >= 2 and w not in stopwords and w.strip()
        ]
        # 按词频和长度排序
        word_freq = Counter(meaningful_content)
        sorted_content_words = sorted(
            word_freq.items(), key=lambda x: (x[1], len(x[0])), reverse=True
        )

        for word, _ in sorted_content_words[:3]:
            if word not in keywords_set and len(keywords) < 15:
                keywords.append(word)
                keywords_set.add(word)

    # 7. 通用资源关键词（补充）
    general_keywords = ["下载", "免费", "资源", "分享", "获取", "在线"]
    for word in general_keywords:
        if word not in keywords_set and len(keywords) < 15:
            keywords.append(word)
            keywords_set.add(word)

    # 8. 文件大小相关关键词
    if resource.file_size and resource.file_size != "未知大小":
        size_keywords = ["高清", "完整版", "无损", "原版"]
        for word in size_keywords:
            if word not in keywords_set and len(keywords) < 15:
                keywords.append(word)
                keywords_set.add(word)
                break

    # 限制关键词数量并返回（保持重要性顺序）
    final_keywords = keywords[:15]  # 最多15个关键词
    return ",".join(final_keywords)
