"""
资源求助相关的Pydantic模型
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

from app.models.help_request import CloudDiskType, ResourceType, HelpRequestStatus


class HelpRequestCreateRequest(BaseModel):
    """创建求助请求模型"""

    title: str = Field(..., min_length=1, max_length=200, description="资源名称")
    description: Optional[str] = Field(None, max_length=2000, description="详细描述")
    cloud_disk_types: List[CloudDiskType] = Field(
        ..., min_items=1, description="网盘类型列表"
    )
    resource_type: ResourceType = Field(ResourceType.OTHER, description="资源类型")

    @field_validator("cloud_disk_types")
    @classmethod
    def validate_cloud_disk_types(cls, v):
        """验证网盘类型"""
        if not v:
            raise ValueError("至少选择一种网盘类型")
        # 如果选择了"全部"，则只保留"全部"
        if CloudDiskType.ALL in v:
            return [CloudDiskType.ALL]
        return v


class HelpAnswerCreateRequest(BaseModel):
    """创建回答请求模型"""

    resource_title: str = Field(
        ..., min_length=1, max_length=200, description="资源标题"
    )
    resource_link: str = Field(
        ..., min_length=1, max_length=500, description="资源链接"
    )
    cloud_disk_type: CloudDiskType = Field(..., description="网盘类型")
    additional_info: Optional[str] = Field(
        None, max_length=1000, description="补充说明"
    )
    should_archive: bool = Field(False, description="是否入库")

    @field_validator("resource_link")
    @classmethod
    def validate_resource_link(cls, v):
        """验证资源链接格式"""
        if not v.startswith(("http://", "https://")):
            raise ValueError("资源链接必须是有效的URL")
        return v


class UserBasicInfo(BaseModel):
    """用户基本信息"""

    id: int
    username: str
    nickname: Optional[str]
    points: int
    title: str


class HelpRequestResponse(BaseModel):
    """求助响应模型"""

    id: int
    title: str
    description: Optional[str]
    cloud_disk_types: List[str]
    resource_type: str
    status: str
    requester: UserBasicInfo
    answer_count: int
    view_count: int
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]

    class Config:
        from_attributes = True


class HelpAnswerResponse(BaseModel):
    """回答响应模型"""

    id: int
    resource_title: str
    resource_link: str
    cloud_disk_type: str
    additional_info: Optional[str]
    should_archive: bool
    answerer: UserBasicInfo
    is_accepted: bool
    accepted_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class HelpRequestDetailResponse(BaseModel):
    """求助详情响应模型"""

    id: int
    title: str
    description: Optional[str]
    cloud_disk_types: List[str]
    resource_type: str
    status: str
    requester: UserBasicInfo
    answer_count: int
    view_count: int
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]
    answers: List[HelpAnswerResponse]

    class Config:
        from_attributes = True


class HelpRequestListResponse(BaseModel):
    """求助列表响应模型"""

    total: int
    page: int
    size: int
    pages: int
    requests: List[HelpRequestResponse]


class AcceptAnswerRequest(BaseModel):
    """采纳回答请求模型"""

    answer_id: int = Field(..., description="回答ID")


class HelpRequestStatsResponse(BaseModel):
    """求助统计响应模型"""

    total_requests: int
    open_requests: int
    resolved_requests: int
    total_answers: int
    accepted_answers: int
    top_helpers: List[UserBasicInfo]
    recent_requests: List[HelpRequestResponse]
