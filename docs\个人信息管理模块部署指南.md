# 个人信息管理模块部署指南

## 概述

本指南介绍如何部署和配置个人信息管理模块，包括数据库迁移、配置文件设置、存储方案配置等。

## 前置条件

- Python 3.8+
- PostgreSQL 12+
- Redis 6+ (可选，用于缓存)
- 已部署的基础系统

## 部署步骤

### 1. 数据库迁移

```bash
# 运行数据库迁移
aerich upgrade

# 或者手动执行迁移文件
psql -U pan_so_user -d pan_so_db -f migrations/models/6_20250731000000_add_profile_management.py
```

### 2. 配置文件设置

在 `app/config.yaml` 中添加以下配置：

```yaml
# 头像存储配置
avatar:
  # 存储类型: local, oss, cos, qiniu
  storage_type: "local"
  
  # 本地存储配置
  local_path: "uploads"
  base_url: "/uploads"
  
  # 阿里云OSS配置（如果使用OSS）
  oss:
    access_key_id: "your_access_key_id"
    access_key_secret: "your_access_key_secret"
    endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
    bucket_name: "your-bucket-name"
    cdn_domain: "your-cdn-domain.com"  # 可选
  
  # 腾讯云COS配置（如果使用COS）
  cos:
    secret_id: "your_secret_id"
    secret_key: "your_secret_key"
    region: "ap-beijing"
    bucket_name: "your-bucket-name"
  
  # 七牛云配置（如果使用七牛云）
  qiniu:
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket_name: "your-bucket-name"
    domain: "your-domain.com"

# 邮箱服务配置（如果未配置）
email:
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-app-password"
  from_email: "<EMAIL>"
  from_name: "Pan-So Team"

# 前端URL配置
app:
  frontend_url: "http://localhost:3000"  # 前端应用URL
```

### 3. 创建上传目录

如果使用本地存储，需要创建上传目录：

```bash
mkdir -p uploads/avatars
chmod 755 uploads
chmod 755 uploads/avatars
```

### 4. 安装依赖

如果使用云存储，需要安装相应的SDK：

```bash
# 阿里云OSS
pip install oss2

# 腾讯云COS
pip install cos-python-sdk-v5

# 七牛云
pip install qiniu

# 图片处理
pip install Pillow
```

### 5. Nginx配置

如果使用本地存储，需要配置Nginx提供静态文件服务：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 头像文件服务
    location /uploads/ {
        alias /path/to/your/project/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        
        # 防盗链配置
        valid_referers none blocked your-domain.com *.your-domain.com;
        if ($invalid_referer) {
            return 403;
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 存储方案配置详解

### 本地存储配置

```yaml
avatar:
  storage_type: "local"
  local_path: "uploads"  # 相对于项目根目录
  base_url: "/uploads"   # 访问URL前缀
```

**优点**: 简单、成本低
**缺点**: 扩展性差、需要备份
**适用**: 小规模部署

### 阿里云OSS配置

```yaml
avatar:
  storage_type: "oss"
  oss:
    access_key_id: "LTAI5t..."
    access_key_secret: "your_secret"
    endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
    bucket_name: "your-avatar-bucket"
    cdn_domain: "cdn.your-domain.com"  # 可选，配置CDN域名
```

**配置步骤**:
1. 创建OSS Bucket（建议私有读写）
2. 创建RAM用户，授予OSS权限
3. 配置CDN（可选）
4. 设置跨域规则（如果前端直传）

### 腾讯云COS配置

```yaml
avatar:
  storage_type: "cos"
  cos:
    secret_id: "AKIDxxxxx"
    secret_key: "your_secret"
    region: "ap-beijing"
    bucket_name: "your-avatar-bucket-1234567890"
```

### 七牛云配置

```yaml
avatar:
  storage_type: "qiniu"
  qiniu:
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket_name: "your-bucket"
    domain: "your-domain.qiniucdn.com"
```

## 安全配置

### 1. 文件上传安全

```python
# 在配置文件中设置
ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
```

### 2. 敏感词配置

可以在数据库中创建敏感词表，或者在配置文件中设置：

```yaml
nickname:
  sensitive_words:
    - "admin"
    - "管理员"
    - "系统"
    - "客服"
    - "官方"
  reserved_usernames:
    - "admin"
    - "root"
    - "system"
    - "api"
```

### 3. 邮箱验证配置

```yaml
email_verification:
  token_expire_hours: 24  # 验证链接有效期
  max_attempts: 3         # 最大尝试次数
  cooldown_minutes: 5     # 重发邮件冷却时间
```

## 监控和日志

### 1. 日志配置

```python
# 在logging配置中添加
LOGGING = {
    'loggers': {
        'profile': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
        'avatar': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    }
}
```

### 2. 监控指标

建议监控以下指标：
- 头像上传成功率
- 邮箱验证成功率
- 昵称修改频率
- 存储空间使用量
- API响应时间

## 性能优化

### 1. 数据库索引

确保以下索引已创建：

```sql
-- 用户昵称修改历史索引
CREATE INDEX idx_user_nickname_history_user_id ON user_nickname_history(user_id);
CREATE INDEX idx_user_nickname_history_created_at ON user_nickname_history(created_at);

-- 用户头像索引
CREATE INDEX idx_user_avatars_user_id ON user_avatars(user_id);
CREATE INDEX idx_user_avatars_is_active ON user_avatars(is_active);

-- 邮箱更改请求索引
CREATE INDEX idx_email_change_requests_token ON email_change_requests(verification_token);
CREATE INDEX idx_email_change_requests_expires ON email_change_requests(token_expires_at);
```

### 2. 缓存配置

如果使用Redis缓存：

```yaml
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""  # 如果有密码
  
cache:
  user_profile_ttl: 3600      # 用户资料缓存1小时
  avatar_url_ttl: 86400       # 头像URL缓存1天
  nickname_check_ttl: 300     # 昵称检查缓存5分钟
```

## 备份策略

### 1. 数据库备份

```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -U pan_so_user pan_so_db > backup_${DATE}.sql
```

### 2. 文件备份

如果使用本地存储：

```bash
# 同步到云存储
rsync -av uploads/ user@backup-server:/backup/uploads/
```

## 故障排除

### 常见问题

1. **头像上传失败**
   - 检查文件权限
   - 检查存储配置
   - 查看错误日志

2. **邮箱验证邮件发送失败**
   - 检查SMTP配置
   - 检查邮箱服务商限制
   - 查看邮件发送日志

3. **昵称修改失败**
   - 检查敏感词配置
   - 检查唯一性约束
   - 查看业务日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log | grep profile

# 查看错误日志
tail -f logs/error.log | grep -E "(avatar|nickname|email)"
```

## 测试验证

部署完成后，建议进行以下测试：

1. **功能测试**
   ```bash
   python test_profile_management.py
   ```

2. **API测试**
   ```bash
   # 使用curl测试API
   curl -X GET "http://localhost:8000/api/profile/me" \
        -H "Authorization: Bearer your_token"
   ```

3. **性能测试**
   ```bash
   # 使用ab进行压力测试
   ab -n 1000 -c 10 http://localhost:8000/api/profile/me
   ```

## 升级指南

当需要升级个人信息管理模块时：

1. 备份数据库和文件
2. 停止应用服务
3. 更新代码
4. 运行数据库迁移
5. 更新配置文件
6. 重启应用服务
7. 验证功能正常

通过以上配置和部署步骤，可以成功部署个人信息管理模块并确保其稳定运行。
