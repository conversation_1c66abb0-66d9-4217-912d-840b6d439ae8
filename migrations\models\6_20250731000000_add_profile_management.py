from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 为用户表添加个人信息管理相关字段
        ALTER TABLE "users" ADD COLUMN "last_nickname_change" TIMESTAMPTZ;
        ALTER TABLE "users" ADD COLUMN "nickname_change_count" INT NOT NULL DEFAULT 0;
        
        COMMENT ON COLUMN "users"."last_nickname_change" IS '上次昵称修改时间';
        COMMENT ON COLUMN "users"."nickname_change_count" IS '昵称修改次数';
        
        -- 创建用户昵称修改历史表
        CREATE TABLE IF NOT EXISTS "user_nickname_history" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "old_nickname" VARCHAR(100),
            "new_nickname" VARCHAR(100) NOT NULL,
            "change_reason" VARCHAR(255),
            "ip_address" VARCHAR(45),
            "user_agent" VARCHAR(512),
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "user_nickname_history" IS '用户昵称修改历史表';
        COMMENT ON COLUMN "user_nickname_history"."user_id" IS '关联用户';
        COMMENT ON COLUMN "user_nickname_history"."old_nickname" IS '旧昵称';
        COMMENT ON COLUMN "user_nickname_history"."new_nickname" IS '新昵称';
        COMMENT ON COLUMN "user_nickname_history"."change_reason" IS '修改原因';
        COMMENT ON COLUMN "user_nickname_history"."ip_address" IS '修改时的IP地址';
        COMMENT ON COLUMN "user_nickname_history"."user_agent" IS '用户代理';
        COMMENT ON COLUMN "user_nickname_history"."created_at" IS '修改时间';
        
        -- 创建索引
        CREATE INDEX "idx_user_nickname_history_user_id" ON "user_nickname_history" ("user_id");
        CREATE INDEX "idx_user_nickname_history_created_at" ON "user_nickname_history" ("created_at");
        
        -- 创建用户头像存储表
        CREATE TABLE IF NOT EXISTS "user_avatars" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "original_filename" VARCHAR(255) NOT NULL,
            "file_path" VARCHAR(512) NOT NULL,
            "file_size" INT NOT NULL,
            "mime_type" VARCHAR(100) NOT NULL,
            "width" INT,
            "height" INT,
            "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
            "storage_type" VARCHAR(20) NOT NULL DEFAULT 'local',
            "cdn_url" VARCHAR(512),
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "user_avatars" IS '用户头像存储表';
        COMMENT ON COLUMN "user_avatars"."user_id" IS '关联用户';
        COMMENT ON COLUMN "user_avatars"."original_filename" IS '原始文件名';
        COMMENT ON COLUMN "user_avatars"."file_path" IS '文件存储路径';
        COMMENT ON COLUMN "user_avatars"."file_size" IS '文件大小（字节）';
        COMMENT ON COLUMN "user_avatars"."mime_type" IS 'MIME类型';
        COMMENT ON COLUMN "user_avatars"."width" IS '图片宽度';
        COMMENT ON COLUMN "user_avatars"."height" IS '图片高度';
        COMMENT ON COLUMN "user_avatars"."is_active" IS '是否为当前头像';
        COMMENT ON COLUMN "user_avatars"."storage_type" IS '存储类型：local/oss/cos/qiniu';
        COMMENT ON COLUMN "user_avatars"."cdn_url" IS 'CDN访问URL';
        COMMENT ON COLUMN "user_avatars"."created_at" IS '上传时间';
        
        -- 创建索引
        CREATE INDEX "idx_user_avatars_user_id" ON "user_avatars" ("user_id");
        CREATE INDEX "idx_user_avatars_is_active" ON "user_avatars" ("is_active");
        CREATE INDEX "idx_user_avatars_created_at" ON "user_avatars" ("created_at");
        
        -- 创建邮箱更改请求表
        CREATE TABLE IF NOT EXISTS "email_change_requests" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "old_email" VARCHAR(100) NOT NULL,
            "new_email" VARCHAR(100) NOT NULL,
            "verification_token" VARCHAR(255) NOT NULL,
            "token_expires_at" TIMESTAMPTZ NOT NULL,
            "is_verified" BOOLEAN NOT NULL DEFAULT FALSE,
            "verified_at" TIMESTAMPTZ,
            "ip_address" VARCHAR(45),
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        
        COMMENT ON TABLE "email_change_requests" IS '邮箱更改请求表';
        COMMENT ON COLUMN "email_change_requests"."user_id" IS '关联用户';
        COMMENT ON COLUMN "email_change_requests"."old_email" IS '旧邮箱';
        COMMENT ON COLUMN "email_change_requests"."new_email" IS '新邮箱';
        COMMENT ON COLUMN "email_change_requests"."verification_token" IS '验证令牌';
        COMMENT ON COLUMN "email_change_requests"."token_expires_at" IS '令牌过期时间';
        COMMENT ON COLUMN "email_change_requests"."is_verified" IS '是否已验证';
        COMMENT ON COLUMN "email_change_requests"."verified_at" IS '验证时间';
        COMMENT ON COLUMN "email_change_requests"."ip_address" IS '请求IP地址';
        COMMENT ON COLUMN "email_change_requests"."created_at" IS '创建时间';
        
        -- 创建索引
        CREATE INDEX "idx_email_change_requests_user_id" ON "email_change_requests" ("user_id");
        CREATE INDEX "idx_email_change_requests_token" ON "email_change_requests" ("verification_token");
        CREATE INDEX "idx_email_change_requests_expires" ON "email_change_requests" ("token_expires_at");
        CREATE INDEX "idx_email_change_requests_verified" ON "email_change_requests" ("is_verified");
        
        -- 确保每个用户只有一个活跃头像的约束
        CREATE UNIQUE INDEX "idx_user_avatars_active_unique" ON "user_avatars" ("user_id") WHERE "is_active" = TRUE;
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除索引
        DROP INDEX IF EXISTS "idx_user_avatars_active_unique";
        DROP INDEX IF EXISTS "idx_email_change_requests_verified";
        DROP INDEX IF EXISTS "idx_email_change_requests_expires";
        DROP INDEX IF EXISTS "idx_email_change_requests_token";
        DROP INDEX IF EXISTS "idx_email_change_requests_user_id";
        DROP INDEX IF EXISTS "idx_user_avatars_created_at";
        DROP INDEX IF EXISTS "idx_user_avatars_is_active";
        DROP INDEX IF EXISTS "idx_user_avatars_user_id";
        DROP INDEX IF EXISTS "idx_user_nickname_history_created_at";
        DROP INDEX IF EXISTS "idx_user_nickname_history_user_id";
        
        -- 删除表
        DROP TABLE IF EXISTS "email_change_requests";
        DROP TABLE IF EXISTS "user_avatars";
        DROP TABLE IF EXISTS "user_nickname_history";
        
        -- 删除用户表新增字段
        ALTER TABLE "users" DROP COLUMN IF EXISTS "nickname_change_count";
        ALTER TABLE "users" DROP COLUMN IF EXISTS "last_nickname_change";
    """
