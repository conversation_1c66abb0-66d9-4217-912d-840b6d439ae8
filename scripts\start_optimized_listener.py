#!/usr/bin/env python3
"""
优化后的 Meilisearch 同步监听器启动脚本
包含完整的错误处理、日志配置和性能监控
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.tasks.meilisearch_sync_listener import main
from app.services.async_meilisearch_service import async_meilisearch_service


def setup_logging():
    """配置日志系统"""
    # 创建日志目录
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            # 控制台输出
            logging.StreamHandler(sys.stdout),
            # 文件输出
            logging.FileHandler(
                log_dir / "meilisearch_sync_listener.log", encoding="utf-8"
            ),
            # 错误日志单独文件
            logging.FileHandler(
                log_dir / "meilisearch_sync_listener_error.log",
                level=logging.ERROR,
                encoding="utf-8",
            ),
        ],
    )

    # 设置特定模块的日志级别
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("asyncpg").setLevel(logging.WARNING)


def check_dependencies():
    """检查依赖项"""
    required_modules = ["asyncpg", "aiohttp", "psutil", "tortoise"]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("请运行: pip install " + " ".join(missing_modules))
        return False

    return True


async def check_services():
    """检查外部服务连接"""
    logger = logging.getLogger(__name__)

    # 检查 Meilisearch 连接
    try:
        session = await async_meilisearch_service._get_session()
        url = f"{async_meilisearch_service.host}/health"
        async with session.get(url, timeout=5) as response:
            if response.status == 200:
                logger.info("✅ Meilisearch 连接正常")
            else:
                logger.warning(f"⚠️  Meilisearch 响应异常: {response.status}")
    except Exception as e:
        logger.error(f"❌ Meilisearch 连接失败: {e}")
        return False

    # 检查数据库连接
    try:
        import asyncpg
        from app.utils.config import settings

        db_creds = (
            settings.get("database", {})
            .get("connections", {})
            .get("default", {})
            .get("credentials", {})
        )

        conn = await asyncpg.connect(
            user=db_creds.get("user"),
            password=db_creds.get("password"),
            database=db_creds.get("database"),
            host=db_creds.get("host"),
            port=db_creds.get("port"),
            timeout=5,
        )
        await conn.close()
        logger.info("✅ 数据库连接正常")
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

    return True


def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("🚀 优化后的 Meilisearch 同步监听器")
    print("=" * 60)
    print("✨ 主要优化:")
    print("  • 修复了数据库连接泄漏问题")
    print("  • 实现了批处理机制，减少API调用")
    print("  • 添加了异步架构，避免阻塞")
    print("  • 集成了健康监控和性能统计")
    print("  • 优化了重连机制，支持指数退避")
    print("  • 添加了优雅关闭和信号处理")
    print()
    print("📊 监控功能:")
    print("  • 内存使用监控 (阈值: 500MB)")
    print("  • CPU使用率监控")
    print("  • 连接数监控")
    print("  • 批处理队列监控")
    print()
    print("🔧 性能配置:")
    print("  • 批处理大小: 50")
    print("  • 刷新间隔: 5秒")
    print("  • 最大队列大小: 1000")
    print("  • 连接超时: 30秒")
    print("=" * 60)


async def run_with_monitoring():
    """运行监听器并启动性能监控"""
    logger = logging.getLogger(__name__)

    try:
        # 启动主服务
        await main()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"服务运行异常: {e}", exc_info=True)
        raise
    finally:
        # 清理资源
        try:
            await async_meilisearch_service.close()
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")


def main_entry():
    """主入口函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    # 打印启动信息
    print_startup_info()

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查服务连接
    async def check_and_run():
        if not await check_services():
            logger.error("服务检查失败，请检查 Meilisearch 和数据库连接")
            sys.exit(1)

        logger.info("所有服务检查通过，启动监听器...")
        await run_with_monitoring()

    try:
        # 运行服务
        asyncio.run(check_and_run())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main_entry()
