import asyncio
import random
from datetime import datetime
from typing import List
from urllib.parse import unquote, urlparse, urljoin

from bs4 import BeautifulSoup
from playwright.async_api import async_playwright, <PERSON>, Browser

from app.crawlers.base_crawler import BaseCrawler
from app.models.pydantic_models import CrawledResource
from app.services.db_batch import add_to_db_queue
from app.utils.pan_url_parser import parse_pan_url
from app.utils.common import infer_file_type, get_normalized_file_type, BEIJING_TIMEZONE


class XuebapansCrawler(BaseCrawler):
    """
    学霸盘（xuebapans.com）巡检式爬虫。
    能够遍历多个预设分类，并对每个分类下的所有分页进行深度抓取。
    """

    def __init__(self):
        """
        初始化爬虫，设置名称、基础URL和目标分类列表。
        """
        super().__init__("xuebapans")
        self.base_url = "https://www.xuebapans.com"
        # 维护一个需要爬取的资源分类页面列表
        self.target_categories = [
            "/res/new",
            # "/res/new/2025xinbandianzikeben",
            # "/res/new/%E3%80%90jiaoshizhuanqu%E3%80%91kejianjiaoanjiaoxuesheji",
            # "/res/new/gaozhongxuexiziliao",
            # "/res/new/chuzhongxuexiziliao",
            # "/res/new/xiaoxuexuexiziliao",
            # "/res/new/%E3%80%90xiaochugao%E3%80%91K12xuexiziliao",
            # "/res/new/zhuanshengbendaxuekaoyanziliao",
            # "/res/new/bangongsucai",
            # "/res/new/qingshaoniansuzhituozhan",
            # "/res/new/xueqian-zaojiao-youerziliao",
        ]

    async def crawl(self, **kwargs) -> int:
        """
        执行一次完整的巡检抓取任务，并分批次将结果存入数据库。
        返回值为本次任务总共提交到数据库队列的资源数量。
        """
        total_queued_count = 0
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=self.config.get("headless", True)
            )
            try:
                for category_path in self.target_categories:
                    try:
                        category_url = urljoin(self.base_url, category_path)
                        self.logger.info(f"--- 开始处理新分类: {category_url} ---")

                        # 1. 访问分类首页，获取总页数
                        page = await browser.new_page()
                        await page.goto(
                            category_url,
                            wait_until="domcontentloaded",
                            timeout=60000,
                        )
                        max_pages = await self._get_max_pages(page)
                        self.logger.info(
                            f"分类 '{category_path}' 共发现 {max_pages} 页。"
                        )
                        await page.close()

                        # 2. 遍历所有分页
                        for page_num in range(1, 3 + 1):
                            # 确保基础URL以/结尾，以便urljoin能正确拼接
                            base_category_url = category_url
                            if not base_category_url.endswith("/"):
                                base_category_url += "/"
                            page_url = urljoin(base_category_url, str(page_num))

                            self.logger.info(f"准备处理分页URL: {page_url}")

                            page_results = await self._process_resource_list_page(
                                browser, page_url
                            )
                            if page_results:
                                # 3. 每处理完一页，立即将结果加入数据库队列
                                items_to_save = [
                                    resource.model_dump() for resource in page_results
                                ]
                                await add_to_db_queue(items_to_save)
                                count = len(items_to_save)
                                total_queued_count += count
                                self.logger.info(
                                    f"已将 {count} 条资源从页面 {page_url} 添加到数据库处理队列。"
                                )
                    except Exception as e:
                        self.logger.error(
                            f"处理分类 {category_path} 时发生顶层错误: {e}",
                            exc_info=True,
                        )
            finally:
                await browser.close()

        self.logger.info(
            f"所有分类抓取完毕，共计 {total_queued_count} 条资源已提交至数据库队列。"
        )
        return total_queued_count

    async def _get_max_pages(self, page: Page) -> int:
        """从页面中解析出最大页码。"""
        try:
            # 根据您的要求，使用精确的XPath定位代表"最后一页"的链接
            last_page_locator = page.locator(
                'xpath=//*[@id="wrap"]/div[1]/div/div[2]/div[1]/div[2]/div/a[last()-1]'
            )

            # 改进：在尝试获取文本之前，先等待元素可见，增加稳定性
            await last_page_locator.wait_for(state="visible", timeout=5000)

            last_page_text = await last_page_locator.text_content()

            if last_page_text and last_page_text.strip().isdigit():
                max_pages = int(last_page_text.strip())
                self.logger.info(f"通过XPath成功解析到最大页码: {max_pages}")
                return max_pages
            else:
                self.logger.warning(
                    "通过XPath未能解析出有效的页码数字，默认只有 1 页。"
                )
                return 1
        except Exception:
            # 如果XPath定位失败（例如页面结构变化或总页数少于XPath指定的页数），则退回到遍历所有分页链接的备用方案
            self.logger.warning(
                "使用精确XPath定位最大页码失败，将尝试备用方案：遍历所有分页链接。"
            )
            try:
                pagination_links = page.locator(".yp-pagination a")
                all_page_numbers = []
                count = await pagination_links.count()
                for i in range(count):
                    text = await pagination_links.nth(i).text_content()
                    if text and text.strip().isdigit():
                        all_page_numbers.append(int(text.strip()))

                if not all_page_numbers:
                    self.logger.info("备用方案也未找到分页链接，默认只有 1 页。")
                    return 1
                return max(all_page_numbers)
            except Exception as e_fallback:
                self.logger.error(
                    f"备用方案解析最大页码也失败，将只抓取第 1 页。错误: {e_fallback}",
                    exc_info=True,
                )
                return 1

    async def _process_resource_list_page(
        self, browser: Browser, page_url: str
    ) -> List[CrawledResource]:
        """处理单个资源列表页面，返回该页面所有资源的抓取结果。"""
        self.logger.info(f"正在处理页面: {page_url}")
        page = await browser.new_page()
        page_results = []
        try:
            await page.goto(page_url, wait_until="domcontentloaded", timeout=60000)
            links_locator = page.locator(
                'xpath=//td[contains(@class, "td-second-col")]/a[1]'
            )
            count = await links_locator.count()
            if count == 0:
                self.logger.warning(f"在页面 {page_url} 未找到任何资源链接。")
                return []

            all_hrefs = []
            for i in range(count):
                href = await links_locator.nth(i).get_attribute("href")
                if href:
                    all_hrefs.append(href)

            for href in all_hrefs:
                detail_page = None
                search_page = None  # 先声明，确保finally中可用
                try:
                    search_page_url = urljoin(self.base_url, href)
                    search_page = await browser.new_page()  # 使用await获取页面对象
                    await search_page.goto(search_page_url)
                    async with search_page.context.expect_page() as dp_info:
                        await search_page.locator(
                            'xpath=//*[@id="wrap"]/div[1]/div/div[1]/div[2]/div[2]/div/div/a/div[1]'
                        ).click()
                    detail_page = await dp_info.value
                    await detail_page.wait_for_load_state()
                    resource = await self._parse_detail_page(detail_page)
                    if resource:
                        page_results.append(resource)
                except Exception as e:
                    self.logger.error(f"处理详情链接 {href} 失败: {e}", exc_info=True)
                finally:
                    if detail_page and not detail_page.is_closed():
                        await detail_page.close()
                    # 新增：确保 search_page 也能被关闭
                    if search_page and not search_page.is_closed():
                        await search_page.close()
            return page_results
        finally:
            if page and not page.is_closed():
                await page.close()

    async def _parse_detail_page(self, page: Page) -> CrawledResource | None:
        """解析单个资源详情页，提取所有目标信息。"""
        url = page.url
        try:
            content = await page.content()
            soup = BeautifulSoup(content, "html.parser")
            title_element = soup.select_one("h1.yp-detail-main-info-title a")
            title = title_element.get_text(strip=True) if title_element else "未知标题"
            description_element = soup.select_one('p.yp-xuebapanscom[data-line="0"]')
            text_content = (
                description_element.get_text(strip=True) if description_element else ""
            )

            # --- 更稳健的时间提取逻辑 ---
            updated_at_dt = datetime.now(BEIJING_TIMEZONE)  # 默认值，使用北京时间
            try:
                # 使用CSS选择器定位，比绝对XPath更稳定
                time_locator = page.locator(
                    "span.yp-detail-main-info-text-item-value[title]"
                )

                # 1. 优先尝试从'title'属性获取时间
                time_str = await time_locator.get_attribute("title", timeout=3000)

                # 2. 如果'title'为空，再尝试获取元素的文本内容
                if not time_str or not time_str.strip():
                    self.logger.warning(
                        "未能从 'title' 属性获取时间，尝试获取元素文本内容。"
                    )
                    time_str = await time_locator.text_content()

                # 3. 如果获取到时间字符串，则解析
                if time_str and time_str.strip():
                    self.logger.info(f"成功获取到时间字符串: '{time_str.strip()}'")
                    parsed_dt = datetime.strptime(time_str.strip(), "%Y-%m-%d %H:%M:%S")
                    # 假设解析出的时间是北京时间，设置为北京时区
                    updated_at_dt = parsed_dt.replace(tzinfo=BEIJING_TIMEZONE)
                else:
                    self.logger.error(
                        f"无法从目标元素中提取有效时间字符串，将使用当前时间: {url}"
                    )

            except Exception as e:
                self.logger.warning(f"解析时间时发生错误 ({e})，将使用当前时间: {url}")

            self.logger.info(f"--- 阶段1: 静态信息提取成功 ---")
            self.logger.info(f"  - 标题: {title}")
            self.logger.info(f"  - 更新时间: {updated_at_dt.isoformat()}")
            self.logger.info(f"------------------------------------")

            # 增加随机延迟，模拟人类行为
            random_delay = random.uniform(0.2, 0.5)
            self.logger.info(f"随机等待 {random_delay:.2f} 秒后点击下载...")
            await asyncio.sleep(random_delay)

            await page.locator(
                'li.yp-xuebapanscom[data-v-45739cdc]:has-text("下载")'
            ).click(timeout=5000)
            final_link_locator = page.locator(
                'a.resource-link:has-text("同意声明，获取资源")'
            )
            await final_link_locator.wait_for(timeout=10000)
            final_link_raw = await final_link_locator.get_attribute("href")
            if not final_link_raw:
                return None
            pan_url = unquote(urlparse(final_link_raw).query.split("=")[1])
            self.logger.info(f"pan_url: {pan_url}")
            parsed_info = parse_pan_url(pan_url)
            if not parsed_info or "pan_type_int" not in parsed_info:
                self.logger.warning(f"无法从URL解析出pan_type，已跳过: {pan_url}")
                return None
            #  通过utils.common获取文件类型
            file_type = infer_file_type(title, text_content)
            normalized_file_type = get_normalized_file_type(file_type)
            return CrawledResource(
                title=title,
                resource_key=parsed_info.get("resource_key"),
                text_content=text_content,
                original_url=pan_url,  # 详情页的URL
                source=self.crawler_name,
                updated_at=updated_at_dt,
                pan_type=parsed_info["pan_type_int"],
                file_type=normalized_file_type,
            )
        except Exception as e:
            self.logger.error(f"解析详情页 {url} 失败: {e}", exc_info=True)
            return None

    async def start_monitoring(self):
        # 该爬虫为一次性任务，不实现持续监控
        raise NotImplementedError
