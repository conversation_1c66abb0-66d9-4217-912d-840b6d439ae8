from app.utils.config import settings

# 为 Aerich 数据库迁移工具和 Tortoise ORM 提供共享配置
# 这份配置被 app.db.engine.init_db 和 aerich 命令行工具共同使用
TORTOISE_ORM = {
    "connections": {"default": settings.get("database.url")},
    "apps": {
        "models": {
            "models": [
                "app.models.resource",
                "app.models.feedback",
                "app.models.submission",
                "app.models.user",  # 新增用户模型
                "app.models.help_request",  # 新增求助模型
                "app.models.config_models",  # 新增配置管理模型
                "aerich.models",  # aerich需要这个来管理自己的历史记录表
            ],
            "default_connection": "default",
        },
    },
}
