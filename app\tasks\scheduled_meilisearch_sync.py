"""
定时批量Meilisearch同步服务
替换实时同步机制，提供更高效的批量处理能力
"""

import asyncio
import logging
import signal
import time
from datetime import datetime
from typing import Dict, Optional

from tortoise.transactions import in_transaction

from app.db.engine import init_db, close_db
from app.services.intelligent_batch_manager import intelligent_batch_manager, Priority

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class OperationMerger:
    """操作合并器，用于优化同步操作"""

    def __init__(self):
        self.operations: Dict[str, str] = {}  # resource_key -> latest_operation
        self.timestamps: Dict[str, datetime] = {}  # resource_key -> latest_timestamp

    def add_operation(self, resource_key: str, operation: str, timestamp: datetime):
        """添加操作，自动合并重复操作"""
        current_op = self.operations.get(resource_key)
        current_time = self.timestamps.get(resource_key)

        # 如果是更新的操作，或者是第一次操作
        if not current_time or timestamp > current_time:
            # 操作合并逻辑
            if current_op == "DELETE":
                # 删除后的任何操作都保持删除
                if operation != "DELETE":
                    # DELETE -> INSERT/UPDATE = INSERT
                    self.operations[resource_key] = "INSERT"
                else:
                    self.operations[resource_key] = "DELETE"
            elif current_op == "INSERT":
                # 插入后的更新仍然是插入，删除则是删除
                if operation == "DELETE":
                    self.operations[resource_key] = "DELETE"
                else:
                    self.operations[resource_key] = "INSERT"
            elif current_op == "UPDATE":
                # 更新后的操作直接替换
                self.operations[resource_key] = operation
            else:
                # 第一次操作
                self.operations[resource_key] = operation

            self.timestamps[resource_key] = timestamp

    def get_merged_operations(self) -> Dict[str, str]:
        """获取合并后的操作"""
        return self.operations.copy()

    def clear(self):
        """清空操作记录"""
        self.operations.clear()
        self.timestamps.clear()


class ScheduledMeilisearchSync:
    """定时Meilisearch同步服务 - 现在使用智能批量管理器"""

    def __init__(
        self,
        sync_interval: int = 300,  # 5分钟同步间隔
        max_retry: int = 3,
    ):  # 最大重试次数
        self.sync_interval = sync_interval
        self.max_retry = max_retry
        self.running = False
        self.sync_task: Optional[asyncio.Task] = None

        # 统计信息
        self.stats = {
            "total_synced": 0,
            "total_errors": 0,
            "last_sync_time": None,
            "avg_sync_duration": 0,
            "delegated_to_batch_manager": 0,
        }

        # 使用智能批量管理器
        self.batch_manager = intelligent_batch_manager

    async def start(self):
        """启动同步服务"""
        if self.running:
            logger.warning("同步服务已在运行")
            return

        # 先启动智能批量管理器
        await self.batch_manager.start()

        self.running = True
        self.sync_task = asyncio.create_task(self._sync_loop())
        logger.info(
            f"🚀 定时同步服务已启动，间隔: {self.sync_interval}秒，使用智能批量管理器"
        )

    async def stop(self):
        """停止同步服务"""
        logger.info("正在停止同步服务...")
        self.running = False

        if self.sync_task:
            self.sync_task.cancel()
            try:
                await self.sync_task
            except asyncio.CancelledError:
                pass

        # 停止智能批量管理器
        await self.batch_manager.stop()
        logger.info("✅ 定时同步服务已停止")

    async def _sync_loop(self):
        """主同步循环"""
        while self.running:
            try:
                start_time = time.time()

                # 处理待同步的变更
                processed_count = await self._process_pending_changes()

                # 更新统计信息
                sync_duration = time.time() - start_time
                self.stats["last_sync_time"] = datetime.now()
                self.stats["total_synced"] += processed_count

                # 计算平均同步时间
                if self.stats["avg_sync_duration"] == 0:
                    self.stats["avg_sync_duration"] = sync_duration
                else:
                    self.stats["avg_sync_duration"] = (
                        self.stats["avg_sync_duration"] * 0.8 + sync_duration * 0.2
                    )

                if processed_count > 0:
                    logger.info(
                        f"同步完成: {processed_count}个操作，耗时: {sync_duration:.2f}秒"
                    )

                # 等待下次同步
                await asyncio.sleep(self.sync_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"同步循环异常: {e}", exc_info=True)
                self.stats["total_errors"] += 1
                await asyncio.sleep(5)  # 错误后短暂等待

    async def _process_pending_changes(self) -> int:
        """处理待同步的变更 - 现在委托给智能批量管理器"""
        try:
            # 使用更大的批量大小，因为我们现在只是委托操作
            batch_size = 1000

            async with in_transaction() as conn:
                # 获取未处理的变更记录
                query_result = await conn.execute_query(
                    """
                    SELECT id, resource_key, operation, created_at, retry_count
                    FROM meilisearch_sync_log
                    WHERE processed = FALSE
                    AND retry_count < $1
                    ORDER BY created_at ASC
                    LIMIT $2
                    """,
                    [self.max_retry, batch_size],
                )

                # 正确解析 execute_query 的返回值
                if isinstance(query_result, tuple) and len(query_result) == 2:
                    _, changes = query_result  # 解构元组：(affected_rows, resultset)
                else:
                    # 兼容性处理：如果返回格式不是预期的元组
                    changes = query_result if query_result else []

                if not changes:
                    return 0

                logger.info(f"📋 发现 {len(changes)} 个待处理的变更记录")

                # 解析变更记录并委托给智能批量管理器
                change_ids = []
                delegated_count = 0

                if isinstance(changes, list):
                    for change in changes:
                        try:
                            # 统一处理不同格式的变更记录
                            if isinstance(change, (list, tuple)) and len(change) >= 5:
                                change_data = {
                                    "id": change[0],
                                    "resource_key": change[1],
                                    "operation": change[2],
                                    "created_at": change[3],
                                    "retry_count": change[4],
                                }
                            elif isinstance(change, dict):
                                change_data = change
                            elif hasattr(change, "__getitem__") and hasattr(
                                change, "keys"
                            ):
                                change_data = {
                                    "id": change["id"],
                                    "resource_key": change["resource_key"],
                                    "operation": change["operation"],
                                    "created_at": change["created_at"],
                                    "retry_count": change["retry_count"],
                                }
                            else:
                                logger.warning(
                                    f"跳过无效的变更记录格式: {type(change)}"
                                )
                                continue

                            # 委托给智能批量管理器
                            priority = (
                                Priority.HIGH
                                if change_data["retry_count"] > 0
                                else Priority.NORMAL
                            )
                            success = await self.batch_manager.add_operation(
                                operation_type=change_data["operation"],
                                resource_key=change_data["resource_key"],
                                priority=priority,
                                user_triggered=False,
                            )

                            if success:
                                change_ids.append(change_data["id"])
                                delegated_count += 1
                                logger.debug(
                                    f"✅ 委托操作: {change_data['operation']} {change_data['resource_key']}"
                                )
                            else:
                                logger.warning(
                                    f"❌ 委托失败: {change_data['operation']} {change_data['resource_key']}"
                                )

                        except Exception as e:
                            logger.error(f"处理变更记录时出错: {e}", exc_info=True)
                            continue

                if not change_ids:
                    logger.warning("没有成功委托任何操作")
                    return 0

                # 标记已委托的记录为已处理
                if change_ids:
                    update_result = await conn.execute_query(
                        """
                        UPDATE meilisearch_sync_log
                        SET processed = TRUE, processed_at = NOW()
                        WHERE id = ANY($1)
                        """,
                        [change_ids],
                    )

                    # 记录更新结果
                    if isinstance(update_result, tuple) and len(update_result) == 2:
                        affected_rows, _ = update_result
                        logger.info(f"✅ 标记了 {affected_rows} 条记录为已处理")
                    else:
                        logger.info(f"✅ 标记记录为已处理，结果: {update_result}")

                # 更新统计信息
                self.stats["delegated_to_batch_manager"] += delegated_count

                logger.info(f"🎯 成功委托 {delegated_count} 个操作给智能批量管理器")
                return delegated_count

        except Exception as e:
            logger.error(f"处理待同步变更时出错: {e}", exc_info=True)
            self.stats["total_errors"] += 1
            return 0

    def get_stats(self) -> Dict:
        """获取统计信息"""
        batch_manager_stats = self.batch_manager.get_stats()

        return {
            **self.stats,
            "running": self.running,
            "sync_interval": self.sync_interval,
            "batch_manager_stats": batch_manager_stats,
        }


# 全局同步服务实例 - 使用智能批量管理器
scheduled_sync_service = ScheduledMeilisearchSync(
    sync_interval=300,  # 5分钟同步间隔
    max_retry=3,  # 最大重试3次
)


async def cleanup_old_logs():
    """清理旧的同步日志"""
    try:
        async with in_transaction() as conn:
            # execute_query 返回 (affected_rows, resultset) 元组
            query_result = await conn.execute_query(
                """
                DELETE FROM meilisearch_sync_log
                WHERE processed = TRUE
                AND processed_at < NOW() - INTERVAL '7 days'
                """,
                [],
            )

            # 正确解析 execute_query 的返回值
            if isinstance(query_result, tuple) and len(query_result) == 2:
                affected_rows, _ = query_result  # 解构元组：(affected_rows, resultset)
                if affected_rows > 0:
                    logger.info(f"清理了 {affected_rows} 条旧的同步日志")
            else:
                # 兼容性处理：如果返回格式不是预期的元组
                if query_result:
                    logger.info(f"清理了旧的同步日志，结果: {query_result}")
    except Exception as e:
        logger.error(f"清理旧日志时出错: {e}", exc_info=True)


async def main():
    """主函数"""

    # 设置信号处理
    def signal_handler(signum, frame):
        _ = frame  # 忽略未使用的参数
        logger.info(f"收到信号 {signum}，准备关闭...")
        asyncio.create_task(scheduled_sync_service.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 初始化数据库
        logger.info("初始化数据库连接...")
        await init_db()

        # 启动同步服务
        logger.info("启动定时同步服务...")
        await scheduled_sync_service.start()

        # 定期清理旧日志（每小时一次）
        async def periodic_cleanup():
            while scheduled_sync_service.running:
                await asyncio.sleep(3600)  # 1小时
                await cleanup_old_logs()

        cleanup_task = asyncio.create_task(periodic_cleanup())

        # 等待服务运行
        while scheduled_sync_service.running:
            await asyncio.sleep(1)

        cleanup_task.cancel()

    except Exception as e:
        logger.error(f"服务启动失败: {e}", exc_info=True)
    finally:
        await close_db()
        logger.info("服务已关闭")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务被用户停止")
