"""
配置管理相关的数据模型 - 增强版
"""

from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict, Union


# Pydantic 请求模型


class ConfigBackupRequest(BaseModel):
    """配置备份请求"""

    comment: Optional[str] = Field(None, description="备份说明")


class ConfigResetRequest(BaseModel):
    """配置重置请求"""

    keys: List[str] = Field(..., description="要重置的配置键列表")
    comment: Optional[str] = Field(None, description="重置说明")


class ConfigValidationRequest(BaseModel):
    """配置验证请求"""

    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")


# Pydantic 响应模型
class ValidationResult(BaseModel):
    """验证结果"""

    valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="验证消息")
    suggestions: Optional[List[str]] = Field(None, description="建议")


# 新增：树形结构相关模型
class ConfigTreeNode(BaseModel):
    """配置树节点"""

    key: str = Field(..., description="配置键")
    display_name: str = Field(..., description="显示名称")
    path: str = Field(..., description="完整路径")
    type: str = Field(..., description="数据类型")
    value: Any = Field(..., description="配置值")
    comment: Optional[str] = Field(None, description="注释")
    children: Optional[Dict[str, "ConfigTreeNode"]] = Field(None, description="子节点")
    is_leaf: bool = Field(True, description="是否为叶子节点")
    sensitive: bool = Field(False, description="是否敏感信息")
    required: bool = Field(False, description="是否必填")
    validation_rules: Optional[Dict] = Field(None, description="验证规则")
    effect_type: str = Field("immediate", description="生效方式")


class ConfigTreeResponse(BaseModel):
    """配置树响应"""

    tree: Dict[str, ConfigTreeNode] = Field(..., description="配置树")
    total_nodes: int = Field(..., description="总节点数")
    max_depth: int = Field(..., description="最大深度")


class ConfigPathRequest(BaseModel):
    """配置路径请求"""

    path: str = Field(..., description="配置路径", example="baidu_accounts.0.cookie")
    value: Any = Field(..., description="新配置值")
    comment: Optional[str] = Field(None, description="修改说明")


class ConfigPathResponse(BaseModel):
    """配置路径响应"""

    path: str = Field(..., description="配置路径")
    value: Any = Field(..., description="配置值")
    type: str = Field(..., description="数据类型")
    comment: Optional[str] = Field(None, description="注释")
    sensitive: bool = Field(False, description="是否敏感信息")
    parent_path: Optional[str] = Field(None, description="父级路径")
    children_paths: Optional[List[str]] = Field(None, description="子级路径列表")


class ConfigSchemaField(BaseModel):
    """配置模式字段"""

    name: str = Field(..., description="字段名")
    type: str = Field(..., description="数据类型")
    required: bool = Field(False, description="是否必填")
    default: Any = Field(None, description="默认值")
    description: Optional[str] = Field(None, description="字段描述")
    validation_rules: Optional[Dict] = Field(None, description="验证规则")
    sensitive: bool = Field(False, description="是否敏感信息")
    enum_values: Optional[List[Any]] = Field(None, description="枚举值")


class ConfigSchema(BaseModel):
    """配置模式"""

    name: str = Field(..., description="模式名称")
    description: Optional[str] = Field(None, description="模式描述")
    fields: List[ConfigSchemaField] = Field(..., description="字段列表")
    required_fields: List[str] = Field([], description="必填字段列表")


class ConfigSchemaResponse(BaseModel):
    """配置模式响应"""

    schemas: Dict[str, ConfigSchema] = Field(..., description="配置模式字典")
    total_schemas: int = Field(..., description="总模式数")


# 更新ConfigTreeNode以支持自引用
ConfigTreeNode.model_rebuild()
