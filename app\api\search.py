from fastapi import APIRouter, Query, Request, Depends, HTTPException
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, timedelta
import time
import jieba
import logging
from app.crawlers.panku8_crawler import panku8_crawler
from app.crawlers.esoua_crawler import esoua_crawler
from app.services.local_search_service import local_search_service

from app.models.pydantic_models import SearchResponse, SearchResult
from app.models.enums import TimeFilter
from app.utils.common import (
    get_normalized_file_type,
    infer_file_type,
    parse_publish_time,
    filter_results_by_time,
    format_update_time,
)
from app.services.db_batch import batch_save_to_db, add_to_db_queue
from app.middleware.verify import verify_origin
from app.utils.config import settings  # 导入配置
import asyncio

router = APIRouter(tags=["search"])
logger = logging.getLogger("pan-service")

# 依赖
dependencies = [Depends(verify_origin)]

# 搜索结果缓存
search_results_cache = {}

# 缓存过期时间（秒）
CACHE_EXPIRY = 30  # 30秒
CRAWLER_LIMIT = 30  # 爬虫每次请求限制

# 加载屏蔽词
BLOCKED_KEYWORDS = settings.get("security.blocked_keywords", [])

# 定义北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))


@router.get(
    "/search",
    response_model=SearchResponse,
    dependencies=[Depends(verify_origin)],
    summary="搜索网盘资源",
    description="根据传入的pan_type，从多个爬虫服务获取资源列表",
)
async def search(
    request: Request,
    keyword: str = Query(..., description="搜索关键词"),
    page: int = Query(1, description="页码"),
    limit: int = Query(10, description="每页结果数"),
    file_type: Optional[str] = Query(
        None,
        description="文件类型过滤: video(视频), audio(音频), image(图片), document(文档), archive(压缩), application(应用)",
    ),
    pan_type: Optional[int] = Query(
        None,
        description="网盘类型: 0=全部, 1=百度网盘, 2=夸克网盘, 3-阿里云盘, 4-迅雷网盘",
    ),
    sort_by: str = Query(
        "relevance", description="排序字段: relevance, file_size, updated_at"
    ),
    sort_order: str = Query("desc", description="排序方向: asc, desc"),
    exact: bool = Query(False, description="是否精准搜索"),
    time_filter: TimeFilter = Query(
        TimeFilter.ALL,
        description="时间过滤: all(全部时间), week(最近一周), half_month(最近半月), month(最近1月), half_year(最近半年), year(最近一年)",
    ),
):
    """
    搜索网盘资源.

    新版V4搜索逻辑:
    1.  对每个唯一搜索请求（关键词+筛选条件），执行一次完整的本地+在线搜索。
    2.  在线搜索固定只请求第一页，上限30条。
    3.  合并本地和在线结果，去重、评分、排序。
    4.  将完整排序结果缓存，后续分页请求直接从缓存读取。
    5.  在线获取的新数据异步存入数据库。
    """
    start_time = time.time()
    if not keyword:
        raise HTTPException(status_code=400, detail="搜索关键词不能为空")

    if any(blocked.lower() in keyword.lower() for blocked in BLOCKED_KEYWORDS):
        logger.warning(f"搜索词 '{keyword}' 命中屏蔽词")
        return SearchResponse(
            status="failed",
            message="抱歉，我们不能提供有关该搜索词的搜索结果",
            total=0,
            results=[],
        )

    if limit > 30:
        limit = 30

    normalized_file_type = get_normalized_file_type(file_type) if file_type else None

    logger.info(
        f"搜索: k={keyword}, p={page}, limit={limit}, t={pan_type}, file_type={normalized_file_type}, exact={exact}, time_filter={time_filter}"
    )

    try:
        # 1. 定义缓存键，包含时间过滤参数
        cache_key_prefix = "exact_" if exact else ""
        cache_key = f"v4_{cache_key_prefix}{keyword}_{pan_type}_{normalized_file_type}_{sort_by}_{sort_order}_{time_filter}"

        # 2. 检查缓存
        cached_data = search_results_cache.get(cache_key)
        if cached_data and time.time() - cached_data.get("timestamp", 0) < CACHE_EXPIRY:
            logger.info(f"命中缓存: {cache_key}")
            sorted_results = cached_data["results"]
            total_items = cached_data["total"]
        else:
            logger.info(f"未命中缓存: {cache_key}，发起新的聚合搜索...")
            # 3. 并行获取本地和在线数据
            local_search_task = local_search_service.search_local(
                keyword=keyword,
                pan_type=pan_type,
                file_type=normalized_file_type,
                exact=exact,
                time_filter=time_filter,  # 添加时间过滤参数
            )
            online_search_task = fetch_online_results(
                keyword,
                pan_type,
                request.headers,
                normalized_file_type,
                exact,
            )
            results = await asyncio.gather(
                local_search_task, online_search_task, return_exceptions=True
            )

            # 4. 拆解和处理结果
            local_data = (
                results[0]
                if not isinstance(results[0], Exception)
                else {"results": [], "totals": 0}
            )
            online_data = (
                results[1]
                if not isinstance(results[1], Exception)
                else {"results": [], "total_items": 0}
            )

            if isinstance(results[0], Exception):
                logger.error(f"本地搜索异常: {results[0]}")
            if isinstance(results[1], Exception):
                logger.error(f"在线搜索异常: {results[1]}")

            # 5. 合并与去重 (在线结果去重)
            local_results = local_data.get("results", [])
            online_results = online_data.get("results", [])

            local_keys = {
                res.get("resource_key")
                for res in local_results
                if res.get("resource_key")
            }
            deduplicated_online_results = [
                res
                for res in online_results
                if not res.get("resource_key")
                or res.get("resource_key") not in local_keys
            ]

            # 标记本地结果，以便在处理时区分，并统一时间字段
            for item in local_results:
                item["is_local"] = True
                # 本地结果已经有 updated_at 字段，不需要额外处理

            # 为联网搜索结果统一时间字段（兼容处理，因为爬虫已经统一使用updated_at）
            for item in deduplicated_online_results:
                if item.get("publish_time") and not item.get("updated_at"):
                    # 兼容旧版本爬虫可能仍使用publish_time的情况
                    item["updated_at"] = parse_publish_time(item["publish_time"])
                elif not item.get("updated_at"):
                    # 设置默认时间为当前时间
                    item["updated_at"] = parse_publish_time(None)

            combined_results = local_results + deduplicated_online_results

            # 6. 应用时间过滤
            time_filter_str = (
                time_filter.value
                if isinstance(time_filter, TimeFilter)
                else time_filter
            )
            if time_filter_str != "all":
                combined_results = filter_results_by_time(
                    combined_results, time_filter_str
                )

            # 7. 统一评分和排序，此函数内部会处理在线结果的入库
            sorted_results = await process_search_results(
                combined_results,
                keyword,
                normalized_file_type,
                exact,
                sort_by,
                sort_order,
            )

            # 7. 计算总数 (用于前端分页)
            online_total_raw = online_data.get("total_items", 0)
            online_total_capped = 30 if online_total_raw > 30 else online_total_raw
            # 最终总数以去重合并后的列表长度为准，更精确
            total_items = len(sorted_results)

            # 8. 缓存完整结果
            search_results_cache[cache_key] = {
                "results": sorted_results,
                "total": total_items,
                "timestamp": time.time(),
            }
            logger.info(f"结果已缓存: {cache_key}, 总数: {total_items}")

        # 9. 从完整结果中分页
        paginated_results, total_for_response = paginate_results(
            sorted_results, page, limit
        )

        logger.info(
            f"搜索完成: k='{keyword}', p={page}, "
            f"返回={len(paginated_results)}/{total_for_response}, "
            f"耗时={(time.time()-start_time):.2f}s"
        )

        return SearchResponse(
            status="success",
            message="搜索成功" if paginated_results else "未找到相关资源",
            total=total_for_response,
            results=paginated_results,
        )

    except Exception as e:
        logger.error(f"搜索主流程发生严重异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"搜索过程中发生错误: {str(e)}")


async def fetch_online_results(
    keyword: str,
    pan_type: Optional[int],
    headers: Dict,
    file_type: Optional[str] = None,
    exact: bool = False,
):
    """
    执行在线爬虫搜索，固定只搜索第一页。
    """
    search_tasks = []
    search_types = []

    panku8_enabled = settings.get("panku8_crawler.enabled", True)
    esoua_enabled = settings.get("esoua_crawler.enabled", True)

    # 1. esoua爬虫
    if esoua_enabled:
        logger.info(f"添加esoua爬虫任务: k={keyword}, page=1, exact={exact}")
        search_tasks.append(
            esoua_crawler.search(
                keyword,
                pan_type,
                page=1,
                limit=CRAWLER_LIMIT,
                headers=headers,
                file_type=file_type,
                exact=exact,
            )
        )
        search_types.append("esoua")

    # 2. panku8爬虫 (仅在非精确、无文件类型筛选时启用)
    should_use_panku8 = panku8_enabled and not file_type and not exact
    if should_use_panku8:
        logger.info(f"添加panku8爬虫任务: k={keyword}, page=1")
        search_tasks.append(
            panku8_crawler.search(
                keyword, pan_type, page=1, limit=CRAWLER_LIMIT, headers=headers
            )
        )
        search_types.append("panku8")

    if not search_tasks:
        return {"results": [], "total_items": 0}

    # 3. 并发执行
    task_results = await asyncio.gather(*search_tasks, return_exceptions=True)

    # 4. 合并结果
    all_results = []
    total_items = 0

    logger.info(f"所有结果{all_results}")

    logger.info(f"=== 各爬虫搜索结果详情 ===")
    for i, res in enumerate(task_results):
        crawler_type = search_types[i]
        if isinstance(res, Exception):
            logger.error(f"{crawler_type}爬虫任务异常: {res}")
            continue

        if res.get("status") == "success":
            new_results = res.get("results", [])
            all_results.extend(new_results)
            total_items += res.get("totals", 0)
            logger.info(
                f"{crawler_type} 返回 {len(new_results)} 条结果, 总数: {res.get('totals', 0)}"
            )

    return {"results": all_results, "total_items": total_items}


async def process_search_results(
    all_combined_results: List[Dict[str, Any]],
    keyword: str,
    file_type: Optional[str] = None,
    exact: bool = False,
    sort_by: str = "relevance",
    sort_order: str = "desc",
) -> List[SearchResult]:
    """
    处理和排序所有搜索结果。
    该版本统一使用 relevance_ranking.py 进行评分和排序。
    在线获取的结果会被异步存入数据库。
    """
    if not all_combined_results:
        return []

    logger.info(f"开始处理和评分 {len(all_combined_results)} 条合并后的结果...")

    # 步骤1: 过滤掉缺少标题或关键链接/标识的结果
    valid_results = [
        r
        for r in all_combined_results
        if r.get("title")
        and (
            r.get("link")
            or r.get("share_url")
            or r.get("resource_key")
            or r.get("original_url")
            or r.get("resource_id")
        )
    ]
    if len(valid_results) != len(all_combined_results):
        logger.debug(
            f"过滤后剩余 {len(valid_results)}/{len(all_combined_results)} 条有效结果"
        )

    # 步骤2: 使用新的高级权重排序算法
    logger.info("使用新的高级权重排序算法")
    from app.utils.advanced_ranking_service import AdvancedRankingService

    ranking_service = AdvancedRankingService()
    sorted_results = await ranking_service.rank_search_results(
        results=valid_results,
        search_term=keyword,
        filters={
            "file_type": file_type,
            "exact": exact,
            "sort_by": sort_by,
            "sort_order": sort_order,
        },
    )

    # 步骤4: 格式化为 SearchResult 模型, 并分离待入库数据
    final_results_api = []
    online_results_to_db = []

    for item in sorted_results:
        # --- 构造用于入库的数据 (仅针对非本地结果) ---
        if not item.get("is_local"):
            # 直接使用原始item字典构造，因为它包含了所有需要入库的字段
            db_item = {
                "resource_key": item.get("resource_id") or item.get("resource_key"),
                "pan_type": item.get("pan_type"),
                "original_url": item.get("original_url") or "",
                "title": item.get("title"),
                "file_type": get_normalized_file_type(
                    item.get("file_type")
                    or infer_file_type(
                        item.get("title", ""), item.get("text_content", "")
                    )
                ),
                "file_size": item.get("file_size"),
                "text_content": item.get("text_content"),
                "created_at": datetime.now(BEIJING_TIMEZONE),
                "updated_at": item.get("updated_at"),
            }
            online_results_to_db.append(db_item)

        title_str = item.get("title", "未知标题")
        resource_id = item.get("resource_id") or item.get("resource_key")

        # 创建API响应模型
        result_model = SearchResult(
            resource_id=resource_id,
            title=title_str,
            file_name=title_str,
            pan_type=item.get("pan_type"),
            file_type=item.get("file_type")
            or infer_file_type(title_str, item.get("text_content", "")),
            file_size=item.get("file_size") or "未知大小",
            updated_at=item.get("updated_at"),
            update_time=format_update_time(item.get("updated_at")),  # 统一的时间字段
            text_content=item.get("text_content", ""),
            relevance_score=item.get("relevance_score", 0),
        )
        final_results_api.append(result_model)

    # 步骤5: 异步将处理好的在线结果存入数据库
    if online_results_to_db:
        logger.info(
            f"准备将 {len(online_results_to_db)} 条处理过的在线结果异步存入数据库..."
        )
        await add_to_db_queue(online_results_to_db)
        await batch_save_to_db(force=True)

    logger.info(f"最终处理完成，返回 {len(final_results_api)} 条结果。")
    return final_results_api


def extract_search_keywords(keyword):
    """从搜索词提取关键词和权重"""
    stopwords = {"的", "了", "和", "与", "或", "等", "是", "在", "以", "及"}
    processed_keywords = []
    keyword_weights = {}

    # 添加原始关键词
    if len(keyword) >= 2:
        processed_keywords.append(keyword)
        keyword_weights[keyword] = 10.0

    # 分词处理
    keywords = list(jieba.cut_for_search(keyword, HMM=False))
    for kw in keywords:
        if kw not in stopwords and len(kw) >= 2:
            processed_keywords.append(kw)
            keyword_weights[kw] = min(1.0 + len(kw) * 0.5, 5.0)

    # 兜底处理
    if not processed_keywords and len(keyword) >= 2:
        processed_keywords.append(keyword[:2])
        keyword_weights[keyword[:2]] = 1.0

    return {
        "keywords": processed_keywords,
        "keyword_weights": keyword_weights,
    }


def paginate_results(results, page, limit):
    """对结果进行分页处理"""
    if not results:
        logger.info("分页处理: 结果为空，返回空列表")
        return [], 0

    total_items = len(results)
    start_idx = (page - 1) * limit
    if start_idx >= total_items:
        # 如果请求的页码超出范围，直接返回空列表
        logger.warning(f"分页处理: 请求页码 {page} 超出总页数，返回空。")
        return [], total_items

    end_idx = start_idx + limit
    page_results = results[start_idx:end_idx]

    logger.info(
        f"分页处理: 总数={total_items}, 页码={page}, 限制={limit}, "
        f"切片索引=[{start_idx}:{end_idx}], 返回数量={len(page_results)}"
    )

    return page_results, total_items
