# 配置管理API使用说明 - 精简版

## 概述

配置管理模块提供了精简而高效的系统配置管理功能，专注于配置查询和修改的核心需求。该模块基于 `app/config.yaml` 文件，提供安全、便捷的配置管理界面。

## 功能特性

### 🔧 核心功能
- **配置查询**: 支持分类查询和搜索
- **配置更新**: 单个和批量配置更新
- **配置分类**: 13个功能导向的配置分类
- **热加载机制**: 配置变更后无需重启服务

### 🛡️ 安全特性
- **权限控制**: 系统配置权限管理
- **敏感信息遮蔽**: 自动遮蔽敏感配置

### ⚡ 高级特性
- **配置分类**: 按功能模块分类管理
- **生效机制**: 支持立即生效和重新加载
- **批量操作**: 支持批量配置更新

## API端点总览

| 序号 | 方法 | 端点 | 功能 | 状态 |
|------|------|------|------|------|
| 1 | GET | `/api/admin/config` | 获取配置列表 | ✅ |
| 2 | GET | `/api/admin/config/{key}` | 获取配置详情 | ✅ |
| 3 | PUT | `/api/admin/config/{key}` | 更新配置项 | ✅ |
| 4 | POST | `/api/admin/config/batch-update` | 批量更新配置 | ✅ |
| 5 | GET | `/api/admin/config/categories` | 获取配置分类 | ✅ |

## API详细说明

### 1. 获取配置列表
```http
GET /api/admin/config
```

**参数:**
- `category` (可选): 配置分类筛选
- `search` (可选): 搜索配置项
- `show_sensitive` (可选): 是否显示敏感信息

**响应:**
```json
{
  "categories": [
    {
      "name": "app",
      "display_name": "应用程序基本信息",
      "description": "应用程序基本信息配置",
      "icon": "📱",
      "configs": [
        {
          "key": "app.name",
          "display_name": "Name",
          "value": "pan-so-backend",
          "type": "str",
          "required": false,
          "sensitive": false,
          "description": "app.name配置项",
          "validation_rules": {},
          "effect_type": "immediate"
        }
      ]
    }
  ],
  "total_count": 27,
  "restart_required": false
}
```

### 2. 获取配置详情
```http
GET /api/admin/config/{config_key}
```

**参数:**
- `config_key`: 配置项键名（如：app.name）
- `show_sensitive` (可选): 是否显示敏感信息

**响应:**
```json
{
  "status": "success",
  "data": {
    "key": "app.name",
    "display_name": "Name",
    "value": "pan-so-backend",
    "type": "str",
    "required": false,
    "sensitive": false,
    "description": "app.name配置项",
    "validation_rules": {},
    "effect_type": "immediate"
  }
}
```

### 3. 更新配置项
```http
PUT /api/admin/config/{config_key}
```

**请求体:**
```json
{
  "value": "new_value",
  "comment": "更新说明"
}
```

**响应:**
```json
{
  "status": "success",
  "message": "配置更新成功",
  "data": {
    "key": "app.name",
    "old_value": "old_value",
    "new_value": "new_value",
    "hot_reload_success": true,
    "effect_type": "immediate"
  }
}
```

### 4. 批量更新配置
```http
POST /api/admin/config/batch-update
```

**请求体:**
```json
{
  "updates": [
    {
      "key": "app.name",
      "value": "new_name",
      "comment": "更新应用名称"
    },
    {
      "key": "app.debug",
      "value": true,
      "comment": "开启调试模式"
    }
  ]
}
```

**响应:**
```json
{
  "status": "success",
  "message": "批量配置更新成功，共更新 2 项",
  "data": {
    "total": 2,
    "results": [
      {
        "key": "app.name",
        "status": "success",
        "old_value": "old_name",
        "new_value": "new_name",
        "effect_type": "immediate"
      },
      {
        "key": "app.debug",
        "status": "success",
        "old_value": false,
        "new_value": true,
        "effect_type": "immediate"
      }
    ]
  }
}
```

### 5. 获取配置分类
```http
GET /api/admin/config/categories
```

**响应:**
```json
{
  "status": "success",
  "data": {
    "categories": [
      {
        "name": "app",
        "display_name": "应用程序基本信息",
        "description": "应用程序基本信息配置",
        "icon": "📱"
      },
      {
        "name": "database",
        "display_name": "数据库配置",
        "description": "数据库连接和相关配置",
        "icon": "🗄️"
      }
    ],
    "total": 13
  }
}
```

## 配置分类

系统提供13个配置分类：

| 序号 | 分类键 | 显示名称 | 图标 | 描述 |
|------|--------|----------|------|------|
| 1 | `app` | 应用程序基本信息 | 📱 | 应用程序基本信息配置 |
| 2 | `database` | 数据库配置 | 🗄️ | 数据库连接和相关配置 |
| 3 | `meilisearch` | Meilisearch配置 | 🔍 | Meilisearch搜索引擎配置 |
| 4 | `redis` | Redis配置 | 🔴 | Redis连接配置 |
| 5 | `celery` | Celery配置 | 🔄 | Celery任务队列配置 |
| 6 | `cache` | 缓存配置 | ⚡ | 缓存大小和过期时间配置 |
| 7 | `api` | API服务配置 | 🌐 | API服务监听和CORS配置 |
| 8 | `pan_service` | 网盘服务配置 | ☁️ | 网盘服务超时和重试配置 |
| 9 | `pan_accounts` | 网盘账户配置 | 💾 | 网盘账户信息配置 |
| 10 | `crawlers` | 爬虫配置 | 🕷️ | 各种爬虫相关配置 |
| 11 | `email` | 邮箱服务配置 | 📧 | SMTP邮箱服务配置 |
| 12 | `logging` | 日志配置 | 📝 | 日志级别和格式配置 |
| 13 | `security` | 屏蔽词配置 | 🛡️ | 安全屏蔽词配置 |

## 配置生效机制

- **immediate**: 立即生效，无需重启
- **reload_required**: 需要重新加载相关模块

## 使用示例

### Python 客户端示例

```python
import requests

# 获取所有配置
response = requests.get("/api/admin/config")
configs = response.json()

# 获取特定分类的配置
response = requests.get("/api/admin/config?category=app")
app_configs = response.json()

# 更新单个配置
data = {"value": "new_value", "comment": "更新说明"}
response = requests.put("/api/admin/config/app.name", json=data)

# 批量更新配置
data = {
    "updates": [
        {"key": "app.name", "value": "new_name"},
        {"key": "app.debug", "value": True}
    ]
}
response = requests.post("/api/admin/config/batch-update", json=data)
```

### JavaScript 客户端示例

```javascript
// 获取配置列表
const configs = await fetch('/api/admin/config').then(r => r.json());

// 更新配置
const updateConfig = async (key, value) => {
  const response = await fetch(`/api/admin/config/${key}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ value, comment: '前端更新' })
  });
  return response.json();
};

// 批量更新
const batchUpdate = async (updates) => {
  const response = await fetch('/api/admin/config/batch-update', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ updates })
  });
  return response.json();
};
```

## 权限要求

所有配置管理API都需要 `RequireSystemConfig` 权限，确保只有授权用户才能访问和修改系统配置。

## 注意事项

1. **敏感信息**: 密码、密钥等敏感信息会自动遮蔽显示
2. **热加载**: 大部分配置支持热加载，无需重启服务
3. **权限控制**: 需要系统配置权限才能访问API
4. **数据验证**: 系统会自动验证配置值的类型和格式

## 错误处理

API使用标准HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 配置项不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```