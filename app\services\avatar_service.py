"""
头像服务
处理用户头像上传、存储、转换等功能
"""

import os
import uuid
import aiofiles
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import UploadFile, HTTPException
from PIL import Image
import io
import logging

from app.models.user import User, UserAvatar
from app.utils.config import settings

logger = logging.getLogger(__name__)


class AvatarService:
    """头像服务类"""

    # 支持的图片格式
    ALLOWED_FORMATS = {"jpg", "jpeg", "png", "gif", "webp"}

    # 最大文件大小 (5MB)
    MAX_FILE_SIZE = 5 * 1024 * 1024

    # 头像尺寸配置
    AVATAR_SIZE = 400
    AVATAR_QUALITY = 80

    @classmethod
    async def upload_avatar(
        cls,
        user: User,
        file: UploadFile,
        ip_address: str = None,
        user_agent: str = None,
    ) -> Dict[str, Any]:
        """
        上传用户头像

        Args:
            user: 用户对象
            file: 上传的文件
            ip_address: 上传IP地址
            user_agent: 用户代理

        Returns:
            Dict: 头像信息
        """
        # 检查每日上传限制
        max_daily_uploads = settings.get("avatar.max_daily_uploads", 3)
        can_upload = await user.check_daily_avatar_upload_limit(max_daily_uploads)

        if not can_upload:
            today_count = await user.get_today_avatar_upload_count()
            raise ValueError(
                f"您今日已上传 {today_count} 次头像，每日最多只能上传 {max_daily_uploads} 次，请明天再试"
            )

        # 验证文件
        await cls._validate_file(file)

        # 读取文件内容
        file_content = await file.read()

        # 处理图片（转换为WebP格式并调整尺寸）
        processed_image, file_info = await cls._process_image(
            file_content, file.filename
        )

        # 生成文件路径
        file_path = cls._generate_file_path(user.id)

        # 保存文件
        storage_type = settings.get("avatar.storage_type", "local")
        cdn_url = None

        if storage_type == "r2":
            # 使用Cloudflare R2存储
            cdn_url = await cls._save_to_r2(file_path, processed_image)
        else:
            # 使用本地存储
            await cls._save_to_local(file_path, processed_image)

        # 停用旧头像
        await user.deactivate_all_avatars()

        # 创建头像记录
        avatar = await UserAvatar.create(
            user=user,
            original_filename=file.filename,
            file_path=file_path,
            file_size=len(processed_image),
            mime_type="image/webp",
            width=cls.AVATAR_SIZE,
            height=cls.AVATAR_SIZE,
            is_active=True,
            storage_type=storage_type,
            cdn_url=cdn_url,
        )

        # 记录上传日志
        await user.record_avatar_upload(ip_address=ip_address, user_agent=user_agent)

        # 异步清理旧头像文件
        try:
            await user.cleanup_old_avatars(keep_recent=1)
        except Exception as e:
            logger.warning(f"清理旧头像失败: {e}")

        logger.info(f"用户 {user.username} 上传头像成功: {file_path}")

        return {
            "avatar_id": avatar.id,
            "file_path": file_path,
            "cdn_url": cdn_url,
            "file_size": len(processed_image),
            "width": cls.AVATAR_SIZE,
            "height": cls.AVATAR_SIZE,
            "mime_type": "image/webp",
            "storage_type": storage_type,
        }

    @classmethod
    async def _validate_file(cls, file: UploadFile):
        """验证上传文件"""
        # 检查文件格式
        if not file.filename:
            raise ValueError("文件名不能为空")

        file_ext = file.filename.lower().split(".")[-1]
        if file_ext not in cls.ALLOWED_FORMATS:
            raise ValueError(
                f"不支持的文件格式，仅支持: {', '.join(cls.ALLOWED_FORMATS)}"
            )

        # 检查文件大小
        if file.size and file.size > cls.MAX_FILE_SIZE:
            raise ValueError(f"文件大小不能超过 {cls.MAX_FILE_SIZE // (1024*1024)}MB")

    @classmethod
    async def _process_image(
        cls, file_content: bytes, filename: str
    ) -> tuple[bytes, Dict[str, Any]]:
        """处理图片：转换格式、调整尺寸"""
        try:
            # 打开图片
            image = Image.open(io.BytesIO(file_content))

            # 转换为RGB模式（WebP需要）
            if image.mode in ("RGBA", "LA", "P"):
                # 创建白色背景
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "P":
                    image = image.convert("RGBA")
                background.paste(
                    image,
                    mask=image.split()[-1] if image.mode in ("RGBA", "LA") else None,
                )
                image = background
            elif image.mode != "RGB":
                image = image.convert("RGB")

            # 调整尺寸为正方形
            image = cls._resize_to_square(image, cls.AVATAR_SIZE)

            # 转换为WebP格式
            output = io.BytesIO()
            image.save(output, format="WebP", quality=cls.AVATAR_QUALITY, optimize=True)
            processed_content = output.getvalue()

            file_info = {
                "original_size": len(file_content),
                "processed_size": len(processed_content),
                "width": image.width,
                "height": image.height,
                "format": "WebP",
            }

            return processed_content, file_info

        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            raise ValueError("图片处理失败，请确保上传的是有效的图片文件")

    @classmethod
    def _resize_to_square(cls, image: Image.Image, size: int) -> Image.Image:
        """将图片调整为正方形"""
        # 获取原始尺寸
        width, height = image.size

        # 计算裁剪区域（居中裁剪）
        if width > height:
            # 宽图，裁剪左右
            left = (width - height) // 2
            top = 0
            right = left + height
            bottom = height
        else:
            # 高图，裁剪上下
            left = 0
            top = (height - width) // 2
            right = width
            bottom = top + width

        # 裁剪为正方形
        square_image = image.crop((left, top, right, bottom))

        # 调整到目标尺寸
        return square_image.resize((size, size), Image.Resampling.LANCZOS)

    @classmethod
    def _generate_file_path(cls, user_id: int) -> str:
        """生成文件路径"""
        now = datetime.now()
        date_path = now.strftime("%Y%m%d")
        filename = f"{uuid.uuid4().hex}.webp"
        return f"avatars/{date_path}/{user_id}/{filename}"

    @classmethod
    async def _save_to_local(cls, file_path: str, content: bytes):
        """保存到本地存储"""
        local_path = settings.get("avatar.local_path", "uploads")
        full_path = os.path.join(local_path, file_path)

        # 确保目录存在
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        # 保存文件
        async with aiofiles.open(full_path, "wb") as f:
            await f.write(content)

    @classmethod
    async def _save_to_r2(cls, file_path: str, content: bytes) -> str:
        """保存到Cloudflare R2存储"""
        try:
            import httpx

            # 获取R2配置
            r2_config = settings.get("avatar.r2", {})
            api_token = r2_config.get("api_token")
            account_id = r2_config.get("account_id")
            bucket_name = r2_config.get("bucket_name")
            public_url = r2_config.get("public_url")

            if not all([api_token, account_id, bucket_name]):
                raise ValueError("R2配置不完整")

            # 构建上传URL
            upload_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/r2/buckets/{bucket_name}/objects/{file_path}"

            # 上传文件
            headers = {
                "Authorization": f"Bearer {api_token}",
                "Content-Type": "image/webp",
            }

            async with httpx.AsyncClient() as client:
                response = await client.put(
                    upload_url, content=content, headers=headers
                )
                response.raise_for_status()

            # 返回CDN URL
            cdn_url = f"{public_url}/{file_path}" if public_url else None
            logger.info(f"文件上传到R2成功: {file_path}")
            return cdn_url

        except Exception as e:
            logger.error(f"R2上传失败: {e}")
            # 降级到本地存储
            await cls._save_to_local(file_path, content)
            return None

    @classmethod
    async def _delete_file(cls, file_path: str, storage_type: str = "local"):
        """删除文件"""
        try:
            if storage_type == "r2":
                await cls._delete_from_r2(file_path)
            else:
                await cls._delete_from_local(file_path)
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {e}")

    @classmethod
    async def _delete_from_local(cls, file_path: str):
        """从本地存储删除文件"""
        local_path = settings.get("avatar.local_path", "uploads")
        full_path = os.path.join(local_path, file_path)

        if os.path.exists(full_path):
            os.remove(full_path)

    @classmethod
    async def _delete_from_r2(cls, file_path: str):
        """从R2存储删除文件"""
        try:
            import httpx

            # 获取R2配置
            r2_config = settings.get("avatar.r2", {})
            api_token = r2_config.get("api_token")
            account_id = r2_config.get("account_id")
            bucket_name = r2_config.get("bucket_name")

            if not all([api_token, account_id, bucket_name]):
                return

            # 构建删除URL
            delete_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/r2/buckets/{bucket_name}/objects/{file_path}"

            # 删除文件
            headers = {"Authorization": f"Bearer {api_token}"}

            async with httpx.AsyncClient() as client:
                response = await client.delete(delete_url, headers=headers)
                response.raise_for_status()

        except Exception as e:
            logger.error(f"R2删除失败: {e}")
