#!/usr/bin/env python3
"""
创建测试用户
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.user import User, Role
from app.services.points_service import PointsService
from app.core.tortoise_config import TORTOISE_ORM
from tortoise import Tortoise


async def create_test_user():
    """创建测试用户"""

    # 初始化数据库连接
    await Tortoise.init(config=TORTOISE_ORM)

    try:
        print("🧪 开始创建测试用户...")

        # 1. 获取用户角色
        user_role = await Role.filter(name="user").first()
        if not user_role:
            print("❌ 找不到用户角色")
            return

        # 2. 检查是否已存在测试用户
        existing_user = await User.filter(username="testuser").first()
        if existing_user:
            print("📝 测试用户已存在，更新状态...")
            existing_user.status = "active"
            existing_user.email_verified = True
            await existing_user.save()
            print(f"✅ 更新用户状态成功: {existing_user.username}")
        else:
            # 3. 创建新的测试用户
            print("📝 创建新的测试用户...")

            # 生成密码哈希
            password_hash = User.hash_password("123456")

            # 创建用户
            user = await User.create(
                username="testuser",
                email="<EMAIL>",
                password_hash=password_hash,
                nickname="测试用户",
                role=user_role,
                status="active",
                email_verified=True,
                points=10,  # 默认注册积分
            )

            print(f"✅ 创建用户成功: {user.username}")
            print(f"📧 邮箱: {user.email}")
            print(f"💰 积分: {user.points}")
            print(f"🏆 头衔: {PointsService.get_title_by_points(user.points)}")

            # 4. 创建注册积分记录
            try:
                await PointsService.register_bonus(user)
                print("✅ 注册积分记录创建成功")
            except Exception as e:
                print(f"⚠️ 注册积分记录创建失败: {e}")

        # 5. 显示所有活跃用户
        print("\n📊 当前活跃用户列表:")
        active_users = await User.filter(status="active").prefetch_related("role")
        for user in active_users:
            print(
                f"  - {user.username} ({user.email}) - {user.points}积分 - {PointsService.get_title_by_points(user.points)}"
            )

        print("\n🎉 测试用户创建完成！")
        print("登录信息:")
        print("  用户名: testuser")
        print("  密码: 123456")

    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(create_test_user())
