import re
from datetime import datetime, timedelta, timezone
import logging
from typing import Any

logger = logging.getLogger(__name__)

BEIJING_TIMEZONE = timezone(timedelta(hours=8))


def calculate_expiry_date(days=7):
    """计算过期日期，默认7天后"""
    return datetime.now() + timedelta(days=days)


def format_size(size_in_bytes):
    """格式化文件大小显示"""
    if not size_in_bytes or size_in_bytes == 0:
        return "未知"

    # 转换为KB
    size_in_kb = size_in_bytes / 1024

    if size_in_kb < 1024:
        return f"{size_in_kb:.2f} KB"

    # 转换为MB
    size_in_mb = size_in_kb / 1024

    if size_in_mb < 1024:
        return f"{size_in_mb:.2f} MB"

    # 转换为GB
    size_in_gb = size_in_mb / 1024
    return f"{size_in_gb:.2f} GB"


def format_time(timestamp):
    """格式化时间戳为可读格式"""
    if not timestamp:
        return "未知"

    try:
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logger.error(f"时间格式化错误: {str(e)}")
        return "未知"


def parse_time_string(time_str: str | None) -> datetime | None:
    """
    将多种格式的时间字符串转换为带时区的datetime对象。

    支持的格式:
    - 相对时间: "x分钟前", "x小时前", "x天前"
    - 绝对时间: "YYYY-MM-DD" 或 "YYYY-MM-DD HH:MM:SS"

    :param time_str: 要解析的时间字符串。
    :return: 转换后的北京时区datetime对象，如果无法解析则返回None。
    """
    if not time_str:
        return None

    now = datetime.now(BEIJING_TIMEZONE)
    time_str = time_str.strip()

    # 1. 匹配相对时间
    if "前" in time_str:
        if "分钟" in time_str:
            try:
                minutes = int(re.search(r"(\d+)", time_str).group(1))
                return now - timedelta(minutes=minutes)
            except (ValueError, AttributeError):
                pass
        elif "小时" in time_str:
            try:
                hours = int(re.search(r"(\d+)", time_str).group(1))
                return now - timedelta(hours=hours)
            except (ValueError, AttributeError):
                pass
        elif "天" in time_str:
            try:
                days = int(re.search(r"(\d+)", time_str).group(1))
                return now - timedelta(days=days)
            except (ValueError, AttributeError):
                pass

    # 2. 匹配绝对时间 "YYYY-MM-DD" 或 "YYYY-MM-DD HH:MM:SS"
    try:
        if " " in time_str and ":" in time_str:
            # 格式: YYYY-MM-DD HH:MM:SS
            dt_naive = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        else:
            # 格式: YYYY-MM-DD
            dt_naive = datetime.strptime(time_str, "%Y-%m-%d")
        return dt_naive.replace(tzinfo=BEIJING_TIMEZONE)
    except ValueError:
        pass

    logger.warning(f"无法解析时间字符串: '{time_str}'")
    return None


def get_file_type_from_title(title: str) -> str:
    """根据文件名判断文件类型"""
    if not title:
        return "unknown"

    # 去除文件名中的特殊字符
    title = title.lower().strip()

    # 视频文件
    if re.search(
        r"\.(mp4|mkv|avi|flv|mov|wmv|m4v|rmvb|3gp|webm|ogg|ogv|m3u8|mpd|mpg|mpeg|mpe|mpv|m2v|mxf|3g2|f4[pvab]|swf)$",
        title,
    ):
        return "video"

    # 音频文件
    if re.search(r"\.(mp3|wav|flac|ape|aac|ogg|m4a)$", title):
        return "audio"

    # 图片文件
    if re.search(r"\.(jpg|jpeg|png|gif|bmp|webp|svg|raw|heic|ico)$", title):
        return "image"

    # 文档文件
    if re.search(
        r"\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|md|csv|go|html|css|js|json|xml|ya?ml|epub|mobi|azw3|azw)$",
        title,
    ):
        return "document"

    # 压缩文件
    if re.search(r"\.(zip|rar|7z|tar|gz|xz|bz2)$", title):
        return "archive"

    # 软件/应用
    if re.search(r"\.(exe|msi|dmg|apk|ipa|deb|rpm|bat|sh|py|jar|iso|pkg)$", title):
        return "application"

    # 通过内容关键词判断
    if re.search(
        r"(视频|影片|电影|电视剧|国漫|动漫|纪录片|综艺|mv|1080p|720p|4k|2160p|蓝光|超清|高清|标清|60fps|uhd|hd|bd|dvd|剧集|季|集|movie|film|video|episode|series|tv|show|anime)",
        title,
    ):
        return "video"

    if re.search(
        r"(音乐|歌曲|专辑|无损|mp3|flac|wav|音频|有声书|广播剧|演唱会|合集|歌手|music|song|album|audio)",
        title,
    ):
        return "audio"

    if re.search(
        r"(图片|照片|截图|壁纸|写真|相册|图集|摄影|拍摄|相机|raw|照相|image|photo|picture|gallery)",
        title,
    ):
        return "image"

    if re.search(
        r"(文档|文稿|报告|表格|幻灯片|论文|小说|电子书|教程|课件|书籍|资料|教材|笔记|doc|pdf|document|book|ebook|tutorial)",
        title,
    ):
        return "document"

    if re.search(
        r"(压缩包|打包|解压|rar|zip|7z|压缩文件|归档|archive|compressed)", title
    ):
        return "archive"

    if re.search(
        r"(软件|程序|app|应用|安装包|游戏|破解版|绿色版|安装程序|软件包|汉化版|模拟器|软件工具|software|application|program|game)",
        title,
    ):
        return "application"

    # 默认类型
    return "other"


def infer_file_type(title: str, text_content: str) -> str:
    """
    根据标题和内容推断文件类型。
    优先从标题推断，如果标题无法确定，则尝试从内容推断。
    """
    file_type = "other"
    # 优先从标题推断
    if title:
        file_type = get_file_type_from_title(title)

    # 如果标题无法推断(即为'other')，再尝试从内容推断
    if file_type == "other" and text_content:
        content_type = get_file_type_from_title(text_content)
        # 只有当内容推断出更明确的类型时才覆盖
        if content_type != "other":
            return content_type

    return file_type


def get_normalized_file_type(file_type: str) -> str:
    """
    标准化文件类型

    将各种文件类型统一转换为标准类型：
    - video: 视频文件
    - audio: 音频文件
    - image: 图片文件
    - document: 文档文件
    - archive: 压缩文件
    - application: 应用程序
    - other: 其他类型
    """
    if not file_type:
        return "other"

    file_type = file_type.lower().strip()

    # 中文类型映射
    if file_type in ["视频", "影片", "电影"]:
        return "video"

    if file_type in ["音频", "音乐", "歌曲"]:
        return "audio"

    if file_type in ["图片", "照片", "图像"]:
        return "image"

    if file_type in ["文档", "文件", "文稿"]:
        return "document"

    if file_type in ["压缩", "压缩包", "打包"]:
        return "archive"

    if file_type in ["应用", "软件", "程序"]:
        return "application"

    # 视频类型
    if file_type in [
        "video",
        "mp4",
        "mkv",
        "avi",
        "flv",
        "mov",
        "wmv",
        "m4v",
        "rmvb",
        "3gp",
        "webm",
        "ogg",
        "ogv",
    ]:
        return "video"

    # 音频类型
    if file_type in ["audio", "mp3", "wav", "flac", "ape", "aac", "ogg", "m4a"]:
        return "audio"

    # 图片类型
    if file_type in [
        "image",
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
        "svg",
        "raw",
        "heic",
        "ico",
    ]:
        return "image"

    # 文档类型
    if file_type in [
        "document",
        "pdf",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "ppt",
        "pptx",
        "txt",
        "md",
        "csv",
        "html",
        "css",
        "js",
        "json",
        "xml",
        "yml",
        "yaml",
    ]:
        return "document"

    # 压缩文件类型
    if file_type in ["archive", "zip", "rar", "7z", "tar", "gz", "xz", "bz2"]:
        return "archive"

    # 应用程序类型
    if file_type in [
        "application",
        "exe",
        "msi",
        "dmg",
        "apk",
        "ipa",
        "deb",
        "rpm",
        "bat",
        "sh",
        "py",
        "jar",
        "iso",
        "pkg",
    ]:
        return "application"

    # 其他类型
    return "other"


def get_format_extensions_by_type(file_type: str) -> list:
    """
    根据文件类型获取对应的文件扩展名列表，用于API请求

    参数:
        file_type: 文件类型，如"video", "audio", "document"等

    返回:
        文件扩展名列表，如[".mp4", ".mkv", ...]
    """
    normalized_type = get_normalized_file_type(file_type)

    if normalized_type == "video":
        return [
            ".mp4",
            ".mkv",
            ".flv",
            ".rmvb",
            ".wmv",
            ".3gp",
            ".mov",
            ".m4v",
            ".swf",
            ".f4v",
            ".webm",
            ".ogg",
            ".ogv",
            ".m3u8",
            ".mpd",
            ".avi",
            ".mpg",
            ".mpeg",
            ".mpe",
            ".mpv",
            ".m2v",
            ".mxf",
            ".3g2",
            ".f4p",
            ".f4a",
            ".f4b",
        ]

    elif normalized_type == "audio":
        return [".mp3", ".aac", ".wav", ".flac", ".ape", ".ogg", ".m4a"]

    elif normalized_type == "image":
        return [
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".webp",
            ".ico",
            ".svg",
            ".raw",
            ".heic",
        ]

    elif normalized_type == "document":
        return [
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".pdf",
            ".txt",
            ".md",
            ".go",
            ".html",
            ".css",
            ".js",
            ".json",
            ".xml",
            ".yml",
            ".yaml",
            ".csv",
            ".epub",
            ".mobi",
            ".azw3",
            ".azw",
        ]

    elif normalized_type == "archive":
        return [".rar", ".zip", ".7z", ".tar", ".gz", ".xz", ".bz2"]

    elif normalized_type == "application":
        return [
            ".exe",
            ".msi",
            ".bat",
            ".sh",
            ".py",
            ".jar",
            ".ipa",
            ".apk",
            ".dmg",
            ".iso",
            ".pkg",
            ".deb",
            ".rpm",
        ]

    # 默认返回空列表
    return []


def filter_by_file_type(results, file_type):
    """根据文件类型筛选结果"""
    if not file_type:
        return results

    normalized_type = get_normalized_file_type(file_type)
    logger.info(f"筛选文件类型: {file_type} -> {normalized_type}")

    filtered_results = []
    for item in results:
        item_file_type = item.get("file_type", "")
        item_title = item.get("title", "未知")
        item_text = item.get("text_content", "")

        normalized_item_type = get_normalized_file_type(item_file_type)

        # 如果没有文件类型，尝试从标题和内容推断
        if not item_file_type:
            # 从标题推断
            if item_title:
                title_type = get_file_type_from_title(item_title)
                if title_type != "other":
                    normalized_item_type = title_type

            # 如果标题无法推断，尝试从内容推断
            if normalized_item_type == "other" and item_text:
                content_type = get_file_type_from_title(item_text)
                if content_type != "other":
                    normalized_item_type = content_type

        # 判断是否符合筛选条件
        if normalized_item_type == normalized_type:
            filtered_results.append(item)

    logger.info(f"筛选结果: 原有{len(results)}项，筛选后{len(filtered_results)}项")
    return filtered_results


def parse_publish_time(publish_time_input: Any) -> datetime:
    """解析各种格式的日期时间字符串，返回带时区的datetime（北京时区）"""
    if not publish_time_input:
        return datetime.now(BEIJING_TIMEZONE)

    if isinstance(publish_time_input, datetime):
        if publish_time_input.tzinfo is None:
            # 无时区信息的datetime，假设为北京时间
            return publish_time_input.replace(tzinfo=BEIJING_TIMEZONE)
        # 有时区信息的datetime，转换为北京时区
        return publish_time_input.astimezone(BEIJING_TIMEZONE)

    if isinstance(publish_time_input, (int, float)):
        try:
            # 时间戳通常是UTC，转换为北京时区
            utc_dt = datetime.fromtimestamp(publish_time_input, tz=timezone.utc)
            return utc_dt.astimezone(BEIJING_TIMEZONE)
        except (ValueError, TypeError):
            return datetime.now(BEIJING_TIMEZONE)

    if not isinstance(publish_time_input, str):
        return datetime.now(BEIJING_TIMEZONE)

    date_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M",
        "%Y年%m月%d日",
    ]

    for fmt in date_formats:
        try:
            dt = datetime.strptime(publish_time_input, fmt)
            # 解析出的时间假设为北京时间
            return dt.replace(tzinfo=BEIJING_TIMEZONE)
        except (ValueError, TypeError):
            continue

    return datetime.now(BEIJING_TIMEZONE)


# 定义有效的时间过滤选项
VALID_TIME_FILTERS = ["all", "week", "half_month", "month", "half_year", "year"]


def validate_time_filter(time_filter: str) -> str:
    """
    验证时间过滤参数是否有效

    Args:
        time_filter: 时间过滤选项

    Returns:
        str: 验证后的时间过滤选项，如果无效则返回'all'
    """
    if time_filter not in VALID_TIME_FILTERS:
        logger.warning(f"无效的时间过滤选项: {time_filter}，使用默认值'all'")
        return "all"
    return time_filter


def get_time_filter_range(time_filter: str) -> tuple[datetime, datetime]:
    """
    根据时间过滤参数获取时间范围

    Args:
        time_filter: 时间过滤选项 (all, week, half_month, month, half_year, year)

    Returns:
        tuple: (开始时间, 结束时间) 的元组，如果是'all'则返回(None, None)
    """
    # 验证时间过滤参数
    time_filter = validate_time_filter(time_filter)

    if time_filter == "all":
        return None, None

    # 使用北京时间计算过滤范围，符合用户预期
    now = datetime.now(BEIJING_TIMEZONE)

    time_ranges = {
        "week": 7,
        "half_month": 15,
        "month": 30,
        "half_year": 180,
        "year": 365,
    }

    days = time_ranges.get(time_filter)
    if days is None:
        logger.warning(f"未知的时间过滤选项: {time_filter}，使用默认值'all'")
        return None, None

    start_time = now - timedelta(days=days)
    # 给结束时间增加1天缓冲，确保包含更多相关数据
    end_time = now + timedelta(days=1)
    return start_time, end_time


def filter_results_by_time(results: list, time_filter: str) -> list:
    """
    根据时间过滤参数过滤搜索结果

    Args:
        results: 搜索结果列表
        time_filter: 时间过滤选项

    Returns:
        list: 过滤后的结果列表
    """
    if time_filter == "all" or not results:
        return results

    start_time, end_time = get_time_filter_range(time_filter)
    if start_time is None:
        return results

    filtered_results = []
    for result in results:
        updated_at = result.get("updated_at")
        if not updated_at:
            continue

        try:
            # 解析时间字段
            result_time = None
            if isinstance(updated_at, datetime):
                result_time = updated_at
            elif isinstance(updated_at, str):
                # 尝试解析ISO格式的字符串
                result_time = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
            elif isinstance(updated_at, (int, float)):
                # Unix时间戳
                result_time = datetime.fromtimestamp(updated_at, tz=timezone.utc)

            if result_time:
                # 确保时间有时区信息，统一转换为北京时区进行比较
                if result_time.tzinfo is None:
                    # 无时区信息的时间，假设为北京时间
                    result_time = result_time.replace(tzinfo=BEIJING_TIMEZONE)
                else:
                    # 有时区信息的时间，转换为北京时区
                    result_time = result_time.astimezone(BEIJING_TIMEZONE)

                # start_time和end_time已经是北京时区，确保有时区信息
                if start_time.tzinfo is None:
                    start_time = start_time.replace(tzinfo=BEIJING_TIMEZONE)
                if end_time.tzinfo is None:
                    end_time = end_time.replace(tzinfo=BEIJING_TIMEZONE)

                # 检查是否在时间范围内
                if start_time <= result_time <= end_time:
                    filtered_results.append(result)

        except Exception as e:
            logger.warning(f"解析时间字段失败: {updated_at}, 错误: {e}")
            # 解析失败的结果不包含在过滤结果中
            continue

    logger.info(
        f"时间过滤: {time_filter}, 原始结果: {len(results)}, 过滤后: {len(filtered_results)}"
    )
    return filtered_results


def format_update_time(updated_at: Any) -> str:
    """
    格式化更新时间为统一的字符串格式

    Args:
        updated_at: 时间字段，可以是datetime、字符串或时间戳

    Returns:
        str: 格式化后的时间字符串 (ISO 8601格式)
    """
    if not updated_at:
        return ""

    try:
        result_time = None
        if isinstance(updated_at, datetime):
            result_time = updated_at
        elif isinstance(updated_at, str):
            # 尝试解析ISO格式的字符串
            result_time = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
        elif isinstance(updated_at, (int, float)):
            # Unix时间戳
            result_time = datetime.fromtimestamp(updated_at, tz=timezone.utc)

        if result_time:
            # 确保时间有时区信息
            if result_time.tzinfo is None:
                # 数据库中存储的时间通常是北京时间，不是UTC
                result_time = result_time.replace(tzinfo=BEIJING_TIMEZONE)

            # 返回ISO 8601格式的字符串
            return result_time.isoformat()

    except Exception as e:
        logger.warning(f"格式化时间字段失败: {updated_at}, 错误: {e}")

    return ""
