"""
资源求助相关数据模型
"""

from tortoise import fields, models
from typing import TYPE_CHECKING
from enum import Enum

if TYPE_CHECKING:
    from app.models.user import User


class CloudDiskType(str, Enum):
    """网盘类型枚举"""

    BAIDU = "baidu"
    ALIYUN = "aliyun"
    QUARK = "quark"
    XUNLEI = "xunlei"
    ALL = "all"


class ResourceType(str, Enum):
    """资源类型枚举"""

    MOVIE = "movie"
    TV_SERIES = "tv_series"
    SOFTWARE = "software"
    GAME = "game"
    MUSIC = "music"
    BOOK = "book"
    DOCUMENT = "document"
    OTHER = "other"


class HelpRequestStatus(str, Enum):
    """求助状态枚举"""

    OPEN = "open"  # 开放中
    RESOLVED = "resolved"  # 已解决
    CLOSED = "closed"  # 已关闭


class HelpRequest(models.Model):
    """资源求助表"""

    id = fields.IntField(pk=True)

    # 基本信息
    title = fields.CharField(max_length=200, description="资源名称")
    description = fields.TextField(null=True, description="详细描述")

    # 分类信息
    cloud_disk_types = fields.JSONField(default=list, description="网盘类型列表")
    resource_type = fields.CharEnumField(
        ResourceType, default=ResourceType.OTHER, description="资源类型"
    )

    # 状态信息
    status = fields.CharEnumField(
        HelpRequestStatus, default=HelpRequestStatus.OPEN, description="求助状态"
    )

    # 关联信息
    requester: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User",
        related_name="help_requests",
        on_delete=fields.CASCADE,
        description="求助者",
    )

    # 统计信息
    answer_count = fields.IntField(default=0, description="回答数量")
    view_count = fields.IntField(default=0, description="浏览次数")

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    resolved_at = fields.DatetimeField(null=True, description="解决时间")

    class Meta:
        table = "help_requests"
        ordering = ["-created_at"]

    def __str__(self):
        return f"求助: {self.title}"


class HelpAnswer(models.Model):
    """求助回答表"""

    id = fields.IntField(pk=True)

    # 基本信息
    resource_title = fields.CharField(max_length=200, description="资源标题")
    resource_link = fields.CharField(max_length=500, description="资源链接")
    cloud_disk_type = fields.CharEnumField(CloudDiskType, description="网盘类型")
    additional_info = fields.TextField(null=True, description="补充说明")

    # 入库选项
    should_archive = fields.BooleanField(default=False, description="是否入库")

    # 关联信息
    help_request: fields.ForeignKeyRelation[HelpRequest] = fields.ForeignKeyField(
        "models.HelpRequest",
        related_name="answers",
        on_delete=fields.CASCADE,
        description="关联的求助",
    )
    answerer: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User",
        related_name="help_answers",
        on_delete=fields.CASCADE,
        description="回答者",
    )

    # 状态信息
    is_accepted = fields.BooleanField(default=False, description="是否被采纳")
    accepted_at = fields.DatetimeField(null=True, description="采纳时间")

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "help_answers"
        ordering = ["-created_at"]

    def __str__(self):
        return f"回答: {self.resource_title}"


# Pydantic模型将在需要时动态创建，避免循环导入问题
