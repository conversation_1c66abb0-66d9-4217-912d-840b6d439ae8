from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 为用户表添加积分字段
        ALTER TABLE "users" ADD COLUMN "points" INT NOT NULL DEFAULT 10;
        COMMENT ON COLUMN "users"."points" IS '用户积分';
        
        -- 创建积分变动记录表
        CREATE TABLE IF NOT EXISTS "points_transactions" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "amount" INT NOT NULL,
            "balance_after" INT NOT NULL,
            "transaction_type" VARCHAR(50) NOT NULL,
            "description" VARCHAR(255) NOT NULL,
            "related_id" INT,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE "points_transactions" IS '积分变动记录表';
        COMMENT ON COLUMN "points_transactions"."user_id" IS '关联用户';
        COMMENT ON COLUMN "points_transactions"."amount" IS '积分变动数量（正数为增加，负数为减少）';
        COMMENT ON COLUMN "points_transactions"."balance_after" IS '变动后的积分余额';
        COMMENT ON COLUMN "points_transactions"."transaction_type" IS '交易类型：register/help_request/help_answer/answer_accepted/admin_adjust';
        COMMENT ON COLUMN "points_transactions"."description" IS '变动描述';
        COMMENT ON COLUMN "points_transactions"."related_id" IS '关联的业务ID（如求助ID、回答ID等）';
        COMMENT ON COLUMN "points_transactions"."created_at" IS '创建时间';
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS "idx_points_transactions_user_id" ON "points_transactions" ("user_id");
        CREATE INDEX IF NOT EXISTS "idx_points_transactions_type" ON "points_transactions" ("transaction_type");
        CREATE INDEX IF NOT EXISTS "idx_points_transactions_created_at" ON "points_transactions" ("created_at");
        
        -- 为现有用户创建注册积分记录
        INSERT INTO "points_transactions" ("user_id", "amount", "balance_after", "transaction_type", "description", "created_at")
        SELECT 
            id, 
            10, 
            10, 
            'register', 
            '新用户注册奖励', 
            created_at
        FROM "users" 
        WHERE id NOT IN (SELECT DISTINCT user_id FROM "points_transactions" WHERE transaction_type = 'register');
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除索引
        DROP INDEX IF EXISTS "idx_points_transactions_created_at";
        DROP INDEX IF EXISTS "idx_points_transactions_type";
        DROP INDEX IF EXISTS "idx_points_transactions_user_id";
        
        -- 删除积分变动记录表
        DROP TABLE IF EXISTS "points_transactions";
        
        -- 删除用户表的积分字段
        ALTER TABLE "users" DROP COLUMN IF EXISTS "points";
    """
