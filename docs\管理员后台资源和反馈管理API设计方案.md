# 🎯 **管理员后台资源和反馈管理API设计方案**

> **注意**: 本文档已完成资源管理和反馈管理功能设计。参数配置管理功能已独立设计，详见 `管理员后台参数配置管理模块设计方案.md`

## 📋 **设计约束总结**

### **严格约束条件**
- ❌ **不允许修改数据库表结构**
- ❌ **不允许新增任何数据库字段**
- ✅ **必须基于现有 PanResource 和 ResourceInvalidFeedback 表**
- ✅ **必须复用现有接口**
- ✅ **仅提供反馈查询功能**

### **接口复用要求**
- 🔄 资源查询 → 复用 `/api/cached_resources` 或 `/api/custom_search`
- 🔄 新增资源 → 复用 `/api/submit_resources`
- 🔐 `submit_resources` 需要增加管理员权限校验
- 🆕 `submit_resources` 需要支持 `is_parsed` 标志控制

---

## 🗂️ **1. 资源管理功能设计**

### **1.1 资源列表查询 - 复用现有接口**

#### **方案选择：复用 `/api/cached_resources` 接口**

**原因分析：**
- ✅ 已支持完整的筛选参数（title, pan_type, file_type, valid_only等）
- ✅ 已支持分页和排序
- ✅ 返回格式适合管理员界面
- ✅ 性能已优化（缓存机制）

**管理员专用扩展方案：**
```python
# 新增管理员专用端点，内部调用cached_resources逻辑
GET /api/admin/resources

# 参数映射关系：
# admin参数 -> cached_resources参数
# keyword -> title
# pan_type -> pan_type  
# file_type -> file_type
# status -> valid_only (转换逻辑)
# is_mine -> user (特殊处理)
```

**权限控制实现：**
```python
@router.get("/admin/resources")
async def get_admin_resources(
    # 管理员专用参数
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    pan_type: Optional[int] = Query(None, description="网盘类型"),
    file_type: Optional[str] = Query(None, description="文件类型"),
    status: Optional[str] = Query(None, description="资源状态"),
    is_mine: Optional[bool] = Query(None, description="是否本人上传"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    sort_by: str = Query("updated_at"),
    sort_order: str = Query("desc"),
    
    # 权限验证
    current_user: User = Depends(RequireResourceManage)
):
    """
    管理员资源列表 - 复用cached_resources逻辑
    """
    # 参数转换逻辑
    cached_params = {
        "title": keyword,
        "pan_type": pan_type,
        "file_type": file_type,
        "page": page,
        "limit": size,
        "sort_by": sort_by,
        "sort_order": sort_order
    }
    
    # 状态筛选转换
    if status == "valid":
        cached_params["valid_only"] = True
    elif status == "invalid":
        cached_params["valid_only"] = False
        
    # is_mine筛选处理
    if is_mine is not None:
        cached_params["is_mine_filter"] = is_mine
    
    # 调用现有的cached_resources逻辑
    result = await get_cached_resources_logic(**cached_params)
    
    # 管理员专用响应格式转换
    return format_admin_response(result)
```

### **1.2 资源详情查询 - 复用现有接口**

**直接复用：`/api/resource_details/{resource_key}`**

```python
@router.get("/admin/resources/{resource_key}")
async def get_admin_resource_detail(
    resource_key: str,
    current_user: User = Depends(RequireResourceManage)
):
    """
    管理员资源详情 - 直接复用resource_details
    """
    # 直接调用现有的get_resource_details函数
    detail = await get_resource_details(resource_key)
    
    # 管理员专用信息扩展（基于现有字段计算）
    admin_info = await calculate_admin_info(resource_key)
    
    return {
        "status": "success",
        "data": {
            **detail.dict(),
            "admin_info": admin_info
        }
    }

async def calculate_admin_info(resource_key: str):
    """基于现有字段计算管理员专用信息"""
    # 统计相关反馈数量
    feedback_count = await ResourceInvalidFeedback.filter(
        resource_id=resource_key
    ).count()
    
    # 获取最近反馈时间
    latest_feedback = await ResourceInvalidFeedback.filter(
        resource_id=resource_key
    ).order_by('-created_at').first()
    
    return {
        "feedback_count": feedback_count,
        "latest_feedback_time": latest_feedback.created_at if latest_feedback else None,
        "has_pending_feedback": feedback_count > 0
    }
```

### **1.3 新增资源功能 - 扩展现有接口**

#### **扩展 `/api/submit_resources` 接口**

**现有接口分析：**
```python
# 现有ResourceSubmissionRequest模型
class ResourceSubmissionRequest(BaseModel):
    urls: List[ResourceUrlItem]
    is_mine: Optional[bool] = Field(False, description="是否为管理员提交")
```

**扩展方案：**
```python
# 扩展请求模型（向后兼容）
class ResourceSubmissionRequest(BaseModel):
    urls: List[ResourceUrlItem]
    is_mine: Optional[bool] = Field(False, description="是否为管理员提交")
    
    # 新增字段（可选，保持向后兼容）
    auto_parse: Optional[bool] = Field(True, description="是否自动解析资源详情")
    admin_submit: Optional[bool] = Field(False, description="管理员提交标识")
```

**权限校验逻辑：**
```python
@router.post("/submit_resources")
async def submit_resources_for_processing(
    payload: ResourceSubmissionRequest,
    request: Request,
    # 可选的用户认证（兼容现有逻辑）
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    扩展的资源提交接口
    """
    # 管理员权限校验逻辑
    if payload.is_mine or payload.admin_submit:
        # 如果设置了is_mine或admin_submit，必须有管理员权限
        if not current_user:
            raise HTTPException(401, "需要管理员认证")
            
        user_permissions = await current_user.get_permissions()
        if Permissions.RESOURCE_MANAGE not in user_permissions:
            raise HTTPException(403, "需要资源管理权限")
    
    # 处理auto_parse逻辑
    auto_parse = payload.auto_parse if payload.auto_parse is not None else True
    
    # 调用现有的提交逻辑，传递新参数
    result = await process_submission_with_parse_control(
        payload=payload,
        auto_parse=auto_parse,
        submitted_by="admin" if (payload.is_mine or payload.admin_submit) else "user"
    )
    
    return result
```

**is_parsed标志控制逻辑：**
```python
async def process_submission_with_parse_control(payload, auto_parse, submitted_by):
    """
    带解析控制的提交处理逻辑
    """
    # 复用现有的批处理创建逻辑
    batch = await SubmissionBatch.create(
        submitted_by=submitted_by,
        total_urls_submitted=len(payload.urls)
    )
    
    for url_item in payload.urls:
        # 创建PanResource时控制is_parsed字段
        resource = await PanResource.create(
            resource_key=generate_resource_key(url_item.url),
            original_url=url_item.url,
            is_mine=payload.is_mine or False,
            is_parsed=not auto_parse,  # 如果不自动解析，标记为已解析
            # 其他字段...
        )
        
        # 根据auto_parse决定是否触发后台解析任务
        if auto_parse:
            # 触发现有的后台解析任务
            process_batch_task.delay(batch.id)
        else:
            # 不触发解析，直接标记任务完成
            await mark_task_completed(resource.id)
```

### **1.4 资源删除功能**

```python
@router.delete("/admin/resources/{resource_id}")
async def delete_admin_resource(
    resource_id: int,
    current_user: User = Depends(RequireResourceManage)
):
    """
    删除单个资源
    """
    resource = await PanResource.get_or_none(id=resource_id)
    if not resource:
        raise HTTPException(404, "资源不存在")
    
    # 删除资源
    await resource.delete()
    
    # 删除相关反馈（级联删除）
    await ResourceInvalidFeedback.filter(resource_id=resource.resource_key).delete()
    
    return {"status": "success", "message": "资源删除成功"}

@router.post("/admin/resources/batch-delete")
async def batch_delete_resources(
    resource_ids: List[int],
    current_user: User = Depends(RequireResourceManage)
):
    """
    批量删除资源
    """
    # 获取要删除的资源
    resources = await PanResource.filter(id__in=resource_ids)
    resource_keys = [r.resource_key for r in resources]
    
    # 批量删除
    await PanResource.filter(id__in=resource_ids).delete()
    await ResourceInvalidFeedback.filter(resource_id__in=resource_keys).delete()
    
    return {
        "status": "success", 
        "message": f"成功删除 {len(resources)} 个资源"
    }
```

### **1.5 资源统计功能 - 扩展现有接口**

**复用并扩展 `/api/resource_stats` 接口：**

```python
@router.get("/admin/resources/stats")
async def get_admin_resource_stats(
    current_user: User = Depends(RequireResourceManage)
):
    """
    管理员资源统计 - 扩展现有resource_stats
    """
    # 复用现有的基础统计
    basic_stats = await get_resource_stats()
    
    # 扩展管理员专用统计
    admin_stats = await calculate_admin_stats()
    
    return {
        "status": "success",
        "data": {
            **basic_stats,
            **admin_stats
        }
    }

async def calculate_admin_stats():
    """基于现有字段计算管理员统计"""
    # 按网盘类型统计
    by_pan_type = {}
    for pan_type in [1, 2, 3, 4]:
        count = await PanResource.filter(pan_type=pan_type).count()
        by_pan_type[f"pan_type_{pan_type}"] = count
    
    # 按文件类型统计
    file_type_stats = await PanResource.all().values_list('file_type', flat=True)
    by_file_type = {}
    for file_type in set(filter(None, file_type_stats)):
        count = await PanResource.filter(file_type=file_type).count()
        by_file_type[file_type] = count
    
    # 按验证状态统计
    by_status = {
        "valid": await PanResource.filter(verified_status="valid").count(),
        "invalid": await PanResource.filter(verified_status="invalid").count(),
        "unknown": await PanResource.filter(verified_status__isnull=True).count()
    }
    
    # 按is_mine统计
    by_source = {
        "admin_uploaded": await PanResource.filter(is_mine=True).count(),
        "user_submitted": await PanResource.filter(is_mine=False).count()
    }
    
    return {
        "by_pan_type": by_pan_type,
        "by_file_type": by_file_type,
        "by_status": by_status,
        "by_source": by_source
    }
```

---

## 📝 **2. 反馈管理功能设计（仅查询）**

### **2.1 反馈列表查询**

```python
@router.get("/admin/feedback")
async def get_admin_feedback(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    invalid_type: Optional[int] = Query(None, description="失效类型筛选"),
    pan_type: Optional[int] = Query(None, description="网盘类型筛选"),
    is_verified: Optional[bool] = Query(None, description="是否已验证"),
    keyword: Optional[str] = Query(None, description="搜索资源ID或描述"),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: User = Depends(RequireFeedbackManage)
):
    """
    管理员反馈列表查询
    """
    # 构建查询条件
    query = ResourceInvalidFeedback.all()

    # 应用筛选条件
    if invalid_type is not None:
        query = query.filter(invalid_type=invalid_type)
    if pan_type is not None:
        query = query.filter(pan_type=pan_type)
    if is_verified is not None:
        query = query.filter(is_verified=is_verified)
    if keyword:
        query = query.filter(
            Q(resource_id__icontains=keyword) |
            Q(description__icontains=keyword)
        )
    if start_date:
        query = query.filter(created_at__gte=start_date)
    if end_date:
        query = query.filter(created_at__lte=end_date)

    # 分页查询
    total = await query.count()
    offset = (page - 1) * size
    feedbacks = await query.offset(offset).limit(size).order_by('-created_at')

    # 扩展反馈信息（关联资源信息）
    feedback_list = []
    for feedback in feedbacks:
        # 获取关联的资源信息
        resource = await PanResource.filter(resource_key=feedback.resource_id).first()

        feedback_data = {
            "id": feedback.id,
            "resource_id": feedback.resource_id,
            "resource_title": resource.title if resource else "资源已删除",
            "resource_exists": resource is not None,
            "pan_type": feedback.pan_type,
            "pan_type_name": get_pan_type_name(feedback.pan_type),
            "invalid_type": feedback.invalid_type,
            "invalid_type_name": get_invalid_type_name(feedback.invalid_type),
            "description": feedback.description,
            "contact_info": feedback.contact_info,
            "is_verified": feedback.is_verified,
            "verification_result": feedback.verification_result,
            "created_at": feedback.created_at,
            "updated_at": feedback.updated_at
        }
        feedback_list.append(feedback_data)

    return {
        "status": "success",
        "data": {
            "feedbacks": feedback_list,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            }
        }
    }

def get_pan_type_name(pan_type: int) -> str:
    """获取网盘类型名称"""
    mapping = {1: "百度网盘", 2: "夸克网盘", 3: "阿里云盘", 4: "迅雷网盘"}
    return mapping.get(pan_type, "未知网盘")

def get_invalid_type_name(invalid_type: int) -> str:
    """获取失效类型名称"""
    mapping = {1: "链接错误", 2: "资源失效", 3: "文件不存在"}
    return mapping.get(invalid_type, "其他问题")
```

### **2.2 反馈详情查询**

```python
@router.get("/admin/feedback/{feedback_id}")
async def get_admin_feedback_detail(
    feedback_id: int,
    current_user: User = Depends(RequireFeedbackManage)
):
    """
    管理员反馈详情查询
    """
    feedback = await ResourceInvalidFeedback.get_or_none(id=feedback_id)
    if not feedback:
        raise HTTPException(404, "反馈不存在")

    # 获取关联的资源详情
    resource = await PanResource.filter(resource_key=feedback.resource_id).first()

    # 获取同一资源的其他反馈
    related_feedbacks = await ResourceInvalidFeedback.filter(
        resource_id=feedback.resource_id
    ).exclude(id=feedback_id).order_by('-created_at')

    return {
        "status": "success",
        "data": {
            "feedback": {
                "id": feedback.id,
                "resource_id": feedback.resource_id,
                "pan_type": feedback.pan_type,
                "invalid_type": feedback.invalid_type,
                "description": feedback.description,
                "contact_info": feedback.contact_info,
                "is_verified": feedback.is_verified,
                "verification_result": feedback.verification_result,
                "created_at": feedback.created_at,
                "updated_at": feedback.updated_at
            },
            "resource": resource.dict() if resource else None,
            "related_feedbacks": [
                {
                    "id": f.id,
                    "invalid_type": f.invalid_type,
                    "description": f.description,
                    "created_at": f.created_at
                } for f in related_feedbacks
            ]
        }
    }
```

### **2.3 反馈统计功能**

```python
@router.get("/admin/feedback/stats")
async def get_admin_feedback_stats(
    current_user: User = Depends(RequireFeedbackManage)
):
    """
    管理员反馈统计
    """
    # 总数统计
    total_count = await ResourceInvalidFeedback.all().count()

    # 按失效类型统计
    by_invalid_type = {}
    for invalid_type in [1, 2, 3]:
        count = await ResourceInvalidFeedback.filter(invalid_type=invalid_type).count()
        by_invalid_type[get_invalid_type_name(invalid_type)] = count

    # 按网盘类型统计
    by_pan_type = {}
    for pan_type in [1, 2, 3, 4]:
        count = await ResourceInvalidFeedback.filter(pan_type=pan_type).count()
        by_pan_type[get_pan_type_name(pan_type)] = count

    # 按验证状态统计
    by_verification = {
        "verified": await ResourceInvalidFeedback.filter(is_verified=True).count(),
        "unverified": await ResourceInvalidFeedback.filter(is_verified=False).count()
    }

    # 时间趋势统计
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    recent_stats = {
        "today": await ResourceInvalidFeedback.filter(created_at__gte=today).count(),
        "week": await ResourceInvalidFeedback.filter(
            created_at__gte=today - timedelta(days=7)
        ).count(),
        "month": await ResourceInvalidFeedback.filter(
            created_at__gte=today - timedelta(days=30)
        ).count()
    }

    return {
        "status": "success",
        "data": {
            "total_count": total_count,
            "by_invalid_type": by_invalid_type,
            "by_pan_type": by_pan_type,
            "by_verification": by_verification,
            "recent_stats": recent_stats
        }
    }
```

---

## 🔐 **3. 权限控制方案**

### **3.1 权限定义（复用现有）**

```python
# 复用app/core/permissions.py中已定义的权限
class Permissions:
    RESOURCE_MANAGE = "resource.manage"  # 资源管理权限
    FEEDBACK_MANAGE = "feedback.manage"  # 反馈管理权限

# 复用现有的权限检查器
RequireResourceManage = PermissionChecker(Permissions.RESOURCE_MANAGE)
RequireFeedbackManage = PermissionChecker(Permissions.FEEDBACK_MANAGE)
```

### **3.2 接口权限配置**

```python
# 资源管理接口权限
@router.get("/admin/resources", dependencies=[Depends(RequireResourceManage)])
@router.get("/admin/resources/{resource_key}", dependencies=[Depends(RequireResourceManage)])
@router.delete("/admin/resources/{resource_id}", dependencies=[Depends(RequireResourceManage)])
@router.get("/admin/resources/stats", dependencies=[Depends(RequireResourceManage)])

# 反馈管理接口权限
@router.get("/admin/feedback", dependencies=[Depends(RequireFeedbackManage)])
@router.get("/admin/feedback/{feedback_id}", dependencies=[Depends(RequireFeedbackManage)])
@router.get("/admin/feedback/stats", dependencies=[Depends(RequireFeedbackManage)])

# submit_resources接口的动态权限控制
@router.post("/submit_resources")
async def submit_resources_for_processing(
    payload: ResourceSubmissionRequest,
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    # 动态权限验证逻辑
    if payload.is_mine or payload.admin_submit:
        if not current_user:
            raise HTTPException(401, "管理员功能需要认证")

        user_permissions = await current_user.get_permissions()
        if Permissions.RESOURCE_MANAGE not in user_permissions:
            raise HTTPException(403, "需要资源管理权限")
```

---

## 🔄 **4. 业务流程设计**

### **4.1 资源管理流程**

```mermaid
graph TD
    A[管理员登录] --> B[验证资源管理权限]
    B --> C{选择功能}

    C -->|查看资源列表| D[调用cached_resources逻辑]
    D --> E[应用管理员筛选条件]
    E --> F[返回资源列表]

    C -->|新增资源| G[调用submit_resources]
    G --> H[验证管理员权限]
    H --> I[设置is_mine=true]
    I --> J[控制is_parsed标志]
    J --> K[创建资源记录]

    C -->|删除资源| L[验证权限]
    L --> M[删除资源和相关反馈]

    C -->|查看统计| N[调用扩展的stats接口]
    N --> O[返回详细统计信息]
```

### **4.2 反馈管理流程**

```mermaid
graph TD
    A[管理员访问反馈管理] --> B[验证反馈管理权限]
    B --> C{选择功能}

    C -->|查看反馈列表| D[应用筛选条件]
    D --> E[关联资源信息]
    E --> F[返回反馈列表]

    C -->|查看反馈详情| G[获取反馈信息]
    G --> H[获取关联资源]
    H --> I[获取相关反馈]
    I --> J[返回完整详情]

    C -->|查看统计| K[统计各维度数据]
    K --> L[返回统计结果]
```

---

## 📋 **5. API端点总览**

### **5.1 资源管理API**

| 端点 | 方法 | 功能 | 复用方式 |
|------|------|------|----------|
| `/api/admin/resources` | GET | 资源列表 | 复用cached_resources逻辑 |
| `/api/admin/resources/{key}` | GET | 资源详情 | 复用resource_details |
| `/api/admin/resources/stats` | GET | 资源统计 | 扩展resource_stats |
| `/api/admin/resources/{id}` | DELETE | 删除资源 | 新增功能 |
| `/api/admin/resources/batch-delete` | POST | 批量删除 | 新增功能 |
| `/api/submit_resources` | POST | 新增资源 | 扩展现有接口 |

### **5.2 反馈管理API**

| 端点 | 方法 | 功能 | 说明 |
|------|------|------|------|
| `/api/admin/feedback` | GET | 反馈列表 | 仅查询功能 |
| `/api/admin/feedback/{id}` | GET | 反馈详情 | 仅查询功能 |
| `/api/admin/feedback/stats` | GET | 反馈统计 | 仅查询功能 |

---

## 🎯 **6. 实现优先级建议**

### **Phase 1: 核心查询功能**
1. ✅ 扩展 `/api/cached_resources` 支持管理员筛选
2. ✅ 实现 `/api/admin/resources` 端点
3. ✅ 实现 `/api/admin/feedback` 端点
4. ✅ 实现基础统计功能

### **Phase 2: 管理功能**
1. ✅ 扩展 `/api/submit_resources` 支持管理员权限和is_parsed控制
2. ✅ 实现资源删除功能
3. ✅ 实现详情查询功能

### **Phase 3: 高级功能**
1. ✅ 批量操作功能
2. ✅ 详细统计报表
3. ✅ 性能优化

---

## 🔧 **7. 技术实现要点**

### **7.1 接口复用策略**
- **参数映射**: 管理员参数映射到现有接口参数
- **逻辑复用**: 直接调用现有的业务逻辑函数
- **响应转换**: 将现有响应格式转换为管理员界面需要的格式
- **权限叠加**: 在现有接口基础上增加权限验证层

### **7.2 数据扩展策略**
- **计算字段**: 基于现有字段计算管理员需要的信息
- **关联查询**: 通过关联查询获取额外信息
- **统计聚合**: 使用数据库聚合函数生成统计数据
- **缓存优化**: 对计算密集的统计数据使用缓存

### **7.3 权限控制策略**
- **分层验证**: 接口级权限 + 功能级权限
- **动态权限**: 根据请求参数动态验证权限
- **向后兼容**: 保持现有接口的兼容性
- **安全增强**: 敏感操作的额外验证

### **7.4 数据库查询优化**
- **索引利用**: 充分利用现有字段的索引
- **分页优化**: 合理的分页查询避免大数据量问题
- **关联查询**: 减少N+1查询问题
- **缓存策略**: 对统计数据使用适当的缓存

---

## 📝 **8. 响应格式规范**

### **8.1 统一响应格式**

```json
{
    "status": "success|error",
    "message": "操作结果描述",
    "data": {
        // 具体数据内容
    },
    "pagination": {  // 分页数据时包含
        "page": 1,
        "size": 20,
        "total": 100,
        "pages": 5
    }
}
```

### **8.2 错误响应格式**

```json
{
    "status": "error",
    "message": "错误描述",
    "error_code": "PERMISSION_DENIED",
    "details": {
        // 错误详细信息
    }
}
```

---

## 🚀 **9. 部署和测试建议**

### **9.1 开发阶段**
1. 先实现核心查询功能
2. 逐步添加管理功能
3. 完善权限控制
4. 性能优化

### **9.2 测试策略**
1. **单元测试**: 每个API端点的功能测试
2. **权限测试**: 验证权限控制的正确性
3. **性能测试**: 大数据量下的查询性能
4. **集成测试**: 与现有系统的兼容性

### **9.3 监控和日志**
1. **操作日志**: 记录管理员的关键操作
2. **性能监控**: 监控API响应时间
3. **错误追踪**: 记录和分析错误信息
4. **使用统计**: 分析功能使用情况

---

## 📋 **10. 总结**

这个设计方案在严格遵守约束条件的前提下，最大化复用了现有接口和逻辑，通过扩展和组合的方式实现了完整的管理员后台功能。主要特点：

### **✅ 优势**
- **零数据库变更**: 完全基于现有表结构
- **最大化复用**: 充分利用现有接口和逻辑
- **权限安全**: 完善的权限控制机制
- **向后兼容**: 不影响现有功能
- **性能优化**: 利用现有的缓存和优化机制

### **🎯 核心价值**
- **快速实现**: 基于现有基础快速构建管理功能
- **维护简单**: 复用现有逻辑，减少维护成本
- **扩展性强**: 为未来功能扩展预留空间
- **用户体验**: 提供完整的管理员后台体验

这个方案确保了在技术约束下实现最大的功能价值，为管理员提供了完整、高效的资源和反馈管理能力。
