# PostgreSQL配置文件 - 针对4核8GB服务器优化
# 适用于中等负载的Web应用

# 连接设置
max_connections = 100                    # 最大连接数
shared_buffers = 256MB                   # 共享缓冲区 (约内存的25%)
effective_cache_size = 1GB               # 有效缓存大小

# 内存设置 - 针对大数据集查询优化
work_mem = 8MB                           # 增加工作内存（用于排序和哈希）
maintenance_work_mem = 128MB             # 增加维护工作内存
temp_buffers = 16MB                      # 增加临时缓冲区

# WAL设置
wal_buffers = 16MB                       # WAL缓冲区
checkpoint_completion_target = 0.9       # 检查点完成目标
max_wal_size = 1GB                       # 最大WAL大小
min_wal_size = 80MB                      # 最小WAL大小

# 查询规划器
random_page_cost = 1.1                   # 随机页面成本
effective_io_concurrency = 200           # 有效IO并发

# 日志设置
log_destination = 'stderr'               # 日志目标
logging_collector = on                   # 启用日志收集器
log_directory = 'log'                    # 日志目录
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d                    # 日志轮转时间
log_rotation_size = 100MB                # 日志轮转大小
log_min_duration_statement = 1000        # 记录慢查询(1秒以上)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# 性能监控
track_activities = on                    # 跟踪活动
track_counts = on                        # 跟踪统计
track_io_timing = on                     # 跟踪IO时间
track_functions = pl                     # 跟踪函数

# 自动清理
autovacuum = on                          # 启用自动清理
autovacuum_max_workers = 2               # 自动清理最大工作进程
autovacuum_naptime = 30s                 # 自动清理间隔

# 锁管理
deadlock_timeout = 1s                    # 死锁超时
lock_timeout = 30s                       # 锁超时

# 其他优化
synchronous_commit = on                  # 同步提交
fsync = on                              # 强制同步
full_page_writes = on                   # 完整页面写入
