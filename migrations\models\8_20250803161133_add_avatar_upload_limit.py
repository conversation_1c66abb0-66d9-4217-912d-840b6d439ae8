from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "user_avatar_upload_logs" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "upload_date" DATE NOT NULL,
    "upload_count" INT NOT NULL  DEFAULT 1,
    "last_upload_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "ip_address" VARCHAR(45),
    "user_agent" VARCHAR(512),
    "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_user_avatar_user_id_bcaf9a" UNIQUE ("user_id", "upload_date")
);
COMMENT ON COLUMN "user_avatar_upload_logs"."upload_date" IS '上传日期';
COMMENT ON COLUMN "user_avatar_upload_logs"."upload_count" IS '当日上传次数';
COMMENT ON COLUMN "user_avatar_upload_logs"."last_upload_at" IS '最后上传时间';
COMMENT ON COLUMN "user_avatar_upload_logs"."ip_address" IS '上传IP地址';
COMMENT ON COLUMN "user_avatar_upload_logs"."user_agent" IS '用户代理';
COMMENT ON COLUMN "user_avatar_upload_logs"."user_id" IS '用户';
COMMENT ON TABLE "user_avatar_upload_logs" IS '用户头像上传日志表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "user_avatar_upload_logs";"""
