"""
配置管理服务
"""

import yaml
import re
import os
from typing import Any, Dict, List, Optional, Union
import logging
from fastapi import HTTPException
from ruamel.yaml import YAML
from ruamel.yaml.comments import CommentedMap, CommentedSeq
from pathlib import Path

from app.models.config_models import ValidationResult
from app.utils.config import settings

logger = logging.getLogger("config-service")


class CommentPreservingYAMLParser:
    """支持注释保护的YAML解析器"""

    def __init__(self):
        self.yaml = YAML()
        self.yaml.preserve_quotes = True
        self.yaml.width = 4096  # 避免长行被折叠
        self.yaml.indent(mapping=2, sequence=4, offset=2)

    def load(self, file_path: str) -> CommentedMap:
        """加载YAML文件并保持注释"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return self.yaml.load(f)
        except Exception as e:
            logger.error(f"加载YAML文件失败: {e}")
            raise

    def save(self, data: CommentedMap, file_path: str) -> bool:
        """保存YAML文件并保持注释"""
        try:
            # 创建临时文件
            temp_file = f"{file_path}.tmp"

            with open(temp_file, "w", encoding="utf-8") as f:
                self.yaml.dump(data, f)

            # 原子性替换
            os.replace(temp_file, file_path)
            return True

        except Exception as e:
            logger.error(f"保存YAML文件失败: {e}")
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise

    def get_value_by_path(self, data: CommentedMap, path: str) -> Any:
        """根据路径获取配置值"""
        try:
            keys = path.split(".")
            current = data

            for key in keys:
                if isinstance(current, (CommentedMap, dict)):
                    current = current[key]
                elif isinstance(current, (CommentedSeq, list)):
                    # 处理数组索引
                    try:
                        index = int(key)
                        current = current[index]
                    except (ValueError, IndexError):
                        raise KeyError(f"无效的数组索引: {key}")
                else:
                    raise KeyError(f"无法在 {type(current)} 中查找键: {key}")

            return current

        except (KeyError, TypeError, IndexError) as e:
            logger.debug(f"路径 {path} 不存在: {e}")
            return None

    def set_value_by_path(
        self, data: CommentedMap, path: str, value: Any
    ) -> CommentedMap:
        """根据路径设置配置值"""
        keys = path.split(".")
        current = data

        # 导航到父级
        for key in keys[:-1]:
            # 检查是否为数组索引
            if key.isdigit():
                # 数组索引，转换为整数
                index = int(key)
                if index >= len(current):
                    # 扩展数组
                    while len(current) <= index:
                        current.append(CommentedMap())
                current = current[index]
            else:
                # 普通键
                if key not in current:
                    current[key] = CommentedMap()
                current = current[key]

        # 设置值
        final_key = keys[-1]
        if final_key.isdigit():
            # 最终键是数组索引
            index = int(final_key)
            if index >= len(current):
                # 扩展数组
                while len(current) <= index:
                    current.append(None)
            current[index] = value
        else:
            # 最终键是普通键
            current[final_key] = value

        return data

    def get_config_tree(
        self, data: CommentedMap, path_prefix: str = ""
    ) -> Dict[str, Any]:
        """将配置转换为树形结构"""
        tree = {}

        if isinstance(data, CommentedMap):
            for key, value in data.items():
                current_path = f"{path_prefix}.{key}" if path_prefix else key

                if isinstance(value, (CommentedMap, dict)):
                    # 嵌套对象
                    tree[key] = {
                        "type": "object",
                        "path": current_path,
                        "value": value,
                        "children": self.get_config_tree(value, current_path),
                        "comment": self._get_comment(data, key),
                    }
                elif isinstance(value, (CommentedSeq, list)):
                    # 数组
                    tree[key] = {
                        "type": "array",
                        "path": current_path,
                        "value": value,
                        "children": self._get_array_tree(value, current_path),
                        "comment": self._get_comment(data, key),
                    }
                else:
                    # 基本类型
                    tree[key] = {
                        "type": type(value).__name__.lower(),
                        "path": current_path,
                        "value": value,
                        "comment": self._get_comment(data, key),
                    }

        return tree

    def _get_array_tree(self, array: CommentedSeq, path_prefix: str) -> Dict[str, Any]:
        """处理数组类型的配置"""
        tree = {}

        for index, item in enumerate(array):
            current_path = f"{path_prefix}.{index}"

            if isinstance(item, (CommentedMap, dict)):
                tree[str(index)] = {
                    "type": "object",
                    "path": current_path,
                    "value": item,
                    "children": self.get_config_tree(item, current_path),
                    "comment": self._get_comment(array, index),
                }
            else:
                tree[str(index)] = {
                    "type": type(item).__name__.lower(),
                    "path": current_path,
                    "value": item,
                    "comment": self._get_comment(array, index),
                }

        return tree

    def _get_comment(
        self, parent: Union[CommentedMap, CommentedSeq], key: Union[str, int]
    ) -> Optional[str]:
        """获取配置项的注释"""
        try:
            if hasattr(parent, "ca") and parent.ca.items:
                comment_token = parent.ca.items.get(key)
                if comment_token and comment_token[2]:
                    # 清理注释文本
                    comment = comment_token[2].value.strip()
                    if comment.startswith("#"):
                        comment = comment[1:].strip()
                    return comment
        except Exception:
            pass
        return None


class ConfigService:
    """配置管理服务"""

    CONFIG_FILE_PATH = "app/config.yaml"

    def __init__(self):
        """初始化配置服务"""
        self.yaml_parser = CommentPreservingYAMLParser()
        self._config_cache = None

    # 敏感配置项
    SENSITIVE_KEYS = {
        "database.url",
        "database.credentials.password",
        "auth.jwt_secret_key",
        "email.password",
        "wx_push.corp_secret",
        "redis.password",
    }

    async def load_config(self) -> Dict[str, Any]:
        """加载配置文件（兼容性方法）"""
        try:
            with open(self.CONFIG_FILE_PATH, "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {self.CONFIG_FILE_PATH}")
            raise HTTPException(status_code=500, detail="配置文件不存在")
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式错误: {e}")
            raise HTTPException(status_code=500, detail="配置文件格式错误")

    async def load_config_with_comments(self) -> CommentedMap:
        """加载配置文件并保持注释"""
        try:
            return self.yaml_parser.load(self.CONFIG_FILE_PATH)
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {self.CONFIG_FILE_PATH}")
            raise HTTPException(status_code=500, detail="配置文件不存在")
        except Exception as e:
            logger.error(f"配置文件格式错误: {e}")
            raise HTTPException(status_code=500, detail="配置文件格式错误")

    async def get_config_tree(self) -> Dict[str, Any]:
        """获取配置的树形结构"""
        try:
            config_data = await self.load_config_with_comments()
            return self.yaml_parser.get_config_tree(config_data)
        except Exception as e:
            logger.error(f"获取配置树失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取配置树失败: {str(e)}")

    async def get_config_by_path(self, path: str) -> Any:
        """根据路径获取配置值"""
        try:
            config_data = await self.load_config_with_comments()
            value = self.yaml_parser.get_value_by_path(config_data, path)
            if value is None:
                raise HTTPException(status_code=404, detail=f"配置路径 {path} 不存在")
            return value
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

    async def set_config_by_path(self, path: str, value: Any) -> bool:
        """根据路径设置配置值"""
        try:
            logger.debug(f"开始设置配置: {path} = {value} (类型: {type(value)})")

            config_data = await self.load_config_with_comments()
            logger.debug(f"配置数据加载完成")

            # 设置新值
            updated_config = self.yaml_parser.set_value_by_path(
                config_data, path, value
            )
            logger.debug(f"配置值设置完成")

            # 保存配置
            success = self.yaml_parser.save(updated_config, self.CONFIG_FILE_PATH)
            logger.debug(f"配置保存结果: {success}")

            if success:
                # 重新加载settings
                settings.load_config(self.CONFIG_FILE_PATH)
                logger.debug(f"settings重新加载完成")

                # 应用配置变更（热更新）
                logger.debug(f"开始应用配置变更: {path}")
                await self.apply_config_change(path, value)
                logger.debug(f"配置变更应用完成")

                logger.info(f"配置更新成功: {path} = {value}")
                return True
            else:
                raise HTTPException(status_code=500, detail="配置保存失败")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"设置配置失败: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"设置配置失败: {str(e)}")

    async def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            # 创建临时文件
            temp_file = f"{self.CONFIG_FILE_PATH}.tmp"

            with open(temp_file, "w", encoding="utf-8") as f:
                yaml.dump(
                    config, f, default_flow_style=False, allow_unicode=True, indent=2
                )

            # 原子性替换
            os.replace(temp_file, self.CONFIG_FILE_PATH)

            # 重新加载settings
            settings.load_config(self.CONFIG_FILE_PATH)

            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise HTTPException(status_code=500, detail=f"保存配置文件失败: {str(e)}")

    def set_config_value(
        self, config: Dict[str, Any], key: str, value: Any
    ) -> Dict[str, Any]:
        """设置配置值"""
        keys = key.split(".")
        current = config

        # 导航到父级
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        # 设置值
        current[keys[-1]] = value
        return config

    def is_sensitive_config(self, key: str) -> bool:
        """检查是否为敏感配置"""
        return key in self.SENSITIVE_KEYS or any(
            sensitive_key in key
            for sensitive_key in ["password", "secret", "token", "key", "cookie"]
        )

    def mask_sensitive_value(self, key: str, value: Any) -> Any:
        """遮蔽敏感配置值"""
        if not self.is_sensitive_config(key):
            return value

        if isinstance(value, str) and len(value) > 8:
            return value[:4] + "*" * (len(value) - 8) + value[-4:]
        elif isinstance(value, str):
            return "***"
        else:
            return "***"

    def get_effect_type(self, key: str) -> str:
        """获取配置项生效方式"""
        if key.startswith("logging."):
            return "reload_required"
        else:
            return "immediate"

    async def apply_config_change(self, config_key: str, new_value: Any) -> bool:
        """应用配置变更，实现热更新"""
        logger.debug(
            f"开始应用配置变更: {config_key} = {new_value} (类型: {type(new_value)})"
        )

        effect_type = self.get_effect_type(config_key)
        logger.debug(f"配置生效类型: {effect_type}")

        try:
            if effect_type == "immediate":
                # 立即生效的配置，尝试热更新
                logger.debug(f"应用立即生效配置: {config_key}")
                return await self._apply_immediate_config(config_key, new_value)
            elif effect_type == "reload_required":
                # 需要重新加载的配置
                logger.debug(f"应用重新加载配置: {config_key}")
                return await self._apply_reload_config(config_key, new_value)

            logger.debug(f"配置变更应用完成: {config_key}")
            return True
        except Exception as e:
            logger.error(f"应用配置变更失败 {config_key}: {e}", exc_info=True)
            return False

    async def _apply_immediate_config(self, config_key: str, new_value: Any) -> bool:
        """应用立即生效的配置"""
        try:
            # 缓存配置热更新
            if config_key.startswith("cache."):
                await self._update_cache_config(config_key, new_value)

            # 并发限制配置热更新
            elif config_key.startswith("concurrency_limiter."):
                await self._update_concurrency_config(config_key, new_value)

            # 安全配置热更新
            elif config_key == "security.blocked_keywords":
                await self._update_security_config(config_key, new_value)

            # 爬虫配置热更新
            elif "_crawler." in config_key:
                await self._update_crawler_config(config_key, new_value)

            # 网盘账号配置热更新
            elif "_accounts" in config_key:
                await self._update_pan_accounts_config(config_key, new_value)

            logger.info(f"立即生效配置已更新: {config_key}")
            return True

        except Exception as e:
            logger.error(f"应用立即生效配置失败 {config_key}: {e}")
            return False

    async def _apply_reload_config(self, config_key: str, new_value: Any) -> bool:
        """应用需要重新加载的配置"""
        try:
            # 日志配置热更新
            if config_key.startswith("logging."):
                await self._update_logging_config(config_key, new_value)

            logger.info(f"重新加载配置已更新: {config_key}")
            return True

        except Exception as e:
            logger.error(f"应用重新加载配置失败 {config_key}: {e}")
            return False

    async def _update_cache_config(self, config_key: str, new_value: Any):
        """更新缓存配置"""
        # 这里可以实现缓存配置的热更新
        # 例如：更新缓存大小、TTL等
        logger.info(f"缓存配置热更新: {config_key} = {new_value}")

    async def _update_concurrency_config(self, config_key: str, new_value: Any):
        """更新并发限制配置"""
        try:
            # 动态更新并发限制
            if config_key == "concurrency_limiter.times":
                # 这里需要更新中间件中的并发限制
                from app.middleware.concurrency import update_max_concurrent_requests

                update_max_concurrent_requests(new_value)
                logger.info(f"并发限制已更新: {new_value}")
        except ImportError:
            logger.warning("并发限制中间件不支持热更新")

    async def _update_security_config(self, config_key: str, new_value: Any):
        """更新安全配置"""
        if config_key == "security.blocked_keywords":
            # 这里可以通知搜索模块更新屏蔽词列表
            logger.info(f"安全配置已更新: {config_key}")

    async def _update_crawler_config(self, config_key: str, new_value: Any):
        """更新爬虫配置"""
        # 爬虫配置的热更新
        logger.info(f"爬虫配置已更新: {config_key} = {new_value}")

    async def _update_pan_accounts_config(self, config_key: str, new_value: Any):
        """更新网盘账号配置"""
        try:
            # 网盘账号配置已经支持热加载
            # 因为网盘服务的accounts属性每次访问都会重新从settings读取
            logger.info(f"网盘账号配置已更新: {config_key}")

            # 可以在这里添加额外的处理逻辑
            if "baidu_accounts" in config_key:
                logger.info("百度网盘账号配置已更新，下次请求将使用新的Cookie")
            elif "quark_accounts" in config_key:
                logger.info("夸克网盘账号配置已更新，下次请求将使用新的Cookie")
            elif "xunlei_accounts" in config_key:
                logger.info("迅雷网盘账号配置已更新，下次请求将使用新的Cookie")

            # 可以在这里通知网盘服务清除缓存（如果有的话）
            await self._notify_pan_services_config_change(config_key)

        except Exception as e:
            logger.error(f"更新网盘账号配置失败: {e}")

    async def _notify_pan_services_config_change(self, config_key: str):
        """通知网盘服务配置变更"""
        try:
            # 这里可以实现通知机制，比如：
            # 1. 清除网盘服务的内部缓存
            # 2. 重新验证Cookie有效性
            # 3. 发送配置变更事件

            logger.debug(f"已通知网盘服务配置变更: {config_key}")

        except Exception as e:
            logger.warning(f"通知网盘服务配置变更失败: {e}")

    async def _update_logging_config(self, config_key: str, new_value: Any):
        """更新日志配置"""
        try:
            import logging

            if config_key == "logging.level":
                # 动态更新日志级别
                log_level = getattr(logging, new_value.upper(), logging.INFO)
                root_logger = logging.getLogger()
                root_logger.setLevel(log_level)

                # 更新所有处理器的级别
                for handler in root_logger.handlers:
                    handler.setLevel(log_level)

                logger.info(f"日志级别已更新为: {new_value}")

            elif config_key == "logging.format":
                # 动态更新日志格式
                root_logger = logging.getLogger()
                formatter = logging.Formatter(new_value)

                for handler in root_logger.handlers:
                    handler.setFormatter(formatter)

                logger.info(f"日志格式已更新")

        except Exception as e:
            logger.error(f"更新日志配置失败: {e}")
            raise


class ConfigValidator:
    """配置验证器"""

    # 验证规则定义
    VALIDATION_RULES = {
        # 应用程序配置
        "app.name": {
            "type": "string",
            "required": True,
            "min_length": 1,
            "max_length": 50,
            "pattern": r"^[a-zA-Z0-9-_]+$",
            "description": "应用名称，只能包含字母、数字、连字符和下划线",
        },
        "app.version": {
            "type": "string",
            "required": True,
            "pattern": r"^\d+\.\d+\.\d+$",
            "description": "版本号，格式为 x.y.z",
        },
        "app.debug": {
            "type": "boolean",
            "required": True,
            "description": "调试模式开关",
        },
        # API服务配置
        "api.port": {
            "type": "integer",
            "required": True,
            "min_value": 1024,
            "max_value": 65535,
            "description": "API服务端口，范围 1024-65535",
        },
        "api.host": {
            "type": "string",
            "required": True,
            "pattern": r"^(\d{1,3}\.){3}\d{1,3}$|^localhost$|^0\.0\.0\.0$",
            "description": "API服务监听地址",
        },
        "api.max_concurrent_requests": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 1000,
            "description": "最大并发请求数，范围 1-1000",
        },
        # 数据库配置
        "database.url": {
            "type": "string",
            "required": True,
            "pattern": r"^postgresql://.*",
            "description": "PostgreSQL数据库连接URL",
        },
        "database.credentials.host": {
            "type": "string",
            "required": True,
            "description": "数据库主机地址",
        },
        "database.credentials.port": {
            "type": "string",
            "required": True,
            "pattern": r"^\d+$",
            "description": "数据库端口号",
        },
        "database.credentials.user": {
            "type": "string",
            "required": True,
            "min_length": 1,
            "description": "数据库用户名",
        },
        "database.credentials.password": {
            "type": "string",
            "required": True,
            "min_length": 1,
            "sensitive": True,
            "description": "数据库密码",
        },
        "database.credentials.database": {
            "type": "string",
            "required": True,
            "min_length": 1,
            "description": "数据库名称",
        },
        # Redis配置
        "redis.host": {
            "type": "string",
            "required": True,
            "description": "Redis主机地址",
        },
        "redis.port": {
            "type": "integer",
            "required": True,
            "min_value": 1,
            "max_value": 65535,
            "description": "Redis端口号",
        },
        "redis.db": {
            "type": "integer",
            "required": False,
            "min_value": 0,
            "max_value": 15,
            "description": "Redis数据库编号，范围 0-15",
        },
        # 缓存配置
        "cache.resource_cache.maxsize": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 100000,
            "description": "资源缓存最大条目数",
        },
        "cache.resource_cache.ttl": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 86400,
            "description": "资源缓存过期时间（秒）",
        },
        "cache.db_exists_cache.maxsize": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 100000,
            "description": "数据库存在性缓存最大条目数",
        },
        "cache.db_exists_cache.ttl": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 86400,
            "description": "数据库存在性缓存过期时间（秒）",
        },
        # 网盘服务配置
        "pan_service.default_timeout": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 300,
            "description": "默认超时时间（秒），范围 1-300",
        },
        "pan_service.max_retries": {
            "type": "integer",
            "required": False,
            "min_value": 0,
            "max_value": 10,
            "description": "最大重试次数，范围 0-10",
        },
        "pan_service.retry_delay": {
            "type": "float",
            "required": False,
            "min_value": 0.1,
            "max_value": 60.0,
            "description": "重试延迟时间（秒），范围 0.1-60.0",
        },
        "pan_service.max_concurrent_tasks": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 50,
            "description": "最大并发任务数，范围 1-50",
        },
        "pan_service.share_expiry_days": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 365,
            "description": "分享链接有效期（天），范围 1-365",
        },
        # 日志配置
        "logging.level": {
            "type": "string",
            "required": False,
            "enum_values": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            "description": "日志级别",
        },
        "logging.format": {
            "type": "string",
            "required": False,
            "min_length": 1,
            "description": "日志格式",
        },
        "logging.filename": {
            "type": "string",
            "required": False,
            "pattern": r"^[a-zA-Z0-9._-]+\.log$",
            "description": "日志文件名，必须以.log结尾",
        },
        # 爬虫配置通用规则
        "*.enabled": {
            "type": "boolean",
            "required": False,
            "description": "是否启用",
        },
        "*.timeout": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 300,
            "description": "超时时间（秒）",
        },
        "*.max_retries": {
            "type": "integer",
            "required": False,
            "min_value": 0,
            "max_value": 10,
            "description": "最大重试次数",
        },
        "*.retry_delay": {
            "type": "float",
            "required": False,
            "min_value": 0.1,
            "max_value": 60.0,
            "description": "重试延迟时间（秒）",
        },
        "*.concurrent_limit": {
            "type": "integer",
            "required": False,
            "min_value": 1,
            "max_value": 50,
            "description": "并发限制",
        },
    }

    def validate_type(self, value: Any, expected_type: str) -> bool:
        """验证数据类型"""
        type_mapping = {
            "string": str,
            "integer": int,
            "float": float,
            "boolean": bool,
            "list": list,
            "dict": dict,
        }

        expected_python_type = type_mapping.get(expected_type)
        if not expected_python_type:
            return True  # 未知类型，跳过验证

        return isinstance(value, expected_python_type)

    def get_validation_rules(self, key: str) -> Dict[str, Any]:
        """获取配置项的验证规则"""
        # 直接匹配
        if key in self.VALIDATION_RULES:
            return self.VALIDATION_RULES[key]

        # 通配符匹配
        for pattern, rules in self.VALIDATION_RULES.items():
            if "*" in pattern:
                # 简单的通配符匹配
                pattern_parts = pattern.split(".")
                key_parts = key.split(".")

                if len(pattern_parts) == len(key_parts):
                    match = True
                    for i, (p_part, k_part) in enumerate(zip(pattern_parts, key_parts)):
                        if p_part != "*" and p_part != k_part:
                            match = False
                            break

                    if match:
                        return rules

        return {}

    def validate_config_value(self, key: str, value: Any) -> ValidationResult:
        """验证配置项值"""
        rules = self.get_validation_rules(key)

        if not rules:
            # 没有验证规则，默认通过
            return ValidationResult(valid=True, message="验证通过（无验证规则）")

        # 类型验证
        expected_type = rules.get("type")
        if expected_type and not self.validate_type(value, expected_type):
            return ValidationResult(
                valid=False,
                message=f"类型错误: 期望 {expected_type}，实际 {type(value).__name__}",
            )

        # 必填验证
        if rules.get("required") and (value is None or value == ""):
            return ValidationResult(valid=False, message="此配置项为必填项")

        # 枚举值验证
        enum_values = rules.get("enum_values")
        if enum_values and value not in enum_values:
            return ValidationResult(
                valid=False,
                message=f"值必须是以下之一: {', '.join(map(str, enum_values))}",
            )

        # 字符串长度验证
        if isinstance(value, str):
            min_length = rules.get("min_length")
            max_length = rules.get("max_length")

            if min_length and len(value) < min_length:
                return ValidationResult(
                    valid=False, message=f"长度不能少于 {min_length} 个字符"
                )

            if max_length and len(value) > max_length:
                return ValidationResult(
                    valid=False, message=f"长度不能超过 {max_length} 个字符"
                )

        # 数值范围验证
        if isinstance(value, (int, float)):
            min_value = rules.get("min_value")
            max_value = rules.get("max_value")

            # 确保 min_value 和 max_value 是数值类型
            if min_value is not None:
                try:
                    min_value = float(min_value)
                    if value < min_value:
                        return ValidationResult(
                            valid=False, message=f"值不能小于 {min_value}"
                        )
                except (ValueError, TypeError):
                    logger.warning(f"验证规则中的 min_value 不是有效数值: {min_value}")

            if max_value is not None:
                try:
                    max_value = float(max_value)
                    if value > max_value:
                        return ValidationResult(
                            valid=False, message=f"值不能大于 {max_value}"
                        )
                except (ValueError, TypeError):
                    logger.warning(f"验证规则中的 max_value 不是有效数值: {max_value}")

        # 正则表达式验证
        pattern = rules.get("pattern")
        if pattern and isinstance(value, str):
            if not re.match(pattern, value):
                return ValidationResult(
                    valid=False,
                    message=f"格式不正确: {rules.get('description', '请检查格式')}",
                )

        # 自定义验证
        custom_validation = self.custom_validate(key, value, rules)
        if not custom_validation.valid:
            return custom_validation

        return ValidationResult(valid=True, message="验证通过")

    def custom_validate(
        self, key: str, value: Any, rules: Dict[str, Any]
    ) -> ValidationResult:
        """自定义验证逻辑"""
        try:
            # URL验证
            if "url" in key.lower() and isinstance(value, str):
                if not value.startswith(
                    ("http://", "https://", "postgresql://", "redis://")
                ):
                    return ValidationResult(
                        valid=False,
                        message="URL格式不正确，必须以 http://, https://, postgresql:// 或 redis:// 开头",
                    )

            # Cookie验证
            if "cookie" in key.lower() and isinstance(value, str):
                if len(value) < 10:
                    return ValidationResult(
                        valid=False,
                        message="Cookie长度过短，可能无效",
                    )

            # 端口号验证
            if "port" in key.lower() and isinstance(value, (int, str)):
                try:
                    port_num = int(value)
                    if port_num < 1 or port_num > 65535:
                        return ValidationResult(
                            valid=False,
                            message="端口号必须在 1-65535 范围内",
                        )
                except ValueError:
                    return ValidationResult(
                        valid=False,
                        message="端口号必须是数字",
                    )

            # 文件路径验证
            if "path" in key.lower() and isinstance(value, str):
                if not value or value.strip() == "":
                    return ValidationResult(
                        valid=False,
                        message="文件路径不能为空",
                    )

            return ValidationResult(valid=True, message="自定义验证通过")

        except Exception as e:
            return ValidationResult(
                valid=False,
                message=f"验证过程中发生错误: {str(e)}",
            )

    def validate_config_dependencies(
        self, config_data: Dict[str, Any]
    ) -> List[ValidationResult]:
        """验证配置项之间的依赖关系"""
        results = []

        try:
            # 验证数据库配置的一致性
            db_url = config_data.get("database", {}).get("url", "")
            db_creds = config_data.get("database", {}).get("credentials", {})

            if db_url and db_creds:
                # 检查URL和凭据是否一致
                if "postgresql://" in db_url:
                    url_parts = db_url.replace("postgresql://", "").split("/")
                    if len(url_parts) >= 2:
                        auth_host = url_parts[0]
                        if "@" in auth_host:
                            auth_part, host_part = auth_host.rsplit("@", 1)
                            if ":" in auth_part:
                                url_user, url_pass = auth_part.split(":", 1)

                                # 检查用户名是否一致
                                if db_creds.get("user") != url_user:
                                    results.append(
                                        ValidationResult(
                                            valid=False,
                                            message="数据库URL中的用户名与credentials.user不一致",
                                        )
                                    )

            # 验证Redis配置
            redis_config = config_data.get("redis", {})
            if redis_config:
                redis_url = redis_config.get("url", "")
                redis_host = redis_config.get("host", "")
                redis_port = redis_config.get("port", 6379)

                if redis_url and redis_host:
                    # 检查URL和单独配置是否一致
                    expected_url = f"redis://{redis_host}:{redis_port}/0"
                    if not redis_url.startswith(f"redis://{redis_host}:{redis_port}"):
                        results.append(
                            ValidationResult(
                                valid=False,
                                message=f"Redis URL与host:port配置不一致，期望: {expected_url}",
                            )
                        )

            # 验证爬虫配置
            for key, value in config_data.items():
                if key.endswith("_crawler") and isinstance(value, dict):
                    enabled = value.get("enabled", False)
                    if enabled:
                        # 检查必要的配置项
                        required_fields = ["timeout", "max_retries"]
                        for field in required_fields:
                            if field not in value:
                                results.append(
                                    ValidationResult(
                                        valid=False,
                                        message=f"爬虫 {key} 已启用但缺少必要配置: {field}",
                                    )
                                )

            if not results:
                results.append(ValidationResult(valid=True, message="依赖关系验证通过"))

        except Exception as e:
            results.append(
                ValidationResult(
                    valid=False,
                    message=f"依赖关系验证失败: {str(e)}",
                )
            )

        return results


# 创建全局实例
config_service = ConfigService()
config_validator = ConfigValidator()
