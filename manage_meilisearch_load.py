#!/usr/bin/env python3
"""
管理 Meilisearch 负载的脚本
提供暂停/恢复同步、清理任务队列等功能
"""

import asyncio
import aiohttp
import json
import sys
import signal
import subprocess
from datetime import datetime

# 配置
MEILI_HOST = "http://127.0.0.1:7700"
MEILI_API_KEY = "M3lPuLTZeJeA7urKBe6YN7nAPJxBZxA7Lr7kamVXW_k"
INDEX_NAME = "resources"

class MeilisearchManager:
    def __init__(self):
        self.session = None
        self.headers = {"Authorization": f"Bearer {MEILI_API_KEY}"}
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_active_tasks(self):
        """获取活跃任务"""
        try:
            async with self.session.get(
                f"{MEILI_HOST}/tasks?status=processing,enqueued", 
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('results', [])
                return []
        except Exception as e:
            print(f"获取任务失败: {e}")
            return []
    
    async def cancel_task(self, task_uid):
        """取消指定任务"""
        try:
            async with self.session.delete(
                f"{MEILI_HOST}/tasks/{task_uid}", 
                headers=self.headers
            ) as response:
                return response.status == 200
        except Exception as e:
            print(f"取消任务 {task_uid} 失败: {e}")
            return False
    
    async def cancel_all_tasks(self):
        """取消所有排队中的任务"""
        try:
            async with self.session.delete(
                f"{MEILI_HOST}/tasks?status=enqueued", 
                headers=self.headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result
                return None
        except Exception as e:
            print(f"批量取消任务失败: {e}")
            return None
    
    async def get_index_stats(self):
        """获取索引统计"""
        try:
            async with self.session.get(
                f"{MEILI_HOST}/indexes/{INDEX_NAME}", 
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                return None
        except Exception as e:
            print(f"获取索引统计失败: {e}")
            return None

def find_sync_processes():
    """查找同步相关进程"""
    try:
        result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        processes = []
        for line in result.stdout.split('\n'):
            if 'meilisearch' in line.lower() or 'sync' in line.lower():
                if 'grep' not in line and line.strip():
                    processes.append(line.strip())
        
        return processes
    except Exception as e:
        print(f"查找进程失败: {e}")
        return []

def stop_sync_process(pid):
    """停止同步进程"""
    try:
        # 先尝试优雅停止
        subprocess.run(["kill", "-TERM", str(pid)], check=True)
        print(f"已发送 TERM 信号给进程 {pid}")
        return True
    except subprocess.CalledProcessError:
        try:
            # 强制停止
            subprocess.run(["kill", "-KILL", str(pid)], check=True)
            print(f"已强制停止进程 {pid}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"停止进程 {pid} 失败: {e}")
            return False

async def main():
    print("🔧 Meilisearch 负载管理工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看当前状态")
        print("2. 查看活跃任务")
        print("3. 取消所有排队任务")
        print("4. 停止同步进程")
        print("5. 重启 Meilisearch 容器")
        print("6. 查看系统资源使用")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-6): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        
        elif choice == "1":
            # 查看当前状态
            async with MeilisearchManager() as manager:
                print("\n📊 当前状态:")
                
                # 索引统计
                stats = await manager.get_index_stats()
                if stats:
                    doc_count = stats.get('numberOfDocuments', 0)
                    print(f"   索引文档数: {doc_count:,}")
                
                # 活跃任务
                tasks = await manager.get_active_tasks()
                print(f"   活跃任务数: {len(tasks)}")
                
                # 进程信息
                processes = find_sync_processes()
                print(f"   相关进程数: {len(processes)}")
        
        elif choice == "2":
            # 查看活跃任务
            async with MeilisearchManager() as manager:
                tasks = await manager.get_active_tasks()
                
                if tasks:
                    print(f"\n📋 发现 {len(tasks)} 个活跃任务:")
                    for i, task in enumerate(tasks[:10], 1):
                        task_type = task.get('type', 'unknown')
                        status = task.get('status', 'unknown')
                        uid = task.get('uid', 'unknown')
                        
                        print(f"   {i}. 任务 {uid}: {task_type} ({status})")
                        
                        if 'details' in task:
                            details = task['details']
                            if 'receivedDocuments' in details:
                                received = details['receivedDocuments']
                                indexed = details.get('indexedDocuments', 0)
                                print(f"      进度: {indexed}/{received}")
                else:
                    print("\n✅ 没有活跃任务")
        
        elif choice == "3":
            # 取消排队任务
            async with MeilisearchManager() as manager:
                print("\n⚠️  正在取消所有排队中的任务...")
                result = await manager.cancel_all_tasks()
                
                if result:
                    canceled_count = result.get('taskUid', 0)
                    print(f"✅ 已取消 {canceled_count} 个任务")
                else:
                    print("❌ 取消任务失败")
        
        elif choice == "4":
            # 停止同步进程
            processes = find_sync_processes()
            
            if processes:
                print(f"\n发现 {len(processes)} 个相关进程:")
                for i, proc in enumerate(processes, 1):
                    print(f"   {i}. {proc}")
                
                try:
                    proc_choice = input("\n请输入要停止的进程编号 (回车取消): ").strip()
                    if proc_choice:
                        proc_idx = int(proc_choice) - 1
                        if 0 <= proc_idx < len(processes):
                            # 提取 PID
                            proc_line = processes[proc_idx]
                            pid = proc_line.split()[1]
                            
                            confirm = input(f"确认停止进程 {pid}? (y/N): ").strip().lower()
                            if confirm == 'y':
                                if stop_sync_process(pid):
                                    print("✅ 进程已停止")
                                else:
                                    print("❌ 停止进程失败")
                        else:
                            print("无效的进程编号")
                except (ValueError, IndexError):
                    print("无效输入")
            else:
                print("\n没有找到相关进程")
        
        elif choice == "5":
            # 重启容器
            confirm = input("\n⚠️  确认重启 Meilisearch 容器? (y/N): ").strip().lower()
            if confirm == 'y':
                try:
                    print("正在重启容器...")
                    subprocess.run(["docker", "restart", "pan_so_meilisearch"], check=True)
                    print("✅ 容器重启成功")
                except subprocess.CalledProcessError as e:
                    print(f"❌ 容器重启失败: {e}")
        
        elif choice == "6":
            # 查看系统资源
            try:
                print("\n💻 系统资源使用:")
                
                # CPU 和内存
                result = subprocess.run(
                    ["top", "-bn1", "-p", "2777379"], 
                    capture_output=True, 
                    text=True, 
                    timeout=5
                )
                
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '2777379' in line:
                            parts = line.split()
                            if len(parts) >= 9:
                                cpu = parts[8]
                                mem = parts[9]
                                print(f"   Meilisearch CPU: {cpu}%")
                                print(f"   Meilisearch 内存: {mem}%")
                
                # 磁盘 I/O
                result = subprocess.run(
                    ["iotop", "-b", "-n1", "-p", "2777379"], 
                    capture_output=True, 
                    text=True, 
                    timeout=5
                )
                
                if result.returncode == 0:
                    print("   磁盘 I/O 信息:")
                    for line in result.stdout.split('\n'):
                        if '2777379' in line:
                            print(f"   {line}")
                
            except Exception as e:
                print(f"获取系统资源信息失败: {e}")
        
        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"程序执行失败: {e}")
