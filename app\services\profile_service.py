"""
个人信息管理服务
"""

from typing import Optional, Dict, Any
from tortoise.transactions import in_transaction
from datetime import datetime, timezone
import logging

from app.models.user import User
from app.models.profile_models import UserProfileUpdateRequest
from app.services.points_service import PointsService

logger = logging.getLogger(__name__)


class ProfileService:
    """个人信息管理服务类"""

    @classmethod
    async def get_user_profile_detail(cls, user: User) -> Dict[str, Any]:
        """获取用户详细资料信息"""
        # 确保加载关联数据
        await user.fetch_related("role")

        # 获取当前头像URL
        current_avatar_url = await user.get_current_avatar_url()

        # 构建详细资料数据
        profile_data = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "nickname": user.nickname,
            "avatar": current_avatar_url,
            "status": user.status,
            "email_verified": user.email_verified,
            "points": user.points,
            "title": PointsService.get_title_by_points(user.points),
            "last_login_at": user.last_login_at,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            # 个人信息管理相关字段
            "last_nickname_change": user.last_nickname_change,
            "nickname_change_count": user.nickname_change_count,
            "can_change_nickname": user.can_change_nickname(),
            "next_nickname_change_date": user.get_next_nickname_change_date(),
            # 角色信息
            "role": (
                {
                    "id": user.role.id,
                    "name": user.role.name,
                    "display_name": user.role.display_name,
                    "description": user.role.description,
                    "permissions": user.role.permissions,
                }
                if user.role
                else None
            ),
        }

        return profile_data

    @classmethod
    async def update_user_profile(
        cls,
        user: User,
        profile_data: UserProfileUpdateRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> User:
        """批量更新用户基础信息"""
        async with in_transaction():
            updated_fields = []

            # 更新昵称（如果提供且不同）
            if (
                profile_data.nickname is not None
                and profile_data.nickname != user.nickname
            ):
                # 检查昵称修改权限
                if not user.can_change_nickname():
                    next_change_date = user.get_next_nickname_change_date()
                    raise ValueError(
                        f"昵称每年只能修改一次，下次可修改时间：{next_change_date}"
                    )

                # 检查昵称唯一性
                existing_user = (
                    await User.filter(nickname=profile_data.nickname)
                    .exclude(id=user.id)
                    .first()
                )
                if existing_user:
                    raise ValueError("该昵称已被使用")

                # 记录昵称修改历史
                from app.models.user import UserNicknameHistory

                await UserNicknameHistory.create(
                    user=user,
                    old_nickname=user.nickname,
                    new_nickname=profile_data.nickname,
                    change_reason="批量更新个人信息",
                    ip_address=ip_address,
                    user_agent=user_agent,
                )

                user.nickname = profile_data.nickname
                user.last_nickname_change = datetime.now(timezone.utc)
                user.nickname_change_count += 1
                updated_fields.append("nickname")

            # 保存更新
            if updated_fields:
                await user.save()
                logger.info(
                    f"用户 {user.username} 更新了个人信息: {', '.join(updated_fields)}"
                )

            return user

    @classmethod
    async def change_password(cls, user: User, old_password: str, new_password: str):
        """修改密码"""
        # 验证旧密码
        if not user.verify_password(old_password):
            raise ValueError("旧密码不正确")

        # 检查新密码是否与旧密码相同
        if user.verify_password(new_password):
            raise ValueError("新密码不能与旧密码相同")

        async with in_transaction():
            # 设置新密码
            user.set_password(new_password)
            await user.save()

            # 清除所有会话（强制重新登录）
            from app.models.user import UserSession

            await UserSession.filter(user=user).update(is_active=False)

            logger.info(f"用户 {user.username} 修改了密码")

    @classmethod
    async def get_user_statistics(cls, user: User) -> Dict[str, Any]:
        """获取用户统计信息"""
        from app.models.help_request import HelpRequest, HelpAnswer
        from app.models.user import PointsTransaction

        # 获取求助统计
        help_requests_count = await HelpRequest.filter(requester=user).count()

        # 获取回答统计
        help_answers_count = await HelpAnswer.filter(answerer=user).count()

        # 获取积分变动统计
        points_earned = await PointsTransaction.filter(user=user, amount__gt=0).count()
        points_spent = await PointsTransaction.filter(user=user, amount__lt=0).count()

        # 获取昵称修改统计
        nickname_changes = user.nickname_change_count

        return {
            "help_requests_count": help_requests_count,
            "help_answers_count": help_answers_count,
            "points_earned_transactions": points_earned,
            "points_spent_transactions": points_spent,
            "nickname_changes_count": nickname_changes,
            "account_age_days": (datetime.now(timezone.utc) - user.created_at).days,
        }

    @classmethod
    async def get_user_activity_summary(cls, user: User) -> Dict[str, Any]:
        """获取用户活动摘要"""
        from app.models.help_request import HelpRequest, HelpAnswer
        from app.models.user import PointsTransaction

        # 最近的求助
        recent_requests = (
            await HelpRequest.filter(requester=user).order_by("-created_at").limit(5)
        )

        # 最近的回答
        recent_answers = (
            await HelpAnswer.filter(answerer=user).order_by("-created_at").limit(5)
        )

        # 最近的积分变动
        recent_transactions = (
            await PointsTransaction.filter(user=user).order_by("-created_at").limit(10)
        )

        return {
            "recent_help_requests": [
                {
                    "id": req.id,
                    "title": req.title,
                    "status": req.status,
                    "created_at": req.created_at,
                }
                for req in recent_requests
            ],
            "recent_help_answers": [
                {
                    "id": ans.id,
                    "resource_title": ans.resource_title,
                    "help_request_id": ans.help_request_id,
                    "created_at": ans.created_at,
                }
                for ans in recent_answers
            ],
            "recent_points_transactions": [
                {
                    "id": trans.id,
                    "amount": trans.amount,
                    "transaction_type": trans.transaction_type,
                    "description": trans.description,
                    "created_at": trans.created_at,
                }
                for trans in recent_transactions
            ],
        }
