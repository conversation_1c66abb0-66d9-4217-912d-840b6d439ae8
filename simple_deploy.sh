#!/bin/bash

# 简化版Meilisearch同步架构部署脚本
# 使用现有Docker镜像，避免网络问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "简化版Meilisearch同步架构部署"
echo "=========================================="

# 1. 停止高I/O服务
log_info "1. 停止当前高I/O服务..."
pm2 stop meilisearch-listener 2>/dev/null || log_warn "meilisearch-listener未运行"

# 2. 清理Meilisearch任务队列
log_info "2. 清理Meilisearch任务队列..."
curl -X POST 'http://localhost:7700/tasks/cancel-tasks' \
    -H 'Authorization: Bearer masterKey' \
    -H 'Content-Type: application/json' \
    -d '{"statuses": ["enqueued", "processing"]}' >/dev/null 2>&1 || log_warn "任务取消可能失败"

# 3. 应用数据库迁移
log_info "3. 应用数据库迁移..."
if [ -f "migrations/2_create_sync_log_table.sql" ]; then
    docker exec -i pan_so_postgres psql -U pan_so_user -d pan_so_db < migrations/2_create_sync_log_table.sql
    log_info "数据库迁移完成"
else
    log_error "迁移文件不存在"
    exit 1
fi

# 4. 重启Meilisearch容器（使用现有镜像）
log_info "4. 重启Meilisearch容器..."
docker restart pan_so_meilisearch

# 等待Meilisearch启动
log_info "等待Meilisearch启动..."
for i in {1..30}; do
    if curl -s http://localhost:7700/health >/dev/null 2>&1; then
        log_info "Meilisearch启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        log_error "Meilisearch启动超时"
        exit 1
    fi
    sleep 2
done

# 5. 启动新的同步服务
log_info "5. 启动新的同步服务..."
pm2 delete meilisearch-listener 2>/dev/null || log_warn "meilisearch-listener未找到"
pm2 start pan_so_pm2.json --only meilisearch-scheduler
pm2 restart pan-so-api

# 6. 验证部署
log_info "6. 验证部署..."
sleep 5

# 检查Meilisearch
if curl -s -H "Authorization: Bearer masterKey" http://localhost:7700/health | grep -q "available"; then
    log_info "✓ Meilisearch服务正常"
else
    log_error "✗ Meilisearch服务异常"
    exit 1
fi

# 检查新同步服务
if pm2 list | grep -q "meilisearch-scheduler.*online"; then
    log_info "✓ 新同步服务运行正常"
else
    log_error "✗ 新同步服务异常"
    exit 1
fi

# 检查任务队列
queue_size=$(curl -s -H "Authorization: Bearer masterKey" \
    "http://localhost:7700/tasks?statuses=enqueued&limit=1" | jq -r '.total // 0' 2>/dev/null)

if [ "$queue_size" != "null" ] && [ "$queue_size" != "" ]; then
    log_info "✓ 当前任务队列大小: $queue_size"
else
    log_warn "⚠ 无法获取任务队列信息"
fi

echo "=========================================="
echo "部署完成！"
echo "=========================================="
log_info "新架构已启动，主要改进："
log_info "- 实时同步 → 定时批量同步（60秒间隔）"
log_info "- 操作去重和合并"
log_info "- 资源使用限制"
log_info ""
log_info "监控命令："
log_info "- pm2 logs meilisearch-scheduler  # 查看同步日志"
log_info "- ./monitor_sync_performance.sh  # 性能监控"
log_info "- sudo iotop -ao                 # 查看I/O状况"
echo "=========================================="
