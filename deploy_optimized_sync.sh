#!/bin/bash

# Meilisearch优化同步架构部署脚本
# 适用于4核8GB服务器环境

set -e  # 遇到错误立即退出

echo "=========================================="
echo "开始部署Meilisearch优化同步架构"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ $total_mem -lt 7000 ]; then
        log_warn "系统内存少于8GB ($total_mem MB)，可能影响性能"
    fi
    
    # 检查CPU核心数
    cpu_cores=$(nproc)
    if [ $cpu_cores -lt 4 ]; then
        log_warn "CPU核心数少于4核 ($cpu_cores 核)，可能影响性能"
    fi
    
    log_info "系统资源检查完成: ${cpu_cores}核CPU, ${total_mem}MB内存"
}

# 备份当前配置
backup_configs() {
    log_info "备份当前配置..."
    
    backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份PM2配置
    if [ -f "pan_so_pm2.json" ]; then
        cp pan_so_pm2.json "$backup_dir/"
        log_info "已备份PM2配置到 $backup_dir/"
    fi
    
    # 备份Docker配置
    if [ -f "docker-compose.yml" ]; then
        cp docker-compose.yml "$backup_dir/"
        log_info "已备份Docker配置到 $backup_dir/"
    fi
    
    echo "$backup_dir" > .last_backup_path
    log_info "配置备份完成"
}

# 停止当前服务
stop_current_services() {
    log_info "停止当前服务..."
    
    # 停止PM2服务
    log_info "停止PM2服务..."
    pm2 stop meilisearch-listener 2>/dev/null || log_warn "meilisearch-listener未运行"
    pm2 delete meilisearch-listener 2>/dev/null || log_warn "meilisearch-listener未找到"
    
    # 停止其他相关服务
    pm2 stop pan-so-api 2>/dev/null || log_warn "pan-so-api停止失败"
    
    log_info "PM2服务停止完成"
}

# 应用数据库迁移
apply_database_migration() {
    log_info "应用数据库迁移..."
    
    # 检查PostgreSQL连接
    if ! docker exec pan_so_postgres pg_isready -U pan_so_user -d pan_so_db >/dev/null 2>&1; then
        log_error "PostgreSQL数据库连接失败"
        exit 1
    fi
    
    # 应用同步日志表迁移
    if [ -f "migrations/2_create_sync_log_table.sql" ]; then
        log_info "创建同步日志表..."
        docker exec -i pan_so_postgres psql -U pan_so_user -d pan_so_db < migrations/2_create_sync_log_table.sql
        log_info "同步日志表创建完成"
    else
        log_error "迁移文件不存在: migrations/2_create_sync_log_table.sql"
        exit 1
    fi
}

# 重启Docker服务
restart_docker_services() {
    log_info "重启Docker服务..."

    # 检查Meilisearch容器是否存在
    if docker ps -a | grep -q "pan_so_meilisearch"; then
        log_info "发现现有Meilisearch容器，直接重启..."
        docker restart pan_so_meilisearch
    else
        log_info "创建新的Meilisearch容器..."
        docker compose up -d meilisearch
    fi

    # 等待Meilisearch启动
    log_info "等待Meilisearch启动..."
    for i in {1..30}; do
        if curl -s http://localhost:7700/health >/dev/null 2>&1; then
            log_info "Meilisearch启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Meilisearch启动超时"
            exit 1
        fi
        sleep 2
    done

    # 检查PostgreSQL容器
    if docker ps -a | grep -q "pan_so_postgres"; then
        log_info "重启PostgreSQL容器..."
        docker restart pan_so_postgres
    else
        log_info "创建PostgreSQL容器..."
        docker compose up -d postgres
    fi

    log_info "Docker服务重启完成"
}

# 启动新的PM2服务
start_new_services() {
    log_info "启动新的PM2服务..."
    
    # 启动新的定时同步服务
    pm2 start pan_so_pm2.json --only meilisearch-scheduler
    
    # 重启主API服务
    pm2 restart pan-so-api
    
    # 显示PM2状态
    pm2 status
    
    log_info "PM2服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查Meilisearch状态
    if curl -s -H "Authorization: Bearer masterKey" http://localhost:7700/health | grep -q "available"; then
        log_info "✓ Meilisearch服务正常"
    else
        log_error "✗ Meilisearch服务异常"
        return 1
    fi
    
    # 检查数据库连接
    if docker exec pan_so_postgres pg_isready -U pan_so_user -d pan_so_db >/dev/null 2>&1; then
        log_info "✓ PostgreSQL连接正常"
    else
        log_error "✗ PostgreSQL连接异常"
        return 1
    fi
    
    # 检查同步日志表
    if docker exec pan_so_postgres psql -U pan_so_user -d pan_so_db -c "SELECT COUNT(*) FROM meilisearch_sync_log;" >/dev/null 2>&1; then
        log_info "✓ 同步日志表创建成功"
    else
        log_error "✗ 同步日志表创建失败"
        return 1
    fi
    
    # 检查PM2服务
    if pm2 list | grep -q "meilisearch-scheduler.*online"; then
        log_info "✓ 定时同步服务运行正常"
    else
        log_error "✗ 定时同步服务异常"
        return 1
    fi
    
    log_info "部署验证完成"
    return 0
}

# 清理旧任务队列
cleanup_old_tasks() {
    log_info "清理Meilisearch旧任务队列..."
    
    # 取消所有排队和处理中的任务
    curl -X POST 'http://localhost:7700/tasks/cancel-tasks' \
        -H 'Authorization: Bearer masterKey' \
        -H 'Content-Type: application/json' \
        -d '{"statuses": ["enqueued", "processing"]}' >/dev/null 2>&1
    
    log_info "旧任务队列清理完成"
}

# 主部署流程
main() {
    log_info "开始执行部署流程..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查必要文件
    required_files=("pan_so_pm2.json" "docker-compose.yml" "migrations/2_create_sync_log_table.sql")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "必要文件不存在: $file"
            exit 1
        fi
    done
    
    # 执行部署步骤
    check_system_resources
    backup_configs
    stop_current_services
    cleanup_old_tasks
    apply_database_migration
    restart_docker_services
    start_new_services
    
    # 等待服务稳定
    log_info "等待服务稳定..."
    sleep 10
    
    # 验证部署
    if verify_deployment; then
        log_info "=========================================="
        log_info "部署成功完成！"
        log_info "=========================================="
        log_info "新架构特性："
        log_info "- 定时批量同步 (60秒间隔)"
        log_info "- 操作去重和合并"
        log_info "- 资源使用限制"
        log_info "- 背压控制机制"
        log_info ""
        log_info "监控命令："
        log_info "- pm2 logs meilisearch-scheduler  # 查看同步日志"
        log_info "- pm2 monit                       # 监控资源使用"
        log_info "- docker stats                    # 监控容器资源"
        log_info "=========================================="
    else
        log_error "部署验证失败，请检查日志"
        exit 1
    fi
}

# 执行主函数
main "$@"
