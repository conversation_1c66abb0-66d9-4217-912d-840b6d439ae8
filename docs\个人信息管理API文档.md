# 个人信息管理API文档

## 概述

个人信息管理模块提供了完整的用户个人信息管理功能，包括基础信息编辑、头像上传、邮箱更改、密码修改、昵称管理等核心功能，以及积分历史、求助记录等扩展功能。

## 基础信息

- **基础路径**: `/api/profile`
- **认证方式**: <PERSON><PERSON> (JWT)
- **响应格式**: JSON

## 核心功能接口

### 1. 查询用户个人信息

**接口地址**: `GET /api/profile/me`

**功能描述**: 获取当前用户的完整个人信息档案

**请求参数**: 无

**响应示例**:
```json
{
  "status": "success",
  "message": "获取个人信息成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "avatar": "https://cdn.example.com/avatars/user1.jpg",
    "phone": "13800138000",
    "status": "active",
    "email_verified": true,
    "points": 150,
    "title": "云盘勘探员",
    "last_login_at": "2025-07-31T10:30:00Z",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-07-31T10:30:00Z",
    "last_nickname_change": "2025-06-01T12:00:00Z",
    "nickname_change_count": 1,
    "can_change_nickname": false,
    "next_nickname_change_date": "2026-06-01T12:00:00Z",
    "role": {
      "id": 2,
      "name": "user",
      "display_name": "普通用户",
      "description": "普通用户角色",
      "permissions": ["read:resource", "create:help_request"]
    }
  }
}
```

### 2. 批量更新用户基础信息

**接口地址**: `PUT /api/profile/me`

**功能描述**: 批量更新用户的基础信息（昵称、手机号等）

**请求参数**:
```json
{
  "nickname": "新昵称",
  "phone": "13900139000"
}
```

**参数说明**:
- `nickname` (可选): 新昵称，2-100字符，只能包含中文、字母、数字、下划线、连字符
- `phone` (可选): 手机号，必须是有效的中国大陆手机号格式

**响应示例**:
```json
{
  "status": "success",
  "message": "个人信息更新成功",
  "data": {
    "id": 1,
    "nickname": "新昵称",
    "phone": "13900139000",
    "updated_at": "2025-07-31T10:30:00Z"
  }
}
```

### 3. 更改邮箱（发送验证邮件）

**接口地址**: `POST /api/profile/change-email`

**功能描述**: 请求更改邮箱地址，系统会发送验证邮件到新邮箱

**请求参数**:
```json
{
  "new_email": "<EMAIL>",
  "password": "当前密码"
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "邮箱更改验证邮件已发送，请查收邮件并点击验证链接",
  "data": {
    "request_id": 123,
    "new_email": "<EMAIL>",
    "expires_at": "2025-08-01T10:30:00Z"
  }
}
```

### 4. 验证邮箱更改

**接口地址**: `POST /api/profile/verify-email-change`

**功能描述**: 验证邮箱更改请求（通过邮件中的验证链接）

**请求参数**:
```json
{
  "token": "验证令牌"
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "邮箱更改成功",
  "data": {
    "user_id": 1,
    "new_email": "<EMAIL>",
    "verified_at": "2025-07-31T10:30:00Z"
  }
}
```

### 5. 上传头像

**接口地址**: `POST /api/profile/upload-avatar`

**功能描述**: 上传用户头像，支持JPG、PNG、GIF、WebP格式，最大5MB

**请求参数**: 
- `file`: 图片文件 (multipart/form-data)

**文件要求**:
- 格式: JPG, JPEG, PNG, GIF, WebP
- 大小: 最大5MB
- 尺寸: 建议正方形，系统会自动生成多种尺寸

**响应示例**:
```json
{
  "status": "success",
  "message": "头像上传成功",
  "data": {
    "avatar_id": 456,
    "file_path": "/uploads/avatars/20250731/1/uuid.jpg",
    "cdn_url": "https://cdn.example.com/avatars/20250731/1/uuid.jpg",
    "file_size": 102400,
    "width": 400,
    "height": 400
  }
}
```

### 6. 修改密码

**接口地址**: `POST /api/profile/change-password`

**功能描述**: 修改用户密码，需要验证旧密码

**请求参数**:
```json
{
  "old_password": "旧密码",
  "new_password": "新密码"
}
```

**密码要求**:
- 长度: 8-128字符
- 必须包含: 大写字母、小写字母、数字
- 不能与旧密码相同

**响应示例**:
```json
{
  "status": "success",
  "message": "密码修改成功，请重新登录",
  "data": {
    "changed_at": "2025-07-31T10:30:00Z"
  }
}
```

### 7. 修改昵称

**接口地址**: `POST /api/profile/change-nickname`

**功能描述**: 修改用户昵称，每年只能修改一次

**请求参数**:
```json
{
  "nickname": "新昵称",
  "reason": "修改原因（可选）"
}
```

**昵称要求**:
- 长度: 2-100字符
- 格式: 只能包含中文、字母、数字、下划线、连字符
- 唯一性: 不能与其他用户重复
- 敏感词: 不能包含系统保留词和敏感词
- 频率: 每年只能修改一次

**响应示例**:
```json
{
  "status": "success",
  "message": "昵称修改成功",
  "data": {
    "user_id": 1,
    "old_nickname": "旧昵称",
    "new_nickname": "新昵称",
    "next_change_date": "2026-07-31T10:30:00Z",
    "changed_at": "2025-07-31T10:30:00Z"
  }
}
```

## 扩展功能接口

### 8. 积分获取日志

**接口地址**: `GET /api/profile/points-history`

**功能描述**: 分页查询用户积分变动记录

**请求参数**:
- `page` (可选): 页码，默认1
- `size` (可选): 每页数量，默认20

**响应示例**:
```json
{
  "status": "success",
  "message": "获取积分历史成功",
  "data": {
    "total": 50,
    "page": 1,
    "size": 20,
    "pages": 3,
    "transactions": [
      {
        "id": 123,
        "amount": 5,
        "balance_after": 150,
        "transaction_type": "answer_accepted",
        "description": "采纳回答奖励",
        "related_id": 456,
        "created_at": "2025-07-31T10:30:00Z"
      }
    ]
  }
}
```

### 9. 我的求助列表

**接口地址**: `GET /api/profile/help-requests`

**功能描述**: 分页查询用户发布的求助信息

**请求参数**:
- `page` (可选): 页码，默认1
- `size` (可选): 每页数量，默认20
- `status_filter` (可选): 状态过滤，可选值: open, resolved, closed

**响应示例**:
```json
{
  "status": "success",
  "message": "获取求助列表成功",
  "data": {
    "total": 10,
    "page": 1,
    "size": 20,
    "pages": 1,
    "requests": [
      {
        "id": 789,
        "title": "寻找某电影资源",
        "description": "详细描述...",
        "resource_type": "video",
        "cloud_disk_types": ["baidu", "quark"],
        "status": "open",
        "answer_count": 3,
        "view_count": 25,
        "created_at": "2025-07-30T15:00:00Z",
        "updated_at": "2025-07-31T10:00:00Z",
        "resolved_at": null
      }
    ]
  }
}
```

### 10. 我的回答列表

**接口地址**: `GET /api/profile/help-answers`

**功能描述**: 分页查询用户的回答记录

**请求参数**:
- `page` (可选): 页码，默认1
- `size` (可选): 每页数量，默认20

**响应示例**:
```json
{
  "status": "success",
  "message": "获取回答列表成功",
  "data": {
    "total": 15,
    "page": 1,
    "size": 20,
    "pages": 1,
    "answers": [
      {
        "id": 321,
        "resource_title": "电影名称",
        "resource_link": "https://pan.baidu.com/s/xxx",
        "cloud_disk_type": "baidu",
        "additional_info": "提取码: abcd",
        "is_accepted": true,
        "accepted_at": "2025-07-31T09:00:00Z",
        "created_at": "2025-07-30T20:00:00Z",
        "help_request": {
          "id": 789,
          "title": "寻找某电影资源",
          "status": "resolved"
        }
      }
    ]
  }
}
```

### 11. 用户统计信息

**接口地址**: `GET /api/profile/statistics`

**功能描述**: 获取用户的统计信息

**响应示例**:
```json
{
  "status": "success",
  "message": "获取统计信息成功",
  "data": {
    "help_requests_count": 10,
    "help_answers_count": 15,
    "points_earned_transactions": 25,
    "points_spent_transactions": 5,
    "nickname_changes_count": 1,
    "account_age_days": 212
  }
}
```

### 12. 用户活动摘要

**接口地址**: `GET /api/profile/activity-summary`

**功能描述**: 获取用户最近的活动摘要

**响应示例**:
```json
{
  "status": "success",
  "message": "获取活动摘要成功",
  "data": {
    "recent_help_requests": [
      {
        "id": 789,
        "title": "寻找某电影资源",
        "status": "open",
        "created_at": "2025-07-30T15:00:00Z"
      }
    ],
    "recent_help_answers": [
      {
        "id": 321,
        "resource_title": "电影名称",
        "help_request_id": 789,
        "created_at": "2025-07-30T20:00:00Z"
      }
    ],
    "recent_points_transactions": [
      {
        "id": 123,
        "amount": 5,
        "transaction_type": "answer_accepted",
        "description": "采纳回答奖励",
        "created_at": "2025-07-31T10:30:00Z"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 业务规则

### 昵称修改规则
1. **频率限制**: 每年只能修改一次
2. **格式要求**: 2-100字符，只能包含中文、字母、数字、下划线、连字符
3. **唯一性**: 不能与其他用户重复
4. **敏感词过滤**: 不能包含系统保留词和敏感词
5. **历史记录**: 所有修改都会记录历史

### 头像上传规则
1. **文件格式**: JPG, JPEG, PNG, GIF, WebP
2. **文件大小**: 最大5MB
3. **存储方案**: 支持本地存储、阿里云OSS、腾讯云COS、七牛云
4. **多尺寸**: 自动生成多种尺寸的头像
5. **历史保留**: 保留历史头像记录

### 邮箱更改规则
1. **验证流程**: 需要验证当前密码和新邮箱
2. **安全通知**: 同时向新旧邮箱发送通知
3. **令牌有效期**: 验证链接24小时有效
4. **唯一性**: 新邮箱不能被其他用户使用

### 密码修改规则
1. **强度要求**: 8-128字符，包含大小写字母和数字
2. **安全验证**: 需要验证旧密码
3. **会话清理**: 修改后清除所有登录会话
4. **不能重复**: 新密码不能与旧密码相同

## 头像存储方案对比

### 5万用户规模存储方案分析

#### 方案一：本地存储
**适用场景**: 小规模部署、成本敏感
- **优点**: 成本低、部署简单、数据完全可控
- **缺点**: 扩展性差、备份复杂、CDN需要额外配置
- **存储成本**: 假设平均每个头像200KB，5万用户约10GB存储
- **推荐配置**:
  - 存储路径: `/uploads/avatars/年月日/用户ID/`
  - 备份策略: 定期同步到云存储
  - CDN: 配置Nginx反向代理

#### 方案二：阿里云OSS（推荐）
**适用场景**: 中大规模部署、需要CDN加速
- **优点**: 高可用、自动备份、全球CDN、成本合理
- **缺点**: 依赖外部服务、需要配置访问密钥
- **存储成本**: 标准存储约0.12元/GB/月，5万用户约12元/月
- **流量成本**: CDN流量约0.24元/GB
- **推荐配置**:
  - Bucket: 私有读写，通过签名URL访问
  - CDN: 配置自定义域名
  - 图片处理: 使用OSS图片处理服务

#### 方案三：腾讯云COS
**适用场景**: 备选方案，功能类似OSS
- **优点**: 与阿里云OSS类似
- **缺点**: 与阿里云OSS类似
- **成本**: 与阿里云接近

#### 方案四：七牛云
**适用场景**: 图片处理需求较多
- **优点**: 强大的图片处理能力、免费额度较大
- **缺点**: 免费额度有限制
- **成本**: 有一定免费额度

### 推荐方案

**5万用户规模推荐使用阿里云OSS方案**:

1. **成本效益**: 月存储成本约12元，流量成本可控
2. **技术成熟**: 阿里云OSS服务稳定，文档完善
3. **扩展性好**: 支持自动扩容，无需担心存储空间
4. **CDN加速**: 全球节点，访问速度快
5. **安全性高**: 支持HTTPS、访问控制、防盗链

### 实施建议

1. **初期**: 可以使用本地存储，降低初期成本
2. **发展期**: 用户量增长时迁移到OSS
3. **成熟期**: 配置CDN、图片处理等高级功能
4. **备份策略**: 无论使用哪种方案，都要做好数据备份
