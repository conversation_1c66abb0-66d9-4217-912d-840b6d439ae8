# 项目依赖包说明

## 📦 新增依赖包（个人信息管理模块）

### 必需依赖

#### Pillow==10.4.0
**用途**: 图片处理库，用于头像上传功能
**功能**:
- 图片格式验证和转换
- 图片尺寸获取和调整
- 图片压缩和优化
- 支持多种图片格式（JPG, PNG, GIF, WebP等）

**安装命令**:
```bash
pip install Pillow==10.4.0
```

### 可选依赖（云存储SDK）

根据选择的存储方案安装对应的SDK：

#### oss2==2.18.4
**用途**: 阿里云对象存储服务SDK
**功能**:
- 文件上传到阿里云OSS
- 文件访问权限管理
- CDN集成支持
- 批量操作支持

**安装命令**:
```bash
pip install oss2==2.18.4
```

**配置要求**:
- 阿里云AccessKey ID
- 阿里云AccessKey Secret
- OSS Endpoint
- Bucket名称

#### cos-python-sdk-v5==1.9.30
**用途**: 腾讯云对象存储服务SDK
**功能**:
- 文件上传到腾讯云COS
- 文件访问权限管理
- CDN集成支持
- 批量操作支持

**安装命令**:
```bash
pip install cos-python-sdk-v5==1.9.30
```

**配置要求**:
- 腾讯云SecretId
- 腾讯云SecretKey
- COS地域信息
- Bucket名称

#### qiniu==7.12.1
**用途**: 七牛云存储服务SDK
**功能**:
- 文件上传到七牛云
- 图片处理服务集成
- CDN加速支持
- 丰富的图片处理功能

**安装命令**:
```bash
pip install qiniu==7.12.1
```

**配置要求**:
- 七牛云AccessKey
- 七牛云SecretKey
- 存储空间名称
- 访问域名

### 开发测试依赖

#### pytest==8.3.2
**用途**: Python测试框架
**功能**:
- 单元测试执行
- 测试用例管理
- 测试报告生成
- 插件扩展支持

#### pytest-asyncio==0.23.8
**用途**: pytest的异步测试支持插件
**功能**:
- 异步函数测试
- 异步数据库操作测试
- 异步HTTP请求测试

**安装命令**:
```bash
pip install pytest==8.3.2 pytest-asyncio==0.23.8
```

## 🚀 安装指南

### 自动安装（推荐）

我们提供了自动安装脚本，可以交互式选择需要的依赖包：

#### Windows系统
```cmd
# 双击运行批处理文件
scripts\install_profile_dependencies.bat

# 或者在命令行中运行
cd scripts
install_profile_dependencies.bat
```

#### Linux/macOS系统
```bash
# 运行shell脚本
chmod +x scripts/install_profile_dependencies.sh
./scripts/install_profile_dependencies.sh

# 或者直接运行Python脚本
python3 scripts/install_profile_dependencies.py
```

### 手动安装

#### 基础安装（必需）
```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者只安装个人信息管理模块的必需依赖
pip install Pillow==10.4.0
```

### 云存储支持（可选）

根据你的存储方案选择安装：

#### 使用阿里云OSS
```bash
pip install oss2==2.18.4
```

#### 使用腾讯云COS
```bash
pip install cos-python-sdk-v5==1.9.30
```

#### 使用七牛云
```bash
pip install qiniu==7.12.1
```

#### 安装所有云存储SDK（推荐用于生产环境）
```bash
pip install oss2==2.18.4 cos-python-sdk-v5==1.9.30 qiniu==7.12.1
```

### 开发环境安装
```bash
# 安装开发和测试依赖
pip install pytest==8.3.2 pytest-asyncio==0.23.8

# 运行测试
pytest tests/test_profile_management.py -v
```

## ⚙️ 配置说明

### 存储配置示例

在 `app/config.yaml` 中配置存储方案：

```yaml
# 头像存储配置
avatar:
  # 存储类型选择: local, oss, cos, qiniu
  storage_type: "local"  # 默认本地存储
  
  # 本地存储配置
  local_path: "uploads"
  base_url: "/uploads"
  
  # 阿里云OSS配置
  oss:
    access_key_id: "your_access_key_id"
    access_key_secret: "your_access_key_secret"
    endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
    bucket_name: "your-bucket-name"
    cdn_domain: "cdn.your-domain.com"
  
  # 腾讯云COS配置
  cos:
    secret_id: "your_secret_id"
    secret_key: "your_secret_key"
    region: "ap-beijing"
    bucket_name: "your-bucket-name"
  
  # 七牛云配置
  qiniu:
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket_name: "your-bucket-name"
    domain: "your-domain.com"
```

## 🔧 故障排除

### 常见安装问题

#### Pillow安装失败
```bash
# Windows系统可能需要安装Visual C++构建工具
# 或者使用预编译的wheel包
pip install --upgrade pip
pip install Pillow==10.4.0 --no-cache-dir
```

#### 云存储SDK安装失败
```bash
# 清理缓存重新安装
pip cache purge
pip install oss2==2.18.4 --no-cache-dir
```

#### 测试依赖问题
```bash
# 确保Python版本兼容
python --version  # 需要Python 3.8+
pip install pytest==8.3.2 pytest-asyncio==0.23.8
```

### 依赖冲突解决

如果遇到依赖冲突，可以使用以下方法：

```bash
# 检查依赖冲突
pip check

# 使用虚拟环境隔离依赖
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 重新安装依赖
pip install -r requirements.txt
```

## 📊 依赖包大小和性能影响

| 包名 | 大小 | 用途 | 性能影响 |
|------|------|------|----------|
| Pillow | ~3MB | 图片处理 | 中等，仅在上传时使用 |
| oss2 | ~500KB | 阿里云存储 | 低，异步上传 |
| cos-python-sdk-v5 | ~1MB | 腾讯云存储 | 低，异步上传 |
| qiniu | ~300KB | 七牛云存储 | 低，异步上传 |
| pytest | ~1MB | 测试框架 | 无，仅开发环境 |

## 🔄 版本更新策略

### 定期更新
- 每季度检查依赖包更新
- 优先更新安全补丁
- 测试兼容性后再更新

### 更新命令
```bash
# 检查过期包
pip list --outdated

# 更新特定包
pip install --upgrade Pillow

# 更新所有包（谨慎使用）
pip install --upgrade -r requirements.txt
```

### 版本锁定
- 生产环境使用固定版本号
- 开发环境可以使用较新版本测试
- 重要依赖包避免跨大版本更新

---

**注意**: 云存储SDK为可选依赖，根据实际使用的存储方案安装对应的SDK即可。如果只使用本地存储，只需要安装Pillow即可。
