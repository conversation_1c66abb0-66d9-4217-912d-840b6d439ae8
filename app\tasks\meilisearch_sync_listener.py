import asyncio
import json
import logging
import signal
import asyncpg

from app.db.engine import init_db, close_db
from app.services.intelligent_batch_manager import intelligent_batch_manager, Priority
from app.utils.health_monitor import health_monitor
from app.utils.config import settings

# 配置日志记录
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


async def handle_notification(payload_str: str):
    """
    解析来自数据库的通知负载，并添加到批处理队列。
    """
    try:
        payload = json.loads(payload_str)
        operation = payload.get("operation")
        resource_key = payload.get("resource_key")

        if not operation or not resource_key:
            logging.warning(f"收到的负载无效: {payload_str}")
            return

        logging.debug(f"收到 {operation} 操作，资源KEY: {resource_key}")

        # 将操作添加到智能批量管理器
        priority = Priority.HIGH if operation == "DELETE" else Priority.NORMAL
        success = await intelligent_batch_manager.add_operation(
            operation_type=operation,
            resource_key=resource_key,
            priority=priority,
            user_triggered=True,  # 来自数据库通知的操作视为用户触发
        )

        if success:
            logging.debug(
                f"✅ 已将 {operation} 操作添加到智能批量管理器: {resource_key}"
            )
        else:
            logging.warning(
                f"❌ 添加 {operation} 操作到智能批量管理器失败: {resource_key}"
            )

    except json.JSONDecodeError:
        logging.error(f"无法解码JSON负载: {payload_str}")
    except Exception as e:
        logging.error(f"处理负载 '{payload_str}' 的通知时出错: {e}", exc_info=True)


async def listen_for_changes():
    """
    连接到PostgreSQL并监听 'resource_changes' 频道的通知。
    """
    db_creds = (
        settings.get("database", {})
        .get("connections", {})
        .get("default", {})
        .get("credentials", {})
    )
    if not db_creds.get("host"):
        logging.error("在配置中未找到数据库连接凭证。")
        return

    conn = None
    retry_count = 0
    max_retries = 10  # 最大重试次数
    base_delay = 1  # 基础延迟时间（秒）
    max_delay = 300  # 最大延迟时间（5分钟）

    while retry_count < max_retries:
        try:
            conn = await asyncpg.connect(
                user=db_creds.get("user"),
                password=db_creds.get("password"),
                database=db_creds.get("database"),
                host=db_creds.get("host"),
                port=db_creds.get("port"),
                timeout=30,  # 连接超时30秒
                command_timeout=60,  # 命令超时60秒
                server_settings={
                    "application_name": "meilisearch_sync_listener",
                    "tcp_keepalives_idle": "600",
                    "tcp_keepalives_interval": "30",
                    "tcp_keepalives_count": "3",
                },
            )

            async def notification_callback(connection, pid, channel, payload):
                # 忽略未使用的参数
                _ = connection, pid, channel
                await handle_notification(payload)

            await conn.add_listener("resource_changes", notification_callback)

            logging.info("监听服务已启动，等待来自 'resource_changes' 频道的通知...")
            retry_count = 0  # 重置重试计数器
            await asyncio.Future()  # 无限期运行

        except (asyncpg.exceptions.PostgresError, OSError) as e:
            retry_count += 1
            delay = min(base_delay * (2 ** (retry_count - 1)), max_delay)
            logging.error(
                f"数据库连接错误 (重试 {retry_count}/{max_retries}): {e}。将在{delay}秒后重试...",
                exc_info=True,
            )
            if conn and not conn.is_closed():
                await conn.close()
            await asyncio.sleep(delay)
        except Exception as e:
            retry_count += 1
            delay = min(base_delay * (2 ** (retry_count - 1)), max_delay)
            logging.error(
                f"发生未知错误 (重试 {retry_count}/{max_retries}): {e}。将在{delay}秒后重试...",
                exc_info=True,
            )
            if conn and not conn.is_closed():
                await conn.close()
            await asyncio.sleep(delay)
        finally:
            if conn and not conn.is_closed():
                await conn.close()
                logging.info("数据库监听连接已关闭。")

    # 如果重试次数耗尽，记录错误并退出
    logging.error(f"已达到最大重试次数 {max_retries}，监听服务停止。")
    raise RuntimeError(f"数据库连接失败，已重试 {max_retries} 次")


async def shutdown_handler():
    """优雅关闭处理器"""
    logging.info("收到关闭信号，开始优雅关闭...")

    # 停止智能批量管理器
    await intelligent_batch_manager.stop()

    # 停止健康监控
    await health_monitor.stop()

    # 关闭数据库连接
    await close_db()

    logging.info("所有服务已关闭")


def setup_signal_handlers():
    """设置信号处理器"""

    def signal_handler(signum, frame):
        _ = frame  # 忽略未使用的参数
        logging.info(f"收到信号 {signum}，准备关闭...")
        # 创建关闭任务
        asyncio.create_task(shutdown_handler())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """
    主函数，初始化服务并启动监听器。
    """
    logging.info("正在启动 Meilisearch 增量同步监听服务...")

    try:
        # 设置信号处理器
        setup_signal_handlers()

        # 初始化数据库
        logging.info("初始化数据库连接...")
        await init_db()

        # 启动智能批量管理器
        logging.info("启动智能批量管理器...")
        await intelligent_batch_manager.start()

        # 启动健康监控
        logging.info("启动健康监控...")
        await health_monitor.start()

        # 启动监听器
        logging.info("启动数据库通知监听器...")
        await listen_for_changes()

    except asyncio.CancelledError:
        logging.info("监听任务被取消。")
    except Exception as e:
        logging.error(f"服务启动失败: {e}", exc_info=True)
        raise
    finally:
        await shutdown_handler()


# 直接运行主函数
try:
    logging.info("启动Meilisearch增量同步监听服务...")
    asyncio.run(main())
except KeyboardInterrupt:
    logging.info("监听服务被用户停止。")
