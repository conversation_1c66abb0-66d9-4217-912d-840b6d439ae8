from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 创建资源求助表
        CREATE TABLE IF NOT EXISTS "help_requests" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "title" VARCHAR(200) NOT NULL,
            "description" TEXT,
            "cloud_disk_types" JSONB NOT NULL DEFAULT '[]',
            "resource_type" VARCHAR(20) NOT NULL DEFAULT 'other',
            "status" VARCHAR(20) NOT NULL DEFAULT 'open',
            "requester_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "answer_count" INT NOT NULL DEFAULT 0,
            "view_count" INT NOT NULL DEFAULT 0,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "resolved_at" TIMESTAMPTZ
        );
        COMMENT ON TABLE "help_requests" IS '资源求助表';
        COMMENT ON COLUMN "help_requests"."title" IS '资源名称';
        COMMENT ON COLUMN "help_requests"."description" IS '详细描述';
        COMMENT ON COLUMN "help_requests"."cloud_disk_types" IS '网盘类型列表';
        COMMENT ON COLUMN "help_requests"."resource_type" IS '资源类型';
        COMMENT ON COLUMN "help_requests"."status" IS '求助状态';
        COMMENT ON COLUMN "help_requests"."requester_id" IS '求助者';
        COMMENT ON COLUMN "help_requests"."answer_count" IS '回答数量';
        COMMENT ON COLUMN "help_requests"."view_count" IS '浏览次数';
        COMMENT ON COLUMN "help_requests"."created_at" IS '创建时间';
        COMMENT ON COLUMN "help_requests"."updated_at" IS '更新时间';
        COMMENT ON COLUMN "help_requests"."resolved_at" IS '解决时间';
        
        -- 创建求助回答表
        CREATE TABLE IF NOT EXISTS "help_answers" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "resource_title" VARCHAR(200) NOT NULL,
            "resource_link" VARCHAR(500) NOT NULL,
            "cloud_disk_type" VARCHAR(20) NOT NULL,
            "additional_info" TEXT,
            "should_archive" BOOL NOT NULL DEFAULT False,
            "help_request_id" INT NOT NULL REFERENCES "help_requests" ("id") ON DELETE CASCADE,
            "answerer_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "is_accepted" BOOL NOT NULL DEFAULT False,
            "accepted_at" TIMESTAMPTZ,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE "help_answers" IS '求助回答表';
        COMMENT ON COLUMN "help_answers"."resource_title" IS '资源标题';
        COMMENT ON COLUMN "help_answers"."resource_link" IS '资源链接';
        COMMENT ON COLUMN "help_answers"."cloud_disk_type" IS '网盘类型';
        COMMENT ON COLUMN "help_answers"."additional_info" IS '补充说明';
        COMMENT ON COLUMN "help_answers"."should_archive" IS '是否入库';
        COMMENT ON COLUMN "help_answers"."help_request_id" IS '关联的求助';
        COMMENT ON COLUMN "help_answers"."answerer_id" IS '回答者';
        COMMENT ON COLUMN "help_answers"."is_accepted" IS '是否被采纳';
        COMMENT ON COLUMN "help_answers"."accepted_at" IS '采纳时间';
        COMMENT ON COLUMN "help_answers"."created_at" IS '创建时间';
        COMMENT ON COLUMN "help_answers"."updated_at" IS '更新时间';
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS "idx_help_requests_requester_id" ON "help_requests" ("requester_id");
        CREATE INDEX IF NOT EXISTS "idx_help_requests_status" ON "help_requests" ("status");
        CREATE INDEX IF NOT EXISTS "idx_help_requests_resource_type" ON "help_requests" ("resource_type");
        CREATE INDEX IF NOT EXISTS "idx_help_requests_created_at" ON "help_requests" ("created_at");
        
        CREATE INDEX IF NOT EXISTS "idx_help_answers_help_request_id" ON "help_answers" ("help_request_id");
        CREATE INDEX IF NOT EXISTS "idx_help_answers_answerer_id" ON "help_answers" ("answerer_id");
        CREATE INDEX IF NOT EXISTS "idx_help_answers_is_accepted" ON "help_answers" ("is_accepted");
        CREATE INDEX IF NOT EXISTS "idx_help_answers_created_at" ON "help_answers" ("created_at");
        
        -- 添加约束：每个求助只能有一个被采纳的回答
        CREATE UNIQUE INDEX IF NOT EXISTS "idx_help_answers_unique_accepted" 
        ON "help_answers" ("help_request_id") 
        WHERE "is_accepted" = true;
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除索引
        DROP INDEX IF EXISTS "idx_help_answers_unique_accepted";
        DROP INDEX IF EXISTS "idx_help_answers_created_at";
        DROP INDEX IF EXISTS "idx_help_answers_is_accepted";
        DROP INDEX IF EXISTS "idx_help_answers_answerer_id";
        DROP INDEX IF EXISTS "idx_help_answers_help_request_id";
        
        DROP INDEX IF EXISTS "idx_help_requests_created_at";
        DROP INDEX IF EXISTS "idx_help_requests_resource_type";
        DROP INDEX IF EXISTS "idx_help_requests_status";
        DROP INDEX IF EXISTS "idx_help_requests_requester_id";
        
        -- 删除表
        DROP TABLE IF EXISTS "help_answers";
        DROP TABLE IF EXISTS "help_requests";
    """
