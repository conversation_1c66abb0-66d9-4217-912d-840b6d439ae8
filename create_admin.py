#!/usr/bin/env python3
"""
创建第一个管理员用户脚本
用于生产环境初始化管理员账户
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from app.models.user import User, Role
from app.core.tortoise_config import TORTOISE_ORM
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 管理员用户配置
ADMIN_CONFIG = {
    "username": "admin",
    "email": "<EMAIL>",
    "password": "Wsk1998107...",
    "nickname": "系统管理员",
}


async def create_default_roles():
    """创建默认角色"""
    logger.info("开始创建默认角色...")

    # 创建超级管理员角色
    admin_role, created = await Role.get_or_create(
        name="超级管理员",
        defaults={
            "display_name": "超级管理员",
            "description": "系统超级管理员，拥有所有权限",
            "permissions": ["*"],  # 所有权限
            "is_active": True,
        },
    )

    if created:
        logger.info(f"✅ 创建角色: {admin_role.display_name}")
    else:
        logger.info(f"📋 角色已存在: {admin_role.display_name}")

    # 创建普通用户角色
    user_role, created = await Role.get_or_create(
        name="普通用户",
        defaults={
            "display_name": "普通用户",
            "description": "普通用户，基础权限",
            "permissions": ["resource.view", "feedback.create"],
            "is_active": True,
        },
    )

    if created:
        logger.info(f"✅ 创建角色: {user_role.display_name}")
    else:
        logger.info(f"📋 角色已存在: {user_role.display_name}")

    return admin_role, user_role


async def create_admin_user():
    """创建管理员用户"""
    logger.info("开始创建管理员用户...")

    # 检查用户是否已存在
    existing_user = await User.filter(username=ADMIN_CONFIG["username"]).first()
    if existing_user:
        logger.warning(f"⚠️  用户 '{ADMIN_CONFIG['username']}' 已存在，跳过创建")
        return existing_user

    # 检查邮箱是否已存在
    existing_email = await User.filter(email=ADMIN_CONFIG["email"]).first()
    if existing_email:
        logger.warning(f"⚠️  邮箱 '{ADMIN_CONFIG['email']}' 已被使用，跳过创建")
        return existing_email

    # 获取超级管理员角色
    admin_role = await Role.filter(name="超级管理员").first()
    if not admin_role:
        logger.error("❌ 未找到超级管理员角色，请先创建角色")
        return None

    # 创建管理员用户
    try:
        admin_user = User(
            username=ADMIN_CONFIG["username"],
            email=ADMIN_CONFIG["email"],
            nickname=ADMIN_CONFIG["nickname"],
            role=admin_role,
            status="active",
            email_verified=True,
        )

        # 设置密码
        admin_user.set_password(ADMIN_CONFIG["password"])

        # 保存用户
        await admin_user.save()

        logger.info(f"✅ 管理员用户创建成功:")
        logger.info(f"   用户名: {admin_user.username}")
        logger.info(f"   邮箱: {admin_user.email}")
        logger.info(f"   昵称: {admin_user.nickname}")
        logger.info(f"   角色: {admin_role.display_name}")

        return admin_user

    except Exception as e:
        logger.error(f"❌ 创建管理员用户失败: {e}")
        return None


async def verify_admin_user():
    """验证管理员用户创建结果"""
    logger.info("验证管理员用户...")

    admin_user = await User.filter(username=ADMIN_CONFIG["username"]).first()
    if not admin_user:
        logger.error("❌ 管理员用户验证失败：用户不存在")
        return False

    await admin_user.fetch_related("role")

    logger.info(f"✅ 管理员用户验证成功:")
    logger.info(f"   ID: {admin_user.id}")
    logger.info(f"   用户名: {admin_user.username}")
    logger.info(f"   邮箱: {admin_user.email}")
    logger.info(f"   角色: {admin_user.role.display_name}")
    logger.info(f"   状态: {admin_user.status}")
    logger.info(f"   邮箱验证: {'是' if admin_user.email_verified else '否'}")

    return True


async def main():
    """主函数"""
    logger.info("🚀 开始初始化管理员用户...")

    try:
        # 初始化数据库连接
        logger.info("📡 连接数据库...")
        await Tortoise.init(config=TORTOISE_ORM)
        logger.info("✅ 数据库连接成功")

        # 创建默认角色
        await create_default_roles()

        # 创建管理员用户
        admin_user = await create_admin_user()

        if admin_user:
            # 验证创建结果
            success = await verify_admin_user()

            if success:
                logger.info("🎉 管理员用户初始化完成！")
                logger.info("📝 登录信息:")
                logger.info(f"   用户名: {ADMIN_CONFIG['username']}")
                logger.info(f"   密码: {ADMIN_CONFIG['password']}")
                logger.info(f"   邮箱: {ADMIN_CONFIG['email']}")
                return True
            else:
                logger.error("❌ 管理员用户验证失败")
                return False
        else:
            logger.error("❌ 管理员用户创建失败")
            return False

    except Exception as e:
        logger.error(f"❌ 初始化过程中发生错误: {e}")
        return False

    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        logger.info("📡 数据库连接已关闭")


if __name__ == "__main__":
    # 检查是否在正确的目录
    if not os.path.exists("app/config.yaml"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        print("   当前目录应包含 app/config.yaml 文件")
        sys.exit(1)

    # 运行主函数
    success = asyncio.run(main())

    if success:
        print("\n🎉 管理员用户创建成功！")
        print("现在您可以使用以下信息登录管理后台:")
        print(f"用户名: {ADMIN_CONFIG['username']}")
        print(f"密码: {ADMIN_CONFIG['password']}")
        sys.exit(0)
    else:
        print("\n❌ 管理员用户创建失败！")
        print("请检查日志信息并重试")
        sys.exit(1)
