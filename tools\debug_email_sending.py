#!/usr/bin/env python3
"""
邮件发送调试工具
专门用于调试和分析邮件发送过程中的问题
"""

import asyncio
import sys
import smtplib
import logging
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.email import EmailService
from app.utils.config import settings

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EmailDebugger:
    """邮件发送调试器"""
    
    def __init__(self):
        self.email_service = EmailService()
    
    def test_raw_smtp(self, to_email: str):
        """测试原始SMTP发送"""
        print("=== 原始SMTP发送测试 ===")
        
        server = None
        try:
            # 创建邮件
            msg = MIMEMultipart("alternative")
            msg["Subject"] = "Pan-So 原始SMTP测试"
            msg["From"] = f"Pan-So Team <{self.email_service.from_email}>"
            msg["To"] = to_email
            
            html_content = """
            <html>
            <body>
                <h2>原始SMTP测试邮件</h2>
                <p>这是一封通过原始SMTP发送的测试邮件。</p>
                <p>如果您收到此邮件，说明SMTP发送功能正常。</p>
            </body>
            </html>
            """
            html_part = MIMEText(html_content, "html", "utf-8")
            msg.attach(html_part)
            
            print(f"📧 创建邮件完成")
            print(f"   收件人: {to_email}")
            print(f"   发件人: {self.email_service.from_email}")
            
            # 建立连接
            print(f"🔗 连接到 {self.email_service.smtp_server}:{self.email_service.smtp_port}")
            server = smtplib.SMTP(self.email_service.smtp_server, self.email_service.smtp_port, timeout=15)
            server.set_debuglevel(1)  # 启用SMTP调试
            
            print("🔒 启动TLS加密...")
            server.starttls()
            
            print("🔑 进行SMTP认证...")
            server.login(self.email_service.username, self.email_service.password)
            
            print("📤 发送邮件...")
            send_result = server.send_message(msg)
            
            print(f"📊 发送结果: {send_result}")
            
            if send_result:
                print(f"⚠️  部分收件人失败: {send_result}")
                return False
            else:
                print("✅ 邮件发送成功")
                return True
                
        except smtplib.SMTPException as e:
            print(f"❌ SMTP错误: {e}")
            print(f"   错误类型: {type(e).__name__}")
            print(f"   错误详情: {str(e)}")
            
            # 特殊处理这个错误
            if "(-1, b'\\x00\\x00\\x00')" in str(e):
                print("🔍 检测到特殊错误码 (-1, b'\\x00\\x00\\x00')")
                print("   这通常表示邮件已发送但连接异常关闭")
                print("   建议检查收件箱确认是否收到邮件")
                return "maybe_success"
            
            return False
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            return False
        finally:
            if server:
                try:
                    print("🔚 关闭SMTP连接...")
                    server.quit()
                    print("✅ SMTP连接正常关闭")
                except Exception as quit_e:
                    print(f"⚠️  关闭连接时出现问题: {quit_e}")
                    try:
                        server.close()
                        print("🔧 强制关闭连接")
                    except:
                        print("❌ 无法关闭连接")
    
    async def test_email_service(self, to_email: str):
        """测试邮件服务发送"""
        print("\n=== 邮件服务发送测试 ===")
        
        try:
            await self.email_service._send_email(
                to_email=to_email,
                subject="Pan-So 邮件服务测试",
                html_content="""
                <html>
                <body>
                    <h2>邮件服务测试邮件</h2>
                    <p>这是一封通过Pan-So邮件服务发送的测试邮件。</p>
                    <p>如果您收到此邮件，说明邮件服务功能正常。</p>
                </body>
                </html>
                """
            )
            print("✅ 邮件服务发送成功")
            return True
        except Exception as e:
            print(f"❌ 邮件服务发送失败: {e}")
            return False
    
    def test_connection_only(self):
        """仅测试SMTP连接"""
        print("\n=== SMTP连接测试 ===")
        
        server = None
        try:
            print(f"🔗 连接到 {self.email_service.smtp_server}:{self.email_service.smtp_port}")
            server = smtplib.SMTP(self.email_service.smtp_server, self.email_service.smtp_port, timeout=15)
            
            print("🤝 EHLO握手...")
            server.ehlo()
            
            print("🔒 启动TLS加密...")
            server.starttls()
            
            print("🔑 进行SMTP认证...")
            server.login(self.email_service.username, self.email_service.password)
            
            print("✅ SMTP连接和认证完全正常")
            return True
            
        except Exception as e:
            print(f"❌ SMTP连接失败: {e}")
            return False
        finally:
            if server:
                try:
                    server.quit()
                except:
                    pass
    
    def analyze_error(self, error_msg: str):
        """分析错误信息"""
        print(f"\n=== 错误分析 ===")
        print(f"错误信息: {error_msg}")
        
        if "(-1, b'\\x00\\x00\\x00')" in error_msg:
            print("\n🔍 错误分析结果:")
            print("   错误码: (-1, b'\\x00\\x00\\x00')")
            print("   可能原因:")
            print("   1. 邮件实际已发送成功，但SMTP会话异常终止")
            print("   2. QQ邮箱服务器在某些情况下的特殊响应")
            print("   3. 网络连接在邮件发送后不稳定")
            print("   4. SMTP服务器提前关闭了连接")
            print("\n💡 建议:")
            print("   1. 检查收件箱确认是否收到邮件")
            print("   2. 如果收到邮件，说明发送实际成功")
            print("   3. 可以忽略此错误或改进错误处理")
            print("   4. 考虑增加重试机制")


async def main():
    """主函数"""
    print("Pan-So 邮件发送调试工具")
    print("=" * 50)
    
    debugger = EmailDebugger()
    
    # 获取测试邮箱
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("未输入邮箱地址，退出")
        return
    
    print(f"\n开始调试邮件发送到: {test_email}")
    
    # 1. 测试连接
    conn_ok = debugger.test_connection_only()
    
    if not conn_ok:
        print("❌ SMTP连接失败，无法继续测试")
        return
    
    # 2. 测试原始SMTP发送
    raw_result = debugger.test_raw_smtp(test_email)
    
    # 3. 测试邮件服务发送
    service_result = await debugger.test_email_service(test_email)
    
    # 4. 结果汇总
    print(f"\n=== 测试结果汇总 ===")
    print(f"SMTP连接: {'✅ 正常' if conn_ok else '❌ 失败'}")
    
    if raw_result == "maybe_success":
        print(f"原始SMTP: ⚠️  可能成功（遇到特殊错误）")
    else:
        print(f"原始SMTP: {'✅ 成功' if raw_result else '❌ 失败'}")
    
    print(f"邮件服务: {'✅ 成功' if service_result else '❌ 失败'}")
    
    # 5. 如果遇到特殊错误，提供分析
    if raw_result == "maybe_success" or not service_result:
        debugger.analyze_error("(-1, b'\\x00\\x00\\x00')")
    
    print(f"\n💡 重要提示:")
    print(f"   请检查邮箱 {test_email} 是否收到测试邮件")
    print(f"   如果收到邮件，说明发送功能实际正常")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n调试被用户中断")
    except Exception as e:
        print(f"\n调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
