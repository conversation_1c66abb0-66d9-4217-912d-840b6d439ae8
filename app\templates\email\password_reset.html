<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>密码重置 - 97盘搜</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white; 
            border-radius: 8px; 
            overflow: hidden; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .header { 
            background: linear-gradient(135deg, #dc3545, #c82333); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 28px; 
            font-weight: 300; 
        }
        .content { 
            padding: 40px 30px; 
            background: #fff; 
        }
        .content h2 { 
            color: #333; 
            margin-top: 0; 
            font-size: 24px; 
        }
        .content p { 
            margin: 16px 0; 
            font-size: 16px; 
            line-height: 1.6; 
        }
        .button { 
            display: inline-block; 
            padding: 15px 30px; 
            background: linear-gradient(135deg, #dc3545, #c82333); 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            font-weight: 500; 
            font-size: 16px; 
            margin: 20px 0; 
            transition: all 0.3s ease; 
        }
        .button:hover { 
            background: linear-gradient(135deg, #c82333, #a71e2a); 
            transform: translateY(-2px); 
        }
        .footer { 
            padding: 30px; 
            text-align: center; 
            color: #666; 
            font-size: 14px; 
            background: #f8f9fa; 
            border-top: 1px solid #e9ecef; 
        }
        .footer p { 
            margin: 8px 0; 
        }
        .divider { 
            height: 1px; 
            background: linear-gradient(to right, transparent, #ddd, transparent); 
            margin: 30px 0; 
        }
        .warning { 
            background: #fff3cd; 
            padding: 20px; 
            border-left: 4px solid #ffc107; 
            margin: 20px 0; 
            border-radius: 0 4px 4px 0; 
        }
        .security-tips { 
            background: #d1ecf1; 
            padding: 20px; 
            border-left: 4px solid #17a2b8; 
            margin: 20px 0; 
            border-radius: 0 4px 4px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>97盘搜</h1>
        </div>
        <div class="content">
            <h2>密码重置请求</h2>
            <p>亲爱的 <strong>{{ username }}</strong>，</p>
            <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置您的密码：</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ reset_url }}" class="button">重置密码</a>
            </div>
            
            <div class="warning">
                <p><strong>安全提醒：</strong></p>
                <ul>
                    <li>此重置链接将在 24 小时后过期</li>
                    <li>如果按钮无法点击，请复制以下链接到浏览器地址栏：</li>
                </ul>
                <p style="word-break: break-all; color: #dc3545; font-family: monospace; font-size: 14px;">
                    {{ reset_url }}
                </p>
            </div>
            
            <div class="security-tips">
                <p><strong>安全建议：</strong></p>
                <ul>
                    <li>请设置一个强密码，包含大小写字母、数字和特殊字符</li>
                    <li>不要在多个网站使用相同的密码</li>
                    <li>定期更换密码以保护账户安全</li>
                </ul>
            </div>
            
            <div class="divider"></div>
            
            <p><strong>如果您没有请求重置密码，请立即联系我们的客服团队。</strong></p>
            <p>为了您的账户安全，请不要将此邮件转发给他人。</p>
        </div>
        <div class="footer">
            <p>此邮件由 97盘搜 系统自动发送，请勿直接回复。</p>
            <p>&copy; 2025 97盘搜. 保留所有权利。</p>
        </div>
    </div>
</body>
</html>
