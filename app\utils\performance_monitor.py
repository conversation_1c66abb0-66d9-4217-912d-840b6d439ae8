#!/usr/bin/env python3
"""
性能监控脚本，用于监控 meilisearch_sync_listener 的运行状态
"""

import asyncio
import aiohttp
import psutil
import time
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, process_name: str = "python", script_name: str = "meilisearch_sync_listener.py"):
        self.process_name = process_name
        self.script_name = script_name
        self.target_process: Optional[psutil.Process] = None
        self.monitoring = False
    
    def find_target_process(self) -> Optional[psutil.Process]:
        """查找目标进程"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if (proc.info['name'] == self.process_name and 
                    any(self.script_name in arg for arg in proc.info['cmdline'])):
                    return proc
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def get_process_metrics(self) -> Optional[Dict[str, Any]]:
        """获取进程指标"""
        if not self.target_process:
            self.target_process = self.find_target_process()
            if not self.target_process:
                return None
        
        try:
            # 检查进程是否还在运行
            if not self.target_process.is_running():
                self.target_process = self.find_target_process()
                if not self.target_process:
                    return None
            
            # 获取进程信息
            memory_info = self.target_process.memory_info()
            cpu_percent = self.target_process.cpu_percent()
            
            # 获取连接信息
            try:
                connections = len(self.target_process.connections())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = 0
            
            # 获取文件描述符数量
            try:
                if hasattr(self.target_process, 'num_fds'):
                    open_files = self.target_process.num_fds()
                else:
                    open_files = len(self.target_process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            return {
                'timestamp': datetime.now().isoformat(),
                'pid': self.target_process.pid,
                'memory_rss_mb': memory_info.rss / 1024 / 1024,
                'memory_vms_mb': memory_info.vms / 1024 / 1024,
                'memory_percent': self.target_process.memory_percent(),
                'cpu_percent': cpu_percent,
                'connections': connections,
                'open_files': open_files,
                'status': self.target_process.status()
            }
            
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.warning(f"无法获取进程信息: {e}")
            self.target_process = None
            return None
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage_percent': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            'boot_time': psutil.boot_time()
        }
    
    async def check_meilisearch_health(self) -> Dict[str, Any]:
        """检查 Meilisearch 健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                # 检查 Meilisearch 健康状态
                async with session.get('http://127.0.0.1:7700/health', timeout=5) as response:
                    if response.status == 200:
                        health_data = await response.json()
                        return {
                            'status': 'healthy',
                            'response_time_ms': response.headers.get('X-Response-Time', 'unknown'),
                            'data': health_data
                        }
                    else:
                        return {
                            'status': 'unhealthy',
                            'http_status': response.status,
                            'error': await response.text()
                        }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def check_database_connections(self) -> Dict[str, Any]:
        """检查数据库连接数"""
        try:
            import asyncpg
            from app.utils.config import settings
            
            db_creds = (
                settings.get("database", {})
                .get("connections", {})
                .get("default", {})
                .get("credentials", {})
            )
            
            conn = await asyncpg.connect(
                user=db_creds.get("user"),
                password=db_creds.get("password"),
                database=db_creds.get("database"),
                host=db_creds.get("host"),
                port=db_creds.get("port"),
                timeout=5
            )
            
            # 查询活跃连接数
            result = await conn.fetchrow("""
                SELECT count(*) as active_connections
                FROM pg_stat_activity 
                WHERE state = 'active'
            """)
            
            await conn.close()
            
            return {
                'status': 'connected',
                'active_connections': result['active_connections']
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def monitor_loop(self, interval: int = 30):
        """监控循环"""
        self.monitoring = True
        logger.info(f"开始监控，间隔: {interval}秒")
        
        while self.monitoring:
            try:
                # 获取各种指标
                process_metrics = self.get_process_metrics()
                system_metrics = self.get_system_metrics()
                meilisearch_health = await self.check_meilisearch_health()
                db_health = await self.check_database_connections()
                
                # 组合报告
                report = {
                    'timestamp': datetime.now().isoformat(),
                    'process': process_metrics,
                    'system': system_metrics,
                    'meilisearch': meilisearch_health,
                    'database': db_health
                }
                
                # 输出报告
                self.print_report(report)
                
                # 检查告警条件
                self.check_alerts(report)
                
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                logger.info("监控被用户中断")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}", exc_info=True)
                await asyncio.sleep(interval)
        
        self.monitoring = False
    
    def print_report(self, report: Dict[str, Any]):
        """打印监控报告"""
        timestamp = report['timestamp']
        
        if report['process']:
            proc = report['process']
            logger.info(
                f"[{timestamp}] 进程监控 - "
                f"PID: {proc['pid']}, "
                f"内存: {proc['memory_rss_mb']:.1f}MB ({proc['memory_percent']:.1f}%), "
                f"CPU: {proc['cpu_percent']:.1f}%, "
                f"连接: {proc['connections']}, "
                f"文件: {proc['open_files']}"
            )
        else:
            logger.warning(f"[{timestamp}] 未找到目标进程: {self.script_name}")
        
        sys_metrics = report['system']
        logger.info(
            f"[{timestamp}] 系统监控 - "
            f"CPU: {sys_metrics['cpu_percent']:.1f}%, "
            f"内存: {sys_metrics['memory_percent']:.1f}%, "
            f"磁盘: {sys_metrics['disk_usage_percent']:.1f}%"
        )
        
        meili_status = report['meilisearch']['status']
        logger.info(f"[{timestamp}] Meilisearch: {meili_status}")
        
        db_status = report['database']['status']
        if db_status == 'connected':
            active_conns = report['database'].get('active_connections', 'unknown')
            logger.info(f"[{timestamp}] 数据库: {db_status}, 活跃连接: {active_conns}")
        else:
            logger.warning(f"[{timestamp}] 数据库: {db_status}")
    
    def check_alerts(self, report: Dict[str, Any]):
        """检查告警条件"""
        if report['process']:
            proc = report['process']
            
            # 内存告警
            if proc['memory_rss_mb'] > 500:
                logger.warning(f"⚠️  内存使用过高: {proc['memory_rss_mb']:.1f}MB")
            
            # CPU告警
            if proc['cpu_percent'] > 80:
                logger.warning(f"⚠️  CPU使用率过高: {proc['cpu_percent']:.1f}%")
            
            # 连接数告警
            if proc['connections'] > 50:
                logger.warning(f"⚠️  连接数过多: {proc['connections']}")
        
        # 系统告警
        sys_metrics = report['system']
        if sys_metrics['memory_percent'] > 90:
            logger.warning(f"⚠️  系统内存不足: {sys_metrics['memory_percent']:.1f}%")
        
        # Meilisearch 告警
        if report['meilisearch']['status'] != 'healthy':
            logger.warning(f"⚠️  Meilisearch 状态异常: {report['meilisearch']['status']}")
        
        # 数据库告警
        if report['database']['status'] != 'connected':
            logger.warning(f"⚠️  数据库连接异常: {report['database']['status']}")
    
    def stop(self):
        """停止监控"""
        self.monitoring = False


async def main():
    """主函数"""
    monitor = PerformanceMonitor()
    
    try:
        await monitor.monitor_loop(interval=30)  # 每30秒监控一次
    except KeyboardInterrupt:
        logger.info("监控被用户中断")
    finally:
        monitor.stop()


if __name__ == "__main__":
    asyncio.run(main())
