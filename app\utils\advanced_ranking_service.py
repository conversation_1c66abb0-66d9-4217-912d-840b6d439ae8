"""
权重排名系统核心实现 - 简化版
Advanced Ranking Service Implementation - Simplified

严格按照以下优先级规则进行排序：
1. 第一优先级：相关性等级 (1-5, 1最高)
2. 第二优先级：时间 (新时间优先)
3. 第三优先级：用户标记 (is_mine=True优先)
4. 第四优先级：清晰度 (高清晰度优先)
"""

import time
import jieba
import logging
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from enum import Enum

logger = logging.getLogger("advanced_ranking")


class RelevanceTier(Enum):
    """相关性等级枚举"""

    PERFECT_MATCH = 1  # 完全匹配
    HIGH_MATCH = 2  # 高度匹配 (包含匹配、高相似度)
    MEDIUM_MATCH = 3  # 中等匹配 (部分匹配)
    LOW_MATCH = 4  # 低度匹配 (弱相关)
    NO_MATCH = 5  # 无匹配


@dataclass
class SortingWeight:
    """简化的多级排序权重结构"""

    relevance_tier: int  # 相关性等级 (1-5, 1最高)
    update_timestamp: float  # 更新时间戳
    is_mine: bool  # 用户标记
    clarity_score: float  # 清晰度分数

    def to_sort_key(self) -> tuple:
        """
        转换为排序键 - 最终版
        优先级：1.相关性 2.时间 3.用户标记 4.清晰度

        注意：时间优先级高于用户标记，确保最新资源优先显示
        """
        return (
            self.relevance_tier,  # 第1优先级：相关性等级（升序，1最高）
            -self.update_timestamp,  # 第2优先级：时间（降序，新时间优先）
            not self.is_mine,  # 第3优先级：用户标记（False < True，所以is_mine=True排前面）
            -self.clarity_score,  # 第4优先级：清晰度（降序，高清晰度优先）
        )


class RankingCache:
    """排序结果缓存"""

    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.cache = {}
        self.max_size = max_size
        self.ttl = ttl

    def get_cache_key(self, search_term: str, filters: Dict) -> str:
        """生成缓存键"""
        return f"ranking:{hash(search_term)}:{hash(str(sorted(filters.items())))}"

    def get(self, cache_key: str) -> Optional[List[Dict]]:
        """获取缓存结果"""
        if cache_key in self.cache:
            result, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.ttl:
                return result
            else:
                del self.cache[cache_key]
        return None

    def set(self, cache_key: str, results: List[Dict]):
        """设置缓存结果"""
        if len(self.cache) >= self.max_size:
            # 删除最旧的缓存项
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]

        self.cache[cache_key] = (results, time.time())


def calculate_relevance_tier(title: str, search_term: str) -> int:
    """
    计算相关性等级

    Args:
        title: 资源标题
        search_term: 搜索关键词

    Returns:
        相关性等级 (1-5)
    """
    if not title or not search_term:
        return RelevanceTier.NO_MATCH.value

    title_lower = title.lower().strip()
    search_lower = search_term.lower().strip()

    # 1. 包含匹配
    if search_lower in title_lower:
        return RelevanceTier.HIGH_MATCH.value

    # 3. 分词高度匹配
    similarity = calculate_word_overlap_ratio(title_lower, search_lower)
    if similarity >= 0.8:
        return RelevanceTier.HIGH_MATCH.value
    elif similarity >= 0.5:
        return RelevanceTier.MEDIUM_MATCH.value
    elif similarity >= 0.3:
        return RelevanceTier.LOW_MATCH.value
    else:
        return RelevanceTier.NO_MATCH.value


def calculate_word_overlap_ratio(title: str, search_term: str) -> float:
    """计算分词重叠比例"""
    try:
        title_words = set(jieba.lcut(title))
        search_words = set(jieba.lcut(search_term))

        # 过滤掉单字符词（除非是重要的单字）
        important_single_chars = {"我", "你", "他", "她", "它"}
        title_words = {
            w for w in title_words if len(w) >= 2 or w in important_single_chars
        }
        search_words = {
            w for w in search_words if len(w) >= 2 or w in important_single_chars
        }

        if not search_words:
            return 0.0

        # 计算词汇覆盖率
        overlap = search_words & title_words
        return len(overlap) / len(search_words)

    except Exception as e:
        logger.warning(f"分词匹配失败: {e}")
        return 0.0


# 移除季度和集数解析函数，因为新的排序算法不再使用这些信息


def calculate_clarity_score(title: str) -> float:
    """
    计算清晰度分数

    Args:
        title: 资源标题

    Returns:
        清晰度分数 (0-100)
    """
    if not title:
        return 0.0

    title_lower = title.lower()

    # 清晰度关键词权重
    clarity_weights = {
        # 超高清
        "8k": 100.0,
        "4k": 90.0,
        "uhd": 90.0,
        "2160p": 90.0,
        # 高清
        "2k": 80.0,
        "1440p": 75.0,
        "1080p": 70.0,
        # 标清
        "720p": 60.0,
        "480p": 40.0,
        "360p": 20.0,
    }

    # 特殊标记加分
    special_bonuses = {
        "remux": 15.0,
        "原盘": 15.0,
        "杜比视界": 10.0,
        "dolby vision": 10.0,
        "hdr": 8.0,
        "高码率": 5.0,
        "60fps": 5.0,
    }

    base_score = 0.0
    bonus_score = 0.0

    # 计算基础清晰度分数
    for keyword, score in clarity_weights.items():
        if keyword in title_lower:
            base_score = max(base_score, score)

    # 计算特殊标记加分
    for keyword, bonus in special_bonuses.items():
        if keyword in title_lower:
            bonus_score += bonus

    return min(100.0, base_score + bonus_score)


def parse_update_timestamp(updated_at: Any) -> float:
    """解析更新时间为时间戳"""
    if not updated_at:
        return 0.0

    try:
        if isinstance(updated_at, str):
            update_time = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
        else:
            update_time = updated_at

        return update_time.timestamp()
    except Exception as e:
        logger.warning(f"时间解析失败: {e}")
        return 0.0


def calculate_sorting_weight(result: Dict[str, Any], search_term: str) -> SortingWeight:
    """计算单个结果的排序权重 - 简化版"""
    title = result.get("title", "")
    updated_at = result.get("updated_at")
    is_mine = result.get("is_mine", False)

    # 计算各个维度
    relevance_tier = calculate_relevance_tier(title, search_term)
    clarity_score = calculate_clarity_score(title)
    update_timestamp = parse_update_timestamp(updated_at)

    return SortingWeight(
        relevance_tier=relevance_tier,
        update_timestamp=update_timestamp,
        is_mine=is_mine,
        clarity_score=clarity_score,
    )


def is_today_resource(result: Dict[str, Any]) -> bool:
    """
    判断是否为当天资源
    """
    updated_at = result.get("updated_at")
    if not updated_at:
        return False

    try:
        if isinstance(updated_at, str):
            update_time = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
        else:
            update_time = updated_at

        # 获取当前时间（UTC）
        now = datetime.now(timezone.utc)

        # 判断是否为同一天
        return update_time.date() == now.date()
    except Exception as e:
        logger.warning(f"判断当天资源失败: {e}")
        return False


def apply_mine_priority(
    results: List[Dict[str, Any]], search_term: str
) -> List[Dict[str, Any]]:
    """
    简化的用户标记优先级处理

    注意：在新的排序算法中，is_mine已经作为第三优先级集成到主排序中，
    这个函数主要用于兼容性，实际上可以直接返回结果。
    """
    # 在新的简化排序算法中，用户标记已经集成到主排序键中
    # 所以这里直接返回排序后的结果即可
    logger.debug(f"用户标记优先级已集成到主排序中，直接返回结果")
    return results


def advanced_weighted_sort(
    results: List[Dict[str, Any]], search_term: str
) -> List[Dict[str, Any]]:
    """
    简化的多级权重排序算法

    Args:
        results: 搜索结果列表
        search_term: 搜索关键词

    Returns:
        排序后的结果列表
    """
    if not results:
        return results

    # 1. 为每个结果计算排序权重
    weighted_results = []
    for result in results:
        try:
            weight = calculate_sorting_weight(result, search_term)
            weighted_results.append((result, weight))
        except Exception as e:
            logger.error(f"计算权重失败: {e}, 结果: {result}")
            # 使用默认权重
            default_weight = SortingWeight(
                relevance_tier=5,
                update_timestamp=0.0,
                is_mine=False,
                clarity_score=0.0,
            )
            weighted_results.append((result, default_weight))

    # 2. 按权重排序
    weighted_results.sort(key=lambda x: x[1].to_sort_key())

    # 3. 返回排序后的结果（用户标记已集成到主排序中）
    sorted_results = [item[0] for item in weighted_results]
    return sorted_results


class AdvancedRankingService:
    """高级排序服务"""

    def __init__(self, enable_cache: bool = True):
        self.cache = RankingCache(max_size=1000, ttl=3600) if enable_cache else None

    async def rank_search_results(
        self,
        results: List[Dict[str, Any]],
        search_term: str,
        filters: Optional[Dict] = None,
        use_cache: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        对搜索结果进行高级排序

        Args:
            results: 原始搜索结果
            search_term: 搜索关键词
            filters: 过滤条件
            use_cache: 是否使用缓存

        Returns:
            排序后的结果列表
        """
        filters = filters or {}

        # 检查缓存
        if use_cache and self.cache:
            cache_key = self.cache.get_cache_key(search_term, filters)
            cached_results = self.cache.get(cache_key)
            if cached_results:
                logger.info(f"缓存命中: {cache_key}")
                return cached_results

        # 执行排序
        start_time = time.time()
        sorted_results = advanced_weighted_sort(results, search_term)
        processing_time = time.time() - start_time

        logger.info(f"排序完成: {len(results)}条结果, 耗时: {processing_time:.4f}s")

        # 缓存结果
        if use_cache and self.cache:
            self.cache.set(cache_key, sorted_results)

        return sorted_results

    def get_ranking_explanation(
        self, result: Dict[str, Any], search_term: str
    ) -> Dict[str, Any]:
        """
        获取排序解释信息 - 简化版

        Args:
            result: 单个搜索结果
            search_term: 搜索关键词

        Returns:
            包含排序依据的详细信息
        """
        weight = calculate_sorting_weight(result, search_term)

        return {
            "title": result.get("title", ""),
            "relevance_tier": weight.relevance_tier,
            "relevance_description": RelevanceTier(weight.relevance_tier).name,
            "clarity_score": weight.clarity_score,
            "update_time": (
                datetime.fromtimestamp(weight.update_timestamp)
                if weight.update_timestamp > 0
                else None
            ),
            "is_mine": weight.is_mine,
            "sort_key": weight.to_sort_key(),
        }


def test_relevance_calculation():
    """详细测试相关性计算过程"""
    print("=== 相关性计算详细分析 ===")

    search_term = "樱桃琥珀"
    titles = [
        "樱桃琥珀.1080P更18",
        "樱桃琥珀(2025)【更17/24集】【4K.HQ.高码率】【60帧版】【赵今麦/张凌赫】",
    ]

    for i, title in enumerate(titles, 1):
        print(f"\n{i}. 分析标题: '{title}'")
        print(f"   搜索词: '{search_term}'")

        title_lower = title.lower().strip()
        search_lower = search_term.lower().strip()

        print(f"   转换后标题: '{title_lower}'")
        print(f"   转换后搜索词: '{search_lower}'")

        # 步骤1: 检查包含匹配
        if search_lower in title_lower:
            print(
                f"   ✓ 包含匹配: '{search_lower}' 在 '{title_lower}' 中 -> HIGH_MATCH (2)"
            )
            relevance = 2
        else:
            print(f"   ✗ 不包含匹配: '{search_lower}' 不在 '{title_lower}' 中")

            # 步骤3: 分词匹配
            similarity = calculate_word_overlap_ratio(title_lower, search_lower)
            print(f"   分词相似度: {similarity}")

            if similarity >= 0.8:
                relevance = 2
                print("   ✓ 高相似度 -> HIGH_MATCH (2)")
            elif similarity >= 0.5:
                relevance = 3
                print("   ✓ 中等相似度 -> MEDIUM_MATCH (3)")
            elif similarity >= 0.3:
                relevance = 4
                print("   ✓ 低相似度 -> LOW_MATCH (4)")
            else:
                relevance = 5
                print("   ✗ 无匹配 -> NO_MATCH (5)")

        # 验证实际计算结果
        actual_relevance = calculate_relevance_tier(title, search_term)
        print(f"   实际计算结果: {actual_relevance}")
        print(f"   预期结果: {relevance}")
        print(f"   结果一致: {'✓' if actual_relevance == relevance else '✗'}")


def test_specific_ranking_issue():
    """测试具体的排序问题：樱桃琥珀.1080P更18 vs 樱桃琥珀(2025)【更17/24集】- 简化版"""
    print("=== 具体排序问题分析 - 简化版 ===")

    test_results = [
        {
            "title": "樱桃琥珀.1080P更18",
            "file_type": "video",
            "updated_at": "2024-01-15T10:00:00Z",
            "is_mine": False,
        },
        {
            "title": "樱桃琥珀(2025)【更17/24集】【4K.HQ.高码率】【60帧版】【赵今麦/张凌赫】",
            "file_type": "video",
            "updated_at": "2024-01-16T10:00:00Z",
            "is_mine": True,  # 测试用户标记功能
        },
    ]

    search_term = "樱桃琥珀"
    ranking_service = AdvancedRankingService()

    # 执行排序
    import asyncio

    sorted_results = asyncio.run(
        ranking_service.rank_search_results(test_results, search_term)
    )

    # 详细分析每个结果
    print("详细排序分析:")
    for i, result in enumerate(sorted_results, 1):
        explanation = ranking_service.get_ranking_explanation(result, search_term)
        print(f"\n{i}. {result['title']}")
        print(
            f"   相关性等级: {explanation['relevance_tier']} ({explanation['relevance_description']})"
        )
        print(f"   清晰度分数: {explanation['clarity_score']}")
        print(f"   更新时间: {explanation['update_time']}")
        print(f"   用户标记: {explanation['is_mine']}")
        print(f"   排序键: {explanation['sort_key']}")

        # 分析清晰度解析
        clarity = calculate_clarity_score(result["title"])
        print(f"   清晰度解析详情: 从 '{result['title']}' 解析出清晰度={clarity}")


def test_all_relevance_levels():
    """测试所有相关性等级的示例"""
    print("=== 所有相关性等级示例 ===")

    search_term = "樱桃琥珀"
    test_cases = [
        # 等级2: 包含匹配（包括完全匹配）
        ("樱桃琥珀", "应该是 HIGH_MATCH (2) - 包含匹配"),
        # 等级2: 包含匹配
        ("樱桃琥珀.1080P更18", "应该是 HIGH_MATCH (2) - 包含匹配"),
        ("樱桃琥珀(2025)第一季", "应该是 HIGH_MATCH (2) - 包含匹配"),
        # 等级2: 高相似度分词匹配
        ("樱桃琥珀色", "应该是 HIGH_MATCH (2) - 高相似度"),
        # 等级3: 中等相似度
        ("樱桃果实", "应该是 MEDIUM_MATCH (3) - 中等相似度"),
        # 等级4: 低相似度
        ("琥珀石", "应该是 LOW_MATCH (4) - 低相似度"),
        # 等级5: 无匹配
        ("钢铁侠", "应该是 NO_MATCH (5) - 无匹配"),
    ]

    for title, expected in test_cases:
        relevance = calculate_relevance_tier(title, search_term)
        tier_name = RelevanceTier(relevance).name

        # 计算分词相似度（如果不是包含匹配的话）
        if search_term.lower() not in title.lower():
            similarity = calculate_word_overlap_ratio(
                title.lower(), search_term.lower()
            )
            print(f"标题: '{title}'")
            print(f"  相关性: {relevance} ({tier_name})")
            print(f"  分词相似度: {similarity:.2f}")
            print(f"  预期: {expected}")
        else:
            print(f"标题: '{title}'")
            print(f"  相关性: {relevance} ({tier_name})")
            print(f"  预期: {expected}")
        print()


if __name__ == "__main__":
    # 测试所有相关性等级
    test_all_relevance_levels()

    print("=" * 50 + "\n")

    # 首先分析相关性计算过程
    test_relevance_calculation()

    print("\n" + "=" * 50 + "\n")

    # 然后分析整体排序问题
    test_specific_ranking_issue()
