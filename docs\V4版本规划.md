# pan-so-backend V4版本规划：用户系统与后台管理功能

## 版本概述

V4版本将为pan-so-backend系统引入完整的用户认证、权限管理、后台管理和社区功能模块，实现从单纯的资源聚合服务向用户驱动的资源分享社区的转型升级。

## 一、技术架构设计

### 1.1 核心技术栈
- **后端框架**: FastAPI (现有)
- **数据库**: PostgreSQL + Tortoise ORM (现有)
- **任务队列**: Celery + Redis (现有)
- **认证方案**: JWT Token + Session
- **权限控制**: RBAC (基于角色的访问控制)
- **前端技术**: 建议使用 Vue 3 + Element Plus 或 React + Ant Design

### 1.2 数据库设计

#### 用户相关表结构
```sql
-- 用户表
users (
    id: SERIAL PRIMARY KEY,
    username: VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email: VARCHAR(100) UNIQUE NOT NULL,
    password_hash: VARCHAR(255) NOT NULL,
    nickname: VARCHAR(100),
    avatar: VARCHAR(512),
    phone: VARCHAR(20),
    status: VARCHAR(20) DEFAULT 'active', -- active/frozen/deleted
    role_id: INT REFERENCES roles(id),
    experience: INT DEFAULT 0,
    level: INT DEFAULT 1,
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_login_at: TIMESTAMPTZ
)

-- 角色表
roles (
    id: SERIAL PRIMARY KEY,
    name: VARCHAR(50) UNIQUE NOT NULL, -- admin/user/guest
    display_name: VARCHAR(100),
    description: TEXT,
    permissions: JSONB, -- 存储权限列表
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)

-- 求助表
help_requests (
    id: SERIAL PRIMARY KEY,
    user_id: INT REFERENCES users(id),
    title: VARCHAR(255) NOT NULL,
    content: TEXT NOT NULL,
    tags: JSONB, -- 标签数组
    status: VARCHAR(20) DEFAULT 'open', -- open/adopted/closed
    reward_exp: INT DEFAULT 0, -- 悬赏经验值
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)

-- 求助回答表
help_answers (
    id: SERIAL PRIMARY KEY,
    request_id: INT REFERENCES help_requests(id),
    user_id: INT REFERENCES users(id),
    content: TEXT NOT NULL,
    is_adopted: BOOLEAN DEFAULT FALSE,
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)

-- 经验记录表
experience_logs (
    id: SERIAL PRIMARY KEY,
    user_id: INT REFERENCES users(id),
    action_type: VARCHAR(50), -- submit_resource/answer_adopted
    exp_change: INT,
    description: VARCHAR(255),
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)

-- 用户提交记录表 (扩展现有submission相关表)
user_submissions (
    id: SERIAL PRIMARY KEY,
    user_id: INT REFERENCES users(id), -- NULL表示游客提交
    resource_id: INT REFERENCES pan_resources(id),
    submission_type: VARCHAR(20), -- manual/auto_transfer
    created_at: TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)
```

### 1.3 权限设计

#### 角色定义
```python
ROLES = {
    'admin': {
        'display_name': '管理员',
        'permissions': [
            'user.manage',           # 用户管理
            'resource.manage',       # 资源管理
            'config.manage',         # 系统配置管理
            'help.manage',           # 求助管理
            'system.stats',          # 系统统计
            'resource.submit',       # 资源提交
            'help.create',           # 发布求助
            'help.answer'            # 回答求助
        ]
    },
    'user': {
        'display_name': '注册用户',
        'permissions': [
            'resource.submit',       # 资源提交
            'help.create',           # 发布求助
            'help.answer',           # 回答求助
            'profile.manage'         # 个人资料管理
        ]
    },
    'guest': {
        'display_name': '游客',
        'permissions': [
            'resource.submit'        # 仅允许提交资源（不获得经验）
        ]
    }
}
```

#### 等级系统设计
```python
LEVEL_CONFIG = {
    1: {'exp_required': 0, 'name': '新手', 'privileges': ['基础功能']},
    2: {'exp_required': 100, 'name': '初级', 'privileges': ['基础功能', '发布求助']},
    3: {'exp_required': 300, 'name': '进阶', 'privileges': ['基础功能', '发布求助', '回答求助']},
    4: {'exp_required': 600, 'name': '熟练', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交']},
    5: {'exp_required': 1000, 'name': '专家', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交', '高级搜索']},
    6: {'exp_required': 1500, 'name': '大师', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交', '高级搜索', '资源推荐']},
    7: {'exp_required': 2500, 'name': '宗师', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交', '高级搜索', '资源推荐', '社区管理']},
    8: {'exp_required': 5000, 'name': '传说', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交', '高级搜索', '资源推荐', '社区管理', 'VIP功能']},
    9: {'exp_required': 10000, 'name': '史诗', 'privileges': ['基础功能', '发布求助', '回答求助', '批量提交', '高级搜索', '资源推荐', '社区管理', 'VIP功能', '专属客服']},
    10: {'exp_required': 20000, 'name': '神话', 'privileges': ['所有功能', '终身VIP', '专属标识']}
}

# 经验获取规则
EXP_RULES = {
    'submit_resource': 1,      # 提交有效资源
    'answer_adopted': 5,       # 回答被采纳
    'daily_login': 1,          # 每日登录（可选）
    'first_help': 2,           # 首次发布求助
    'help_resolved': 3         # 求助被解决（发布者获得）
}
```

## 二、API接口设计

### 2.1 认证相关接口
```python
# 用户认证
POST /api/auth/login          # 用户登录
POST /api/auth/logout         # 用户登出
POST /api/auth/register       # 用户注册
POST /api/auth/refresh        # 刷新Token
GET  /api/auth/profile        # 获取用户信息
PUT  /api/auth/profile        # 更新用户信息
POST /api/auth/change-password # 修改密码
```

### 2.2 用户管理接口（管理员）
```python
GET    /api/admin/users           # 用户列表
GET    /api/admin/users/{id}      # 用户详情
PUT    /api/admin/users/{id}      # 更新用户信息
DELETE /api/admin/users/{id}      # 删除用户
POST   /api/admin/users/{id}/freeze   # 冻结用户
POST   /api/admin/users/{id}/unfreeze # 解冻用户
```

### 2.3 资源管理接口（管理员）
```python
GET    /api/admin/resources       # 资源列表（支持搜索、筛选）
DELETE /api/admin/resources/{id}  # 删除单个资源
POST   /api/admin/resources/batch-delete # 批量删除资源
GET    /api/admin/submissions     # 提交记录管理
```

### 2.4 求助系统接口
```python
GET    /api/help/requests         # 求助列表
POST   /api/help/requests         # 发布求助
GET    /api/help/requests/{id}    # 求助详情
PUT    /api/help/requests/{id}    # 更新求助
DELETE /api/help/requests/{id}    # 删除求助

POST   /api/help/requests/{id}/answers    # 回答求助
PUT    /api/help/answers/{id}             # 更新回答
POST   /api/help/answers/{id}/adopt       # 采纳回答
```

### 2.5 系统配置接口（管理员）
```python
GET    /api/admin/config          # 获取配置
PUT    /api/admin/config          # 更新配置
POST   /api/admin/config/reload   # 重载配置
GET    /api/admin/config/logs     # 配置变更日志
```

## 三、分阶段实施计划

### 第一阶段：基础用户认证系统（预估：5-7天）

**目标**: 建立基础的用户认证框架
**技术要点**:
- 创建用户相关数据模型
- 实现JWT认证中间件
- 用户注册、登录、登出API
- 密码加密存储

**具体任务**:
1. 创建用户、角色数据模型
2. 实现JWT认证服务
3. 开发用户注册/登录API
4. 创建认证中间件
5. 基础权限验证装饰器

### 第二阶段：用户管理与权限系统（预估：4-6天）

**目标**: 完善RBAC权限控制体系
**技术要点**:
- 实现基于角色的权限控制
- 管理员用户管理功能
- 权限验证中间件

**具体任务**:
1. 完善角色权限模型
2. 实现权限验证装饰器
3. 开发管理员用户管理API
4. 用户状态管理（激活/冻结）

### 第三阶段：资源提交用户关联（预估：3-4天）

**目标**: 将现有资源提交与用户系统整合
**技术要点**:
- 扩展现有提交API支持用户关联
- 游客提交与用户提交区分
- 提交历史记录

**具体任务**:
1. 修改资源提交API支持用户ID
2. 创建用户提交记录表
3. 实现提交历史查询
4. 游客提交功能保持

### 第四阶段：经验值与等级系统（预估：4-5天）

**目标**: 建立用户激励体系
**技术要点**:
- 经验值计算与记录
- 等级系统实现
- 等级权益差异化

**具体任务**:
1. 创建经验记录模型
2. 实现经验值计算服务
3. 等级计算与升级逻辑
4. 等级权益验证系统

### 第五阶段：求助板块功能（预估：6-8天）

**目标**: 实现社区求助功能
**技术要点**:
- 求助发布与管理
- 回答系统
- 采纳机制
- 经验值奖励

**具体任务**:
1. 创建求助相关数据模型
2. 求助发布/编辑/删除API
3. 回答系统API
4. 采纳机制与经验奖励
5. 求助状态管理

### 第六阶段：管理员后台功能（预估：5-7天）

**目标**: 完善管理员管理功能
**技术要点**:
- 资源批量管理
- 系统配置管理
- 配置热更新
- 操作日志

**具体任务**:
1. 资源批量删除功能
2. 系统配置管理API
3. 配置热更新机制
4. 管理操作日志记录
5. 统计数据API

### 第七阶段：前端界面开发（预估：10-15天）

**目标**: 开发完整的前端界面
**技术要点**:
- 用户界面（登录、个人中心、求助）
- 管理员后台界面
- 响应式设计

**具体任务**:
1. 用户认证界面
2. 个人中心界面
3. 求助板块界面
4. 管理员后台界面
5. 移动端适配

### 第八阶段：系统集成与测试（预估：3-5天）

**目标**: 系统整合与优化
**技术要点**:
- 功能集成测试
- 性能优化
- 安全加固
- 部署准备

## 四、用户界面规划

### 4.1 管理员功能页面清单

| 页面路径 | 页面名称 | 主要功能 | 权限要求 |
|---------|---------|---------|---------|
| `/admin/dashboard` | 管理员仪表板 | 系统概览、统计数据 | admin |
| `/admin/users` | 用户管理 | 用户列表、搜索、状态管理 | admin |
| `/admin/users/{id}` | 用户详情 | 用户信息编辑、权限设置 | admin |
| `/admin/resources` | 资源管理 | 资源列表、搜索、批量删除 | admin |
| `/admin/submissions` | 提交记录 | 提交历史、状态查看 | admin |
| `/admin/help` | 求助管理 | 求助列表、状态管理 | admin |
| `/admin/config` | 系统配置 | 配置项管理、热更新 | admin |
| `/admin/logs` | 操作日志 | 管理操作记录查看 | admin |

### 4.2 普通用户功能页面清单

| 页面路径 | 页面名称 | 主要功能 | 权限要求 |
|---------|---------|---------|---------|
| `/login` | 登录页面 | 用户登录 | guest |
| `/register` | 注册页面 | 用户注册 | guest |
| `/profile` | 个人中心 | 个人信息、经验等级 | user |
| `/profile/submissions` | 我的提交 | 提交历史查看 | user |
| `/help` | 求助广场 | 求助列表、搜索 | user |
| `/help/create` | 发布求助 | 创建求助请求 | user(level≥2) |
| `/help/{id}` | 求助详情 | 求助内容、回答列表 | user |
| `/help/my` | 我的求助 | 个人求助管理 | user |

### 4.3 页面权限矩阵

| 功能模块 | 游客 | 注册用户 | 管理员 | 备注 |
|---------|------|---------|--------|------|
| 资源搜索 | ✓ | ✓ | ✓ | 基础功能 |
| 资源提交 | ✓ | ✓ | ✓ | 游客无经验奖励 |
| 用户注册 | ✓ | - | - | - |
| 用户登录 | ✓ | - | - | - |
| 个人中心 | - | ✓ | ✓ | - |
| 发布求助 | - | ✓(L≥2) | ✓ | 需要等级限制 |
| 回答求助 | - | ✓(L≥3) | ✓ | 需要等级限制 |
| 用户管理 | - | - | ✓ | 仅管理员 |
| 资源管理 | - | - | ✓ | 仅管理员 |
| 系统配置 | - | - | ✓ | 仅管理员 |

## 五、安全配置管理方案

### 5.1 可配置项分类
```yaml
# 安全配置项（管理员可修改）
admin_configurable:
  api:
    max_concurrent_requests: 10
    cors_origins: ["*"]
  security:
    blocked_keywords: []
    registration_enabled: true
    guest_submission_enabled: true
  experience:
    submit_resource_exp: 1
    answer_adopted_exp: 5
  crawler:
    auto_transfer_enabled: true
    scrape_limits: {}

# 系统核心配置（不可通过界面修改）
system_protected:
  database: {}
  redis: {}
  celery: {}
  wx_push: {}
```

### 5.2 配置热更新机制
- 使用Redis作为配置缓存
- 配置变更时广播更新事件
- 支持配置回滚功能
- 记录配置变更日志

## 六、技术实现要点

### 6.1 JWT认证方案
```python
# JWT配置
JWT_CONFIG = {
    'algorithm': 'HS256',
    'access_token_expire_minutes': 30,
    'refresh_token_expire_days': 7,
    'secret_key': 'your-secret-key'  # 从环境变量获取
}
```

### 6.2 权限验证装饰器
```python
def require_permission(permission: str):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 验证用户权限逻辑
            pass
        return wrapper
    return decorator

def require_level(min_level: int):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 验证用户等级逻辑
            pass
        return wrapper
    return decorator
```

### 6.3 经验值计算服务
```python
class ExperienceService:
    async def add_experience(self, user_id: int, action: str, amount: int = None):
        """添加经验值并检查升级"""
        pass

    async def calculate_level(self, experience: int) -> int:
        """根据经验值计算等级"""
        pass

    async def get_level_privileges(self, level: int) -> List[str]:
        """获取等级权益"""
        pass
```

## 七、部署与运维考虑

### 7.1 数据库迁移
- 使用Aerich进行数据库版本管理
- 提供数据迁移脚本
- 支持回滚机制

### 7.2 缓存策略
- 用户信息缓存（30分钟）
- 权限信息缓存（1小时）
- 配置信息缓存（实时更新）

### 7.3 监控指标
- 用户注册/登录统计
- API调用频率监控
- 系统性能指标
- 错误日志监控

## 八、核心功能需求总结

### 8.1 用户认证与权限系统
- **登录入口**: `/login` 路由，支持用户名/密码登录
- **用户角色**:
  - 普通用户（注册用户）
  - 管理员用户
  - 游客用户（未登录）
- **权限控制**: 基于角色的访问控制(RBAC)

### 8.2 管理员功能模块
- **资源提交管理**: 复用现有API接口，支持批量操作。支持开启解析和非解析功能。
- **用户管理**:
  - 用户列表查看、搜索、筛选
  - 用户状态管理（激活/冻结/删除）
  - 新用户注册审核
  - 用户信息编辑
- **系统配置管理**:
  - 针对 `app/config.yaml` 的安全配置项管理
  - 支持配置热更新（无需重启服务）
  - 配置变更日志记录
- **资源管理**:
  - 支持资源的检索和删除功能。特别是批量删除。

### 8.3 资源求助板块
- **求助发布**: 用户可发布资源求助请求
- **回答系统**: 其他用户可回答求助请求
- **采纳机制**: 求助者可采纳最佳回答，采纳后自动关闭求助
- **状态管理**: 求助状态（开放/已采纳/已关闭）

### 8.4 经验值与等级系统
- **经验获取规则**:
  - 提交有效资源: +1经验值
  - 回答被采纳: +5经验值
- **等级设计**: 10个等级，总计20万经验值上限
- **等级权益**: 不同等级用户的功能权限差异

### 8.5 资源提交增强
- **多用户支持**:
  - 已登录用户提交（获得经验值）
  - 游客用户提交（不获得经验值）
- **提交记录**: 关联用户ID，记录提交历史

## 九、项目时间线

**总预估工期**: 40-60天（约8-12周）

**关键里程碑**:
- 第1-2周: 完成基础认证和权限系统
- 第3-4周: 完成用户管理和资源关联
- 第5-6周: 完成经验系统和求助功能
- 第7-8周: 完成管理员后台功能
- 第9-11周: 完成前端界面开发
- 第12周: 系统集成测试和部署

**风险控制**:
- 每个阶段完成后进行功能验收
- 保持与现有系统的兼容性
- 分阶段部署，降低上线风险

---

*本规划文档将作为V4版本开发的指导性文件，具体实施过程中可根据实际情况进行调整优化。*
