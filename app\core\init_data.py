"""
初始化数据脚本
用于创建默认角色和管理员用户
"""

import asyncio
import logging
from tortoise import Tortoise
from app.models.user import User, Role
from app.core.permissions import DEFAULT_ROLE_PERMISSIONS
from app.core.tortoise_config import TORTOISE_ORM

logger = logging.getLogger(__name__)


async def create_default_roles():
    """创建默认角色"""
    try:
        # 检查是否已存在角色
        existing_roles = await Role.all()
        if existing_roles:
            logger.info("角色已存在，跳过创建")
            return

        # 创建默认角色
        roles_data = [
            {
                'name': 'admin',
                'display_name': '管理员',
                'description': '系统管理员，拥有所有权限',
                'permissions': DEFAULT_ROLE_PERMISSIONS['admin'],
                'is_active': True
            },
            {
                'name': 'user',
                'display_name': '注册用户',
                'description': '普通注册用户',
                'permissions': DEFAULT_ROLE_PERMISSIONS['user'],
                'is_active': True
            },
            {
                'name': 'guest',
                'display_name': '游客',
                'description': '未注册用户',
                'permissions': DEFAULT_ROLE_PERMISSIONS['guest'],
                'is_active': True
            }
        ]

        for role_data in roles_data:
            role = await Role.create(**role_data)
            logger.info(f"创建角色: {role.display_name} ({role.name})")

        logger.info("默认角色创建完成")

    except Exception as e:
        logger.error(f"创建默认角色失败: {e}")
        raise


async def create_admin_user(username: str = "admin", email: str = "<EMAIL>", password: str = "Admin123456"):
    """创建默认管理员用户"""
    try:
        # 检查是否已存在管理员用户
        existing_admin = await User.filter(username=username).first()
        if existing_admin:
            logger.info(f"管理员用户 {username} 已存在，跳过创建")
            return existing_admin

        # 获取管理员角色
        admin_role = await Role.filter(name='admin').first()
        if not admin_role:
            raise Exception("管理员角色不存在，请先创建角色")

        # 创建管理员用户
        admin_user = User(
            username=username,
            email=email,
            nickname="系统管理员",
            role=admin_role,
            status="active",
            email_verified=True
        )
        admin_user.set_password(password)
        await admin_user.save()

        logger.info(f"创建管理员用户: {username} (密码: {password})")
        logger.warning("请及时修改默认管理员密码！")
        
        return admin_user

    except Exception as e:
        logger.error(f"创建管理员用户失败: {e}")
        raise


async def init_database_data():
    """初始化数据库数据"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        logger.info("开始初始化数据库数据...")
        
        # 创建默认角色
        await create_default_roles()
        
        # 创建默认管理员用户
        await create_admin_user()
        
        logger.info("数据库数据初始化完成")
        
    except Exception as e:
        logger.error(f"数据库数据初始化失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()


async def reset_admin_password(username: str = "admin", new_password: str = "Admin123456"):
    """重置管理员密码"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 查找管理员用户
        admin_user = await User.filter(username=username).first()
        if not admin_user:
            logger.error(f"管理员用户 {username} 不存在")
            return
        
        # 重置密码
        admin_user.set_password(new_password)
        admin_user.unlock_account()  # 解锁账户
        await admin_user.save()
        
        logger.info(f"管理员 {username} 密码已重置为: {new_password}")
        
    except Exception as e:
        logger.error(f"重置管理员密码失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    import sys
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    if len(sys.argv) > 1 and sys.argv[1] == "reset-password":
        # 重置管理员密码
        username = sys.argv[2] if len(sys.argv) > 2 else "admin"
        password = sys.argv[3] if len(sys.argv) > 3 else "Wsk1998107..."
        asyncio.run(reset_admin_password(username, password))
    else:
        # 初始化数据
        asyncio.run(init_database_data())
