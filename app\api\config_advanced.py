"""
高级配置管理API路由 - 支持层级展示和精确修改
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, Any
import logging

from app.models.config_models import (
    ConfigTreeResponse,
    ConfigPathRequest,
    ConfigPathResponse,
    ConfigSchemaResponse,
    ConfigTreeNode,
    ValidationResult,
)
from app.models.user import User
from app.core.permissions import RequireSystemConfig
from app.services.config_service import config_service

logger = logging.getLogger("config-advanced-api")
router = APIRouter(tags=["管理员-配置管理"])


@router.get(
    "/config/tree",
    response_model=ConfigTreeResponse,
    summary="获取配置树形结构",
    description="获取完整的配置树形结构，支持层级展示和注释显示",
)
async def get_config_tree(
    show_sensitive: bool = Query(False, description="是否显示敏感信息"),
    current_user: User = Depends(RequireSystemConfig),
):
    """获取配置树形结构"""
    try:
        # 获取配置树
        tree_data = await config_service.get_config_tree()

        # 转换为响应模型并处理敏感信息
        tree_nodes = {}
        total_nodes = 0
        max_depth = 0

        def convert_tree_node(
            key: str, node_data: dict, depth: int = 0
        ) -> ConfigTreeNode:
            nonlocal total_nodes, max_depth
            total_nodes += 1
            max_depth = max(max_depth, depth)

            # 处理敏感信息
            value = node_data.get("value")
            is_sensitive = config_service.is_sensitive_config(node_data.get("path", ""))

            if is_sensitive and not show_sensitive:
                if isinstance(value, str):
                    value = config_service.mask_sensitive_value(
                        node_data.get("path", ""), value
                    )
                else:
                    value = "***"

            # 处理子节点
            children = None
            is_leaf = True

            if "children" in node_data and node_data["children"]:
                is_leaf = False
                children = {}
                for child_key, child_data in node_data["children"].items():
                    children[child_key] = convert_tree_node(
                        child_key, child_data, depth + 1
                    )

            return ConfigTreeNode(
                key=key,
                display_name=key.replace("_", " ").title(),
                path=node_data.get("path", ""),
                type=node_data.get("type", "unknown"),
                value=value,
                comment=node_data.get("comment"),
                children=children,
                is_leaf=is_leaf,
                sensitive=is_sensitive,
                required=False,  # TODO: 从配置模式中获取
                validation_rules={},  # TODO: 从配置模式中获取
                effect_type=config_service.get_effect_type(node_data.get("path", "")),
            )

        # 转换根级节点
        for key, node_data in tree_data.items():
            tree_nodes[key] = convert_tree_node(key, node_data)

        return ConfigTreeResponse(
            tree=tree_nodes,
            total_nodes=total_nodes,
            max_depth=max_depth,
        )

    except Exception as e:
        logger.error(f"获取配置树失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置树失败: {str(e)}")


@router.get(
    "/config/path/{path:path}",
    response_model=ConfigPathResponse,
    summary="按路径获取配置",
    description="根据配置路径获取特定配置项的详细信息",
)
async def get_config_by_path(
    path: str,
    show_sensitive: bool = Query(False, description="是否显示敏感信息"),
    current_user: User = Depends(RequireSystemConfig),
):
    """按路径获取配置"""
    try:
        # 获取配置值
        value = await config_service.get_config_by_path(path)

        # 处理敏感信息
        is_sensitive = config_service.is_sensitive_config(path)
        display_value = value

        if is_sensitive and not show_sensitive:
            display_value = config_service.mask_sensitive_value(path, value)

        # 获取父级路径
        path_parts = path.split(".")
        parent_path = ".".join(path_parts[:-1]) if len(path_parts) > 1 else None

        # TODO: 获取子级路径列表（如果是对象类型）
        children_paths = None

        # TODO: 从配置树中获取注释
        comment = None

        return ConfigPathResponse(
            path=path,
            value=display_value,
            type=type(value).__name__.lower(),
            comment=comment,
            sensitive=is_sensitive,
            parent_path=parent_path,
            children_paths=children_paths,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.put(
    "/config/path/{path:path}",
    summary="按路径修改配置",
    description="根据配置路径精确修改特定配置项的值",
)
async def update_config_by_path(
    path: str,
    request_data: ConfigPathRequest,
    current_user: User = Depends(RequireSystemConfig),
):
    """按路径修改配置"""
    try:
        # 获取旧值
        old_value = await config_service.get_config_by_path(path)

        # 设置新值
        success = await config_service.set_config_by_path(path, request_data.value)

        if success:
            logger.info(f"配置路径更新成功: {path} = {request_data.value}")

            return {
                "status": "success",
                "message": "配置更新成功",
                "data": {
                    "path": path,
                    "old_value": config_service.mask_sensitive_value(path, old_value),
                    "new_value": config_service.mask_sensitive_value(
                        path, request_data.value
                    ),
                    "effect_type": config_service.get_effect_type(path),
                    "comment": request_data.comment,
                },
            }
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post(
    "/config/validate",
    response_model=ValidationResult,
    summary="验证配置值",
    description="验证配置值是否符合要求",
)
async def validate_config_value(
    path: str = Query(..., description="配置路径"),
    value: Any = Query(..., description="配置值"),
    current_user: User = Depends(RequireSystemConfig),
):
    """验证配置值"""
    try:
        from app.services.config_service import config_validator

        result = config_validator.validate_config_value(path, value)
        return result

    except Exception as e:
        logger.error(f"验证配置失败: {e}", exc_info=True)
        return ValidationResult(
            valid=False,
            message=f"验证失败: {str(e)}",
        )


@router.get(
    "/config/schema",
    response_model=ConfigSchemaResponse,
    summary="获取配置模式",
    description="获取配置项的模式定义和验证规则",
)
async def get_config_schema(
    current_user: User = Depends(RequireSystemConfig),
):
    """获取配置模式"""
    try:
        # TODO: 实现配置模式的获取
        # 这里应该返回所有配置项的模式定义

        schemas = {}

        return ConfigSchemaResponse(
            schemas=schemas,
            total_schemas=len(schemas),
        )

    except Exception as e:
        logger.error(f"获取配置模式失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置模式失败: {str(e)}")


@router.get(
    "/config/search",
    summary="搜索配置项",
    description="根据关键词搜索配置项",
)
async def search_config(
    query: str = Query(..., description="搜索关键词"),
    show_sensitive: bool = Query(False, description="是否显示敏感信息"),
    current_user: User = Depends(RequireSystemConfig),
):
    """搜索配置项"""
    try:
        # 获取配置树
        tree_data = await config_service.get_config_tree()

        # 搜索匹配的配置项
        results = []

        def search_in_tree(node_data: dict, query_lower: str):
            path = node_data.get("path", "")
            key = path.split(".")[-1] if path else ""
            comment = node_data.get("comment", "")

            # 检查是否匹配
            if (
                query_lower in key.lower()
                or query_lower in path.lower()
                or (comment and query_lower in comment.lower())
            ):

                # 处理敏感信息
                value = node_data.get("value")
                is_sensitive = config_service.is_sensitive_config(path)

                if is_sensitive and not show_sensitive:
                    value = config_service.mask_sensitive_value(path, value)

                results.append(
                    {
                        "path": path,
                        "key": key,
                        "value": value,
                        "type": node_data.get("type", "unknown"),
                        "comment": comment,
                        "sensitive": is_sensitive,
                    }
                )

            # 递归搜索子节点
            if "children" in node_data:
                for child_data in node_data["children"].values():
                    search_in_tree(child_data, query_lower)

        query_lower = query.lower()
        for node_data in tree_data.values():
            search_in_tree(node_data, query_lower)

        return {
            "status": "success",
            "data": {
                "query": query,
                "results": results,
                "total": len(results),
            },
        }

    except Exception as e:
        logger.error(f"搜索配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"搜索配置失败: {str(e)}")
