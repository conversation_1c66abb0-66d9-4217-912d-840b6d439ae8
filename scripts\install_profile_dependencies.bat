@echo off
chcp 65001 >nul
echo 🚀 个人信息管理模块依赖安装器 (Windows)
echo ============================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

echo.
echo 🔧 开始安装个人信息管理模块依赖...
echo.

REM 运行Python安装脚本
python "%~dp0install_profile_dependencies.py"

if errorlevel 1 (
    echo.
    echo ❌ 安装过程中出现错误
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 📖 接下来请参考文档进行配置:
echo   docs/个人信息管理模块部署指南.md
echo.
pause
