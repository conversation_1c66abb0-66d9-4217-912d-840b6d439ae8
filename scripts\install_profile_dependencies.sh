#!/bin/bash

# 个人信息管理模块依赖安装器 (Linux/macOS)

set -e  # 遇到错误时退出

echo "🚀 个人信息管理模块依赖安装器 (Linux/macOS)"
echo "============================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python，请先安装Python 3.8+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python已安装"
$PYTHON_CMD --version

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ 错误: 需要Python 3.8或更高版本，当前版本: $PYTHON_VERSION"
    exit 1
fi

echo
echo "🔧 开始安装个人信息管理模块依赖..."
echo

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 运行Python安装脚本
$PYTHON_CMD "$SCRIPT_DIR/install_profile_dependencies.py"

if [ $? -eq 0 ]; then
    echo
    echo "🎉 安装完成！"
    echo
    echo "📖 接下来请参考文档进行配置:"
    echo "  docs/个人信息管理模块部署指南.md"
    echo
else
    echo
    echo "❌ 安装过程中出现错误"
    exit 1
fi
