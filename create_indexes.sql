-- 为pan_resources表创建关键索引以解决高IO等待问题
-- 使用CONCURRENTLY避免锁表，可以在生产环境安全执行

-- 1. 为频繁查询的单个字段创建索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_pan_type 
ON pan_resources(pan_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_file_type 
ON pan_resources(file_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_verified_status 
ON pan_resources(verified_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_author 
ON pan_resources(author);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_created_at 
ON pan_resources(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_updated_at 
ON pan_resources(updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_is_parsed 
ON pan_resources(is_parsed);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_is_mine 
ON pan_resources(is_mine);

-- 2. 为常见查询组合创建复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_type_status 
ON pan_resources(pan_type, verified_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_type_file 
ON pan_resources(pan_type, file_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_status_updated 
ON pan_resources(verified_status, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_author_type 
ON pan_resources(author, pan_type);

-- 3. 为时间范围查询优化的索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pan_resources_created_updated 
ON pan_resources(created_at DESC, updated_at DESC);

-- 4. 为submission_tasks表创建关键索引（这个表的全表扫描更严重）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submission_tasks_status 
ON submission_tasks(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submission_tasks_batch_id 
ON submission_tasks(batch_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submission_tasks_resource_id 
ON submission_tasks(resource_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submission_tasks_created_at 
ON submission_tasks(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_submission_tasks_parsed_key 
ON submission_tasks(parsed_resource_key);

-- 5. 为其他高频查询表创建索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resource_feedbacks_resource_id 
ON resource_invalid_feedbacks(resource_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resource_feedbacks_created_at 
ON resource_invalid_feedbacks(created_at DESC);

-- 添加注释说明
COMMENT ON INDEX idx_pan_resources_pan_type IS '网盘类型查询索引';
COMMENT ON INDEX idx_pan_resources_file_type IS '文件类型查询索引';
COMMENT ON INDEX idx_pan_resources_verified_status IS '验证状态查询索引';
COMMENT ON INDEX idx_pan_resources_author IS '作者查询索引';
COMMENT ON INDEX idx_pan_resources_created_at IS '创建时间排序索引';
COMMENT ON INDEX idx_pan_resources_updated_at IS '更新时间排序索引';
