"""
资源求助系统API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi import status as http_status
from typing import Optional
import logging

from app.models.user import User
from app.models.help_request import (
    HelpRequest,
    HelpAnswer,
    ResourceType,
    HelpRequestStatus,
)
from app.models.help_request_models import (
    HelpRequestCreateRequest,
    HelpAnswerCreateRequest,
    UserBasicInfo,
)
from app.services.help_request_service import HelpRequestService
from app.services.points_service import PointsService
from app.core.permissions import (
    get_current_user,
    get_current_admin_user,
    get_current_user_optional,
)
from app.models.auth_models import ApiResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/help", tags=["资源求助"])


@router.post(
    "/requests",
    response_model=ApiResponse,
    summary="创建资源求助",
    description="创建资源求助请求",
)
async def create_help_request(
    request: HelpRequestCreateRequest, current_user: User = Depends(get_current_user)
):
    """创建求助请求"""
    try:
        help_request = await HelpRequestService.create_help_request(
            user=current_user,
            title=request.title,
            description=request.description,
            cloud_disk_types=[
                disk_type.value for disk_type in request.cloud_disk_types
            ],
            resource_type=request.resource_type.value,
        )

        return ApiResponse(
            status="success",
            message="求助发布成功",
            data={
                "request_id": help_request.id,
                "title": help_request.title,
                "points_deducted": 2,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建求助失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建求助失败",
        )


@router.get(
    "/requests",
    response_model=ApiResponse,
    summary="获取资源求助列表",
    description="获取资源求助列表，支持按状态、资源类型筛选和关键词搜索",
)
async def get_help_requests(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[HelpRequestStatus] = Query(
        None, description="状态筛选：open(开放中)、resolved(已解决)、closed(已关闭)"
    ),
    resource_type: Optional[ResourceType] = Query(
        None,
        description="资源类型筛选：movie(电影)、tv(电视剧)、music(音乐)、software(软件)、game(游戏)、book(书籍)、document(文档)、other(其他)",
    ),
    search: Optional[str] = Query(None, description="搜索关键词（标题和描述）"),
    current_user: Optional[User] = Depends(get_current_user_optional),
):
    """获取求助列表"""
    try:
        result = await HelpRequestService.get_help_requests(
            page=page,
            size=size,
            status=status.value if status else None,
            resource_type=resource_type.value if resource_type else None,
            search=search,
        )

        # 转换为响应格式
        requests_data = []
        for req in result["requests"]:
            user_info = UserBasicInfo(
                id=req.requester.id,
                username=req.requester.username,
                nickname=req.requester.nickname,
                points=req.requester.points,
                title=PointsService.get_title_by_points(req.requester.points),
            )

            requests_data.append(
                {
                    "id": req.id,
                    "title": req.title,
                    "description": req.description,
                    "cloud_disk_types": req.cloud_disk_types,
                    "resource_type": req.resource_type,
                    "status": req.status,
                    "requester": user_info.model_dump(),
                    "answer_count": req.answer_count,
                    "view_count": req.view_count,
                    "created_at": req.created_at.isoformat(),
                    "updated_at": req.updated_at.isoformat(),
                    "resolved_at": (
                        req.resolved_at.isoformat() if req.resolved_at else None
                    ),
                }
            )

        response_data = {
            "total": result["total"],
            "page": result["page"],
            "size": result["size"],
            "pages": result["pages"],
            "requests": requests_data,
        }

        return ApiResponse(
            status="success", message="获取求助列表成功", data=response_data
        )
    except Exception as e:
        logger.error(f"获取求助列表失败: {e}")
        import traceback

        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取求助列表失败",
        )


@router.get(
    "/requests/{request_id}",
    response_model=ApiResponse,
    summary="获取资源求助详情",
    description="获取资源求助详情",
)
async def get_help_request_detail(
    request_id: int, current_user: Optional[User] = Depends(get_current_user_optional)
):
    """获取求助详情"""
    try:
        help_request = await HelpRequestService.get_help_request_detail(request_id)

        if not help_request:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND, detail="求助不存在"
            )

        # 构建响应数据
        requester_info = UserBasicInfo(
            id=help_request.requester.id,
            username=help_request.requester.username,
            nickname=help_request.requester.nickname,
            points=help_request.requester.points,
            title=PointsService.get_title_by_points(help_request.requester.points),
        )

        answers_data = []
        for answer in help_request.answers:
            answerer_info = UserBasicInfo(
                id=answer.answerer.id,
                username=answer.answerer.username,
                nickname=answer.answerer.nickname,
                points=answer.answerer.points,
                title=PointsService.get_title_by_points(answer.answerer.points),
            )

            answers_data.append(
                {
                    "id": answer.id,
                    "resource_title": answer.resource_title,
                    "resource_link": answer.resource_link,
                    "cloud_disk_type": answer.cloud_disk_type,
                    "additional_info": answer.additional_info,
                    "should_archive": answer.should_archive,
                    "answerer": answerer_info.model_dump(),
                    "is_accepted": answer.is_accepted,
                    "accepted_at": (
                        answer.accepted_at.isoformat() if answer.accepted_at else None
                    ),
                    "created_at": answer.created_at.isoformat(),
                    "updated_at": answer.updated_at.isoformat(),
                }
            )

        detail_data = {
            "id": help_request.id,
            "title": help_request.title,
            "description": help_request.description,
            "cloud_disk_types": help_request.cloud_disk_types,
            "resource_type": help_request.resource_type,
            "status": help_request.status,
            "requester": requester_info.model_dump(),
            "answer_count": help_request.answer_count,
            "view_count": help_request.view_count,
            "created_at": help_request.created_at.isoformat(),
            "updated_at": help_request.updated_at.isoformat(),
            "resolved_at": (
                help_request.resolved_at.isoformat()
                if help_request.resolved_at
                else None
            ),
            "answers": answers_data,
            "can_answer": current_user
            and current_user.id != help_request.requester.id
            and help_request.status == HelpRequestStatus.OPEN,
            "can_accept": current_user
            and current_user.id == help_request.requester.id
            and help_request.status == HelpRequestStatus.OPEN,
        }

        return ApiResponse(
            status="success", message="获取求助详情成功", data=detail_data
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取求助详情失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取求助详情失败",
        )


@router.post(
    "/requests/{request_id}/answers",
    response_model=ApiResponse,
    summary="回答资源求助",
    description="回答资源求助请求",
)
async def create_help_answer(
    request_id: int,
    answer_request: HelpAnswerCreateRequest,
    current_user: User = Depends(get_current_user),
):
    """创建求助回答"""
    try:
        answer = await HelpRequestService.create_help_answer(
            user=current_user,
            help_request_id=request_id,
            resource_title=answer_request.resource_title,
            resource_link=answer_request.resource_link,
            cloud_disk_type=answer_request.cloud_disk_type.value,
            additional_info=answer_request.additional_info,
            should_archive=answer_request.should_archive,
        )

        return ApiResponse(
            status="success",
            message="回答提交成功",
            data={
                "answer_id": answer.id,
                "resource_title": answer.resource_title,
                "points_earned": 1,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建回答失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建回答失败",
        )


@router.post(
    "/answers/{answer_id}/accept",
    response_model=ApiResponse,
    summary="采纳资源求助回答",
    description="采纳资源求助回答",
)
async def accept_answer(answer_id: int, current_user: User = Depends(get_current_user)):
    """采纳回答"""
    try:
        answer = await HelpRequestService.accept_answer(
            user=current_user, answer_id=answer_id
        )

        return ApiResponse(
            status="success",
            message="回答采纳成功",
            data={
                "answer_id": answer.id,
                "resource_title": answer.resource_title,
                "points_earned": 5,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"采纳回答失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="采纳回答失败",
        )


@router.delete(
    "/answers/{answer_id}",
    response_model=ApiResponse,
    summary="删除资源求助回答",
    description="删除资源求助回答",
)
async def delete_answer(
    answer_id: int, current_user: User = Depends(get_current_admin_user)
):
    """删除回答（管理员专用）"""
    try:
        answer = await HelpAnswer.get_or_none(id=answer_id)
        if not answer:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND, detail="回答不存在"
            )

        # 如果是被采纳的回答，需要重置求助状态
        if answer.is_accepted:
            help_request = await HelpRequest.get(id=answer.help_request_id)
            help_request.status = HelpRequestStatus.OPEN
            help_request.resolved_at = None
            await help_request.save()

        # 更新回答数量
        help_request = await HelpRequest.get(id=answer.help_request_id)
        help_request.answer_count = max(0, help_request.answer_count - 1)
        await help_request.save()

        await answer.delete()

        logger.info(f"管理员 {current_user.username} 删除了回答 {answer_id}")

        return ApiResponse(
            status="success", message="回答删除成功", data={"answer_id": answer_id}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除回答失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除回答失败",
        )


@router.get(
    "/statistics",
    response_model=ApiResponse,
    summary="获取资源求助统计信息",
    description="获取资源求助统计信息",
)
async def get_help_statistics():
    """获取求助系统统计信息"""
    try:
        stats = await HelpRequestService.get_help_request_statistics()

        return ApiResponse(status="success", message="获取统计信息成功", data=stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败",
        )
