import json
import logging
from typing import Dict, Optional, Any
import httpx
import urllib.request
import urllib.parse
from app.utils.config import settings

logger = logging.getLogger("proxy-service")


class ProxyService:
    """
    代理服务类

    提供代理获取和使用功能，包括：
    - 从代理API获取可用代理
    - 代理请求封装
    """

    def __init__(self):
        """初始化代理服务"""
        self.config = settings.get("proxy_service", {})
        self.enabled = self.config.get("enabled", False)

        # 代理API配置
        self.api_server_address = self.config.get("api_server_address", "")
        self.token_id = self.config.get("token_id", "")
        self.schemes = self.config.get("schemes", "socks5")
        self.support_https = self.config.get("support_https", "true")
        self.restime_within_ms = self.config.get("restime_within_ms", 2000)
        self.format = self.config.get("format", "json")
        self.retry_count = self.config.get("retry_count", 2)
        self.timeout = self.config.get("timeout", 10)

        # 当前代理和使用统计
        self.current_proxy: Optional[Dict[str, Any]] = None
        self.proxy_failure_count = 0  # 当前代理失败次数
        self.total_proxy_attempts = 0  # 总代理尝试次数
        self.max_proxy_attempts = 30  # 最大代理尝试次数
        self.is_fallback_mode = False  # 是否已降级到直连模式

        logger.info(f"代理服务初始化完成，启用状态: {self.enabled}")

    async def get_expiretime(self) -> Optional[str]:
        """
        获取代理服务过期时间

        返回:
            Optional[str]: 过期时间信息，获取失败返回None
        """
        if not self.enabled or not self.api_server_address or not self.token_id:
            return None

        try:
            query = {"id": self.token_id}
            url = (
                self.api_server_address
                + "get_expiretime?"
                + urllib.parse.urlencode(query)
            )

            # 使用同步请求，因为这是简单的API调用
            res_data = urllib.request.urlopen(url, timeout=self.timeout)
            result = res_data.read().decode("utf-8")
            logger.info(f"代理服务过期时间: {result}")
            return result
        except Exception as e:
            logger.error(f"获取代理服务过期时间失败: {str(e)}")
            return None

    async def get_proxy_from_api(self) -> Optional[Dict[str, Any]]:
        """
        从代理API获取单个代理

        返回:
            Optional[Dict[str, Any]]: 代理信息，获取失败返回None
        """
        if not self.enabled or not self.api_server_address or not self.token_id:
            return None

        try:
            query = {
                "id": self.token_id,
                "size": 1,  # 只获取一个代理
                "schemes": self.schemes,
                "support_https": self.support_https,
                "restime_within_ms": self.restime_within_ms,
                "format": self.format,
            }
            url = (
                self.api_server_address + "get_proxies?" + urllib.parse.urlencode(query)
            )

            # 使用同步请求获取代理
            res_data = urllib.request.urlopen(url, timeout=self.timeout)
            result = res_data.read().decode("utf-8")

            # 解析JSON响应
            response_data = json.loads(result)

            # 检查响应格式和成功状态
            if not isinstance(response_data, dict):
                logger.error("代理API响应格式错误：不是字典类型")
                return None

            if not response_data.get("success", False):
                logger.error(f"代理API返回失败: {response_data}")
                return None

            # 获取代理列表
            proxies = response_data.get("proxies", [])
            if not proxies or len(proxies) == 0:
                logger.warning("代理API返回空的代理列表")
                return None

            # 取第一个代理
            proxy = proxies[0]
            if not isinstance(proxy, dict) or "ip" not in proxy or "port" not in proxy:
                logger.error(f"代理数据格式错误: {proxy}")
                return None

            # 构建代理信息
            proxy_info = {
                "ip": proxy["ip"],
                "port": proxy["port"],
                "scheme": proxy.get("scheme", self.schemes),
                "support_https": proxy.get("support_https", True),
                "url": f"{proxy.get('scheme', self.schemes)}://{proxy['ip']}:{proxy['port']}",
            }

            logger.info(f"成功获取代理: {proxy_info['url']}")
            return proxy_info

        except json.JSONDecodeError as e:
            logger.error(f"解析代理API响应JSON失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"从API获取代理失败: {str(e)}")
            return None

    def mark_proxy_failure(self):
        """
        标记当前代理失败
        """
        self.proxy_failure_count += 1
        logger.warning(f"代理失败次数: {self.proxy_failure_count}/{self.retry_count}")

        # 如果超过最大重试次数，更换代理
        if self.proxy_failure_count >= self.retry_count:
            logger.info(f"代理失败次数达到上限({self.retry_count})，准备更换代理")
            self.current_proxy = None
            self.proxy_failure_count = 0

    def mark_proxy_success(self):
        """
        标记当前代理成功，重置失败计数
        """
        if self.proxy_failure_count > 0:
            logger.info("代理连接成功，重置失败计数")
            self.proxy_failure_count = 0

    async def get_current_proxy(self) -> Optional[Dict[str, Any]]:
        """
        获取当前可用代理

        返回:
            Optional[Dict[str, Any]]: 代理信息，无可用代理返回None
        """
        if not self.enabled or self.is_fallback_mode:
            return None

        # 如果没有当前代理，尝试获取一个
        if not self.current_proxy:
            # 检查是否已达到最大尝试次数
            if self.total_proxy_attempts >= self.max_proxy_attempts:
                logger.error(
                    f"已尝试{self.max_proxy_attempts}个代理均失败，降级到直连模式"
                )
                self.is_fallback_mode = True
                return None

            # 尝试获取新代理
            self.total_proxy_attempts += 1
            logger.info(f"尝试获取第{self.total_proxy_attempts}个代理")
            self.current_proxy = await self.get_proxy_from_api()

            if not self.current_proxy:
                logger.warning(f"第{self.total_proxy_attempts}个代理获取失败")

        return self.current_proxy

    def reset_proxy_state(self):
        """
        重置代理状态，用于重新开始代理尝试
        """
        logger.info("重置代理状态")
        self.current_proxy = None
        self.proxy_failure_count = 0
        self.total_proxy_attempts = 0
        self.is_fallback_mode = False

    async def create_proxy_client(
        self, base_timeout: float = 30.0, fallback_to_direct: bool = True
    ) -> httpx.AsyncClient:
        """
        创建带代理的HTTP客户端，支持智能代理管理

        参数:
            base_timeout: 基础超时时间
            fallback_to_direct: 代理失败时是否降级到直连

        返回:
            httpx.AsyncClient: 配置了代理的HTTP客户端
        """
        # 尝试获取可用代理
        proxy = await self.get_current_proxy()

        if proxy:
            logger.info(
                f"使用代理: {proxy['url']} (第{self.total_proxy_attempts}个代理)"
            )
            try:
                client = httpx.AsyncClient(
                    proxies=proxy["url"],
                    timeout=base_timeout,
                    follow_redirects=True,
                    http2=True,
                    limits=httpx.Limits(
                        max_connections=100,
                        max_keepalive_connections=50,
                    ),
                    verify=False,
                    transport=httpx.AsyncHTTPTransport(retries=2, verify=False),
                )

                # 标记代理创建成功
                self.mark_proxy_success()
                return client

            except Exception as e:
                logger.error(f"创建代理客户端失败: {str(e)}")
                # 标记代理失败
                self.mark_proxy_failure()

                # 如果不允许降级，抛出异常
                if not fallback_to_direct:
                    raise

                # 尝试获取新代理（如果当前代理已被标记为失败）
                if not self.current_proxy and not self.is_fallback_mode:
                    return await self.create_proxy_client(
                        base_timeout, fallback_to_direct
                    )

        # 无可用代理或已降级，使用直连
        if self.is_fallback_mode:
            logger.info("使用直连模式（已达到最大代理尝试次数）")
        else:
            logger.info("使用直连模式（无可用代理）")

        return httpx.AsyncClient(
            timeout=base_timeout,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=2, verify=False),
        )

    async def make_request_with_proxy_management(
        self, client: httpx.AsyncClient, method: str, url: str, **kwargs
    ) -> httpx.Response:
        """
        使用代理客户端发送请求，并管理代理失败

        参数:
            client: HTTP客户端
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数

        返回:
            httpx.Response: HTTP响应

        异常:
            如果请求失败，会标记代理失败并抛出异常
        """
        try:
            if method.upper() == "GET":
                response = await client.get(url, **kwargs)
            elif method.upper() == "POST":
                response = await client.post(url, **kwargs)
            elif method.upper() == "PUT":
                response = await client.put(url, **kwargs)
            elif method.upper() == "DELETE":
                response = await client.delete(url, **kwargs)
            else:
                response = await client.request(method, url, **kwargs)

            # 请求成功，标记代理成功
            if self.current_proxy:
                self.mark_proxy_success()

            return response

        except Exception as e:
            # 请求失败，标记代理失败
            if self.current_proxy:
                logger.warning(f"代理请求失败: {str(e)}")
                self.mark_proxy_failure()
            raise

    def is_enabled(self) -> bool:
        """
        检查代理服务是否启用

        返回:
            bool: 代理服务是否启用
        """
        return self.enabled

    def get_proxy_stats(self) -> Dict[str, Any]:
        """
        获取代理使用统计信息

        返回:
            Dict[str, Any]: 代理统计信息
        """
        return {
            "enabled": self.enabled,
            "current_proxy": self.current_proxy["url"] if self.current_proxy else None,
            "failure_count": self.proxy_failure_count,
            "max_retries": self.retry_count,
            "total_attempts": self.total_proxy_attempts,
            "max_attempts": self.max_proxy_attempts,
            "is_fallback_mode": self.is_fallback_mode,
        }


# 创建全局代理服务实例
proxy_service = ProxyService()
