from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import Optional
from tortoise.queryset import Q
import logging
from datetime import datetime, timedelta

from app.models.resource import PanResource
from app.models.feedback import ResourceInvalidFeedback
from app.models.user import User
from app.core.permissions import PermissionChecker, Permissions

logger = logging.getLogger("admin-feedback-api")
router = APIRouter(tags=["管理员-资源反馈管理"])

# 权限检查器
RequireFeedbackManage = PermissionChecker(Permissions.FEEDBACK_MANAGE)


def get_pan_type_name(pan_type: int) -> str:
    """获取网盘类型名称"""
    mapping = {1: "百度网盘", 2: "夸克网盘", 3: "阿里云盘", 4: "迅雷网盘"}
    return mapping.get(pan_type, "未知网盘")


def get_invalid_type_name(invalid_type: int) -> str:
    """获取失效类型名称"""
    mapping = {1: "链接错误", 2: "资源失效", 3: "文件不存在"}
    return mapping.get(invalid_type, "其他问题")


@router.get(
    "/feedback/stats", summary="管理员反馈统计", description="获取反馈的统计信息"
)
async def get_admin_feedback_stats(current_user: User = Depends(RequireFeedbackManage)):
    """
    管理员反馈统计
    """
    try:
        # 总数统计
        total_count = await ResourceInvalidFeedback.all().count()

        # 按失效类型统计
        by_invalid_type = {}
        for invalid_type in [1, 2, 3]:
            count = await ResourceInvalidFeedback.filter(
                invalid_type=invalid_type
            ).count()
            by_invalid_type[get_invalid_type_name(invalid_type)] = count

        # 按网盘类型统计
        by_pan_type = {}
        for pan_type in [1, 2, 3, 4]:
            count = await ResourceInvalidFeedback.filter(pan_type=pan_type).count()
            by_pan_type[get_pan_type_name(pan_type)] = count

        # 按验证状态统计
        by_verification = {
            "verified": await ResourceInvalidFeedback.filter(is_verified=True).count(),
            "unverified": await ResourceInvalidFeedback.filter(
                is_verified=False
            ).count(),
        }

        # 时间趋势统计
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        recent_stats = {
            "today": await ResourceInvalidFeedback.filter(
                created_at__gte=today
            ).count(),
            "week": await ResourceInvalidFeedback.filter(
                created_at__gte=today - timedelta(days=7)
            ).count(),
            "month": await ResourceInvalidFeedback.filter(
                created_at__gte=today - timedelta(days=30)
            ).count(),
        }

        return {
            "status": "success",
            "data": {
                "total_count": total_count,
                "by_invalid_type": by_invalid_type,
                "by_pan_type": by_pan_type,
                "by_verification": by_verification,
                "recent_stats": recent_stats,
            },
        }

    except Exception as e:
        logger.error(f"获取管理员反馈统计失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈统计失败: {str(e)}",
        )


@router.get(
    "/feedback",
    summary="管理员反馈列表",
    description="获取反馈列表，支持高级筛选和搜索",
)
async def get_admin_feedback(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    invalid_type: Optional[int] = Query(
        None, description="失效类型筛选: 1=链接错误, 2=资源失效, 3=文件不存在"
    ),
    pan_type: Optional[int] = Query(
        None, description="网盘类型筛选: 1=百度网盘, 2=夸克网盘, 3=阿里云盘, 4=迅雷网盘"
    ),
    is_verified: Optional[bool] = Query(None, description="是否已验证筛选"),
    keyword: Optional[str] = Query(None, description="搜索资源ID或标题"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: User = Depends(RequireFeedbackManage),
):
    """
    管理员反馈列表查询
    """
    try:
        # 构建查询条件
        query = ResourceInvalidFeedback.all()

        # 应用筛选条件
        if invalid_type is not None:
            query = query.filter(invalid_type=invalid_type)
        if pan_type is not None:
            query = query.filter(pan_type=pan_type)
        if is_verified is not None:
            query = query.filter(is_verified=is_verified)
        if keyword:
            # 搜索资源ID或通过关联资源表搜索资源标题
            # 先查找标题匹配的资源，获取其resource_key列表
            matching_resources = await PanResource.filter(
                title__icontains=keyword
            ).values_list("resource_key", flat=True)
            resource_ids_by_title = list(matching_resources)

            # 构建查询条件：资源ID匹配 或 资源标题匹配
            title_condition = (
                Q(resource_id__in=resource_ids_by_title)
                if resource_ids_by_title
                else Q(pk__in=[])
            )
            query = query.filter(Q(resource_id__icontains=keyword) | title_condition)
        if start_date:
            query = query.filter(created_at__gte=start_date)
        if end_date:
            query = query.filter(created_at__lte=end_date)

        # 分页查询
        total = await query.count()
        offset = (page - 1) * size
        feedbacks = await query.offset(offset).limit(size).order_by("-created_at")

        # 扩展反馈信息（关联资源信息）
        feedback_list = []
        for feedback in feedbacks:
            # 获取关联的资源信息
            resource = await PanResource.filter(
                resource_key=feedback.resource_id
            ).first()

            feedback_data = {
                "id": feedback.id,
                "resource_id": feedback.resource_id,
                "resource_title": resource.title if resource else "资源已删除",
                "resource_exists": resource is not None,
                "pan_type": feedback.pan_type,
                "pan_type_name": get_pan_type_name(feedback.pan_type),
                "invalid_type": feedback.invalid_type,
                "invalid_type_name": get_invalid_type_name(feedback.invalid_type),
                "description": feedback.description,
                "contact_info": feedback.contact_info,
                "is_verified": feedback.is_verified,
                "verification_result": feedback.verification_result,
                "created_at": feedback.created_at,
                "updated_at": feedback.updated_at,
            }
            feedback_list.append(feedback_data)

        return {
            "status": "success",
            "data": {
                "feedbacks": feedback_list,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size,
                },
            },
        }

    except Exception as e:
        logger.error(f"获取管理员反馈列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈列表失败: {str(e)}",
        )


@router.get(
    "/feedback/{feedback_id}",
    summary="管理员反馈详情",
    description="获取单个反馈的详细信息，包含关联资源和相关反馈",
)
async def get_admin_feedback_detail(
    feedback_id: int, current_user: User = Depends(RequireFeedbackManage)
):
    """
    管理员反馈详情查询
    """
    try:
        feedback = await ResourceInvalidFeedback.get_or_none(id=feedback_id)
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="反馈不存在"
            )

        # 获取关联的资源详情
        resource = await PanResource.filter(resource_key=feedback.resource_id).first()

        # 获取同一资源的其他反馈
        related_feedbacks = (
            await ResourceInvalidFeedback.filter(resource_id=feedback.resource_id)
            .exclude(id=feedback_id)
            .order_by("-created_at")
        )

        # 构建资源信息
        resource_info = None
        if resource:
            resource_info = {
                "id": resource.id,
                "resource_key": resource.resource_key,
                "title": resource.title,
                "pan_type": resource.pan_type,
                "pan_type_name": get_pan_type_name(resource.pan_type),
                "file_type": resource.file_type,
                "file_size": resource.file_size,
                "author": resource.author,
                "is_mine": resource.is_mine,
                "verified_status": resource.verified_status,
                "original_url": resource.original_url,
                "share_url": resource.share_url,
                "created_at": resource.created_at,
                "updated_at": resource.updated_at,
            }

        return {
            "status": "success",
            "data": {
                "feedback": {
                    "id": feedback.id,
                    "resource_id": feedback.resource_id,
                    "pan_type": feedback.pan_type,
                    "pan_type_name": get_pan_type_name(feedback.pan_type),
                    "invalid_type": feedback.invalid_type,
                    "invalid_type_name": get_invalid_type_name(feedback.invalid_type),
                    "description": feedback.description,
                    "contact_info": feedback.contact_info,
                    "is_verified": feedback.is_verified,
                    "verification_result": feedback.verification_result,
                    "created_at": feedback.created_at,
                    "updated_at": feedback.updated_at,
                },
                "resource": resource_info,
                "related_feedbacks": [
                    {
                        "id": f.id,
                        "invalid_type": f.invalid_type,
                        "invalid_type_name": get_invalid_type_name(f.invalid_type),
                        "description": f.description,
                        "created_at": f.created_at,
                    }
                    for f in related_feedbacks
                ],
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取管理员反馈详情失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取反馈详情失败: {str(e)}",
        )
