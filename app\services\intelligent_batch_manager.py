"""
智能批量管理器 - 统一所有 Meilisearch 操作
针对大规模索引（113万+文档）优化的批量处理系统
"""

import asyncio
import logging
import time
import psutil
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Set
from enum import Enum

from app.services.async_meilisearch_service import async_meilisearch_service
from app.models.resource import PanResource

logger = logging.getLogger(__name__)


def serialize_resource_for_meilisearch(resource: Dict) -> Dict:
    """
    序列化资源数据为 Meilisearch 文档格式
    避免循环导入，直接在此处实现
    """
    try:
        # 基础字段映射
        doc = {
            "resource_key": resource.get("resource_key"),
            "title": resource.get("title", ""),
            "file_type": resource.get("file_type", ""),
            "file_size": resource.get("file_size", 0),
            "pan_type": resource.get("pan_type", ""),
            "share_url": resource.get("share_url", ""),
            "share_pwd": resource.get("share_pwd", ""),
            "original_url": resource.get("original_url", ""),
            "thumbnail": resource.get("thumbnail", ""),
            "text_content": resource.get("text_content", ""),
            "author": resource.get("author", ""),
            "author_avatar": resource.get("author_avatar", ""),
            "access_count": resource.get("access_count", 0),
            "is_mine": resource.get("is_mine", False),
            "is_parsed": resource.get("is_parsed", False),
            "verified_status": resource.get("verified_status", ""),
            "expiry_date": resource.get("expiry_date"),
            "created_at": resource.get("created_at"),
            "updated_at": resource.get("updated_at"),
        }

        # 处理日期字段
        for date_field in ["expiry_date", "created_at", "updated_at"]:
            if doc[date_field]:
                if hasattr(doc[date_field], "isoformat"):
                    doc[date_field] = doc[date_field].isoformat()
                else:
                    doc[date_field] = str(doc[date_field])

        return doc

    except Exception as e:
        logger.error(f"序列化资源失败: {e}")
        # 返回最基本的文档结构
        return {
            "resource_key": resource.get("resource_key", ""),
            "title": resource.get("title", ""),
            "file_type": resource.get("file_type", ""),
        }


class OperationType(Enum):
    """操作类型枚举"""

    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    UPSERT = "UPSERT"


class Priority(Enum):
    """操作优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class BatchOperation:
    """批处理操作数据类"""

    operation: OperationType
    resource_key: str
    timestamp: float
    priority: Priority = Priority.NORMAL
    user_triggered: bool = False
    data: Optional[Dict] = None
    retry_count: int = 0


class SystemLoadMonitor:
    """系统负载监控器"""

    def __init__(self):
        self.cpu_threshold = 70
        self.memory_threshold = 80
        self.io_threshold = 50

    async def get_system_load(self) -> Dict[str, float]:
        """获取系统负载信息"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()

            # 计算 I/O 负载（简化版）
            io_load = 0
            if hasattr(self, "_last_disk_io"):
                read_diff = disk_io.read_bytes - self._last_disk_io.read_bytes
                write_diff = disk_io.write_bytes - self._last_disk_io.write_bytes
                io_load = min(
                    100, (read_diff + write_diff) / (1024 * 1024 * 10)
                )  # MB/s转换为百分比

            self._last_disk_io = disk_io

            return {
                "cpu": cpu_percent,
                "memory": memory.percent,
                "io": io_load,
                "available_memory_gb": memory.available / (1024**3),
            }
        except Exception as e:
            logger.error(f"获取系统负载失败: {e}")
            return {"cpu": 50, "memory": 50, "io": 50, "available_memory_gb": 2}

    async def is_system_overloaded(self) -> bool:
        """检查系统是否过载"""
        load = await self.get_system_load()
        return (
            load["cpu"] > self.cpu_threshold
            or load["memory"] > self.memory_threshold
            or load["io"] > self.io_threshold
        )


class AdvancedOperationMerger:
    """高级操作合并器"""

    def __init__(self):
        self.operation_history: Dict[str, List[BatchOperation]] = defaultdict(list)

    def add_operation(self, operation: BatchOperation):
        """添加操作到历史记录"""
        key = operation.resource_key
        self.operation_history[key].append(operation)

        # 保持历史记录不超过10个操作
        if len(self.operation_history[key]) > 10:
            self.operation_history[key] = self.operation_history[key][-10:]

    def merge_operations(self, resource_keys: Set[str]) -> Dict[str, OperationType]:
        """
        智能合并策略：
        1. 同一资源的多次UPDATE只保留最后一次
        2. UPDATE后的DELETE保留DELETE
        3. DELETE后的UPDATE/INSERT保留UPDATE/INSERT
        4. 连续的INSERT合并为一次UPSERT
        """
        merged = {}

        for key in resource_keys:
            operations = self.operation_history.get(key, [])
            if not operations:
                continue

            # 按时间戳排序
            operations.sort(key=lambda x: x.timestamp)

            # 获取最后的有效操作
            final_op = operations[-1].operation

            # 检查是否有DELETE操作
            has_delete = any(op.operation == OperationType.DELETE for op in operations)
            has_update_after_delete = False

            if has_delete:
                # 找到最后一个DELETE操作的位置
                last_delete_idx = -1
                for i, op in enumerate(operations):
                    if op.operation == OperationType.DELETE:
                        last_delete_idx = i

                # 检查DELETE后是否有UPDATE/INSERT
                if last_delete_idx < len(operations) - 1:
                    has_update_after_delete = True

            # 决定最终操作类型
            if has_delete and not has_update_after_delete:
                merged[key] = OperationType.DELETE
            elif final_op in [OperationType.INSERT, OperationType.UPDATE]:
                merged[key] = OperationType.UPSERT
            else:
                merged[key] = final_op

        return merged

    def clear_history(self, resource_keys: Set[str]):
        """清理已处理的操作历史"""
        for key in resource_keys:
            if key in self.operation_history:
                del self.operation_history[key]


class SmartScheduler:
    """智能调度器"""

    def __init__(self):
        self.base_batch_size = 1000  # 基础批大小 (针对113万文档)
        self.max_batch_size = 1000  # 最大批大小
        self.min_batch_size = 500  # 最小批大小
        self.max_wait_time = 600  # 10分钟最大等待
        self.off_peak_hours = (2, 6)  # 凌晨2-6点为低峰期

        self.load_monitor = SystemLoadMonitor()

    async def get_optimal_batch_size(self) -> int:
        """根据系统状态动态调整批大小"""
        system_load = await self.load_monitor.get_system_load()
        current_hour = datetime.now().hour

        # 低峰期使用大批量
        if self.off_peak_hours[0] <= current_hour <= self.off_peak_hours[1]:
            base_size = self.max_batch_size
            logger.info(f"低峰期模式，使用大批量: {base_size}")
        else:
            base_size = self.base_batch_size

        # 根据系统负载调整
        if system_load["cpu"] > 80 or system_load["memory"] > 85:
            adjusted_size = max(self.min_batch_size, base_size // 2)
            logger.warning(
                f"系统负载高，减小批量: {adjusted_size} (CPU: {system_load['cpu']:.1f}%, Memory: {system_load['memory']:.1f}%)"
            )
            return adjusted_size
        elif system_load["cpu"] < 50 and system_load["memory"] < 60:
            adjusted_size = min(self.max_batch_size, int(base_size * 1.5))
            logger.info(f"系统负载低，增大批量: {adjusted_size}")
            return adjusted_size

        return base_size

    async def should_process_now(
        self,
        queue_size: int,
        last_process_time: float,
        has_urgent_operations: bool = False,
    ) -> bool:
        """判断是否应该立即处理"""
        time_since_last = time.time() - last_process_time
        optimal_batch_size = await self.get_optimal_batch_size()

        # 紧急操作立即处理
        if has_urgent_operations:
            logger.info("检测到紧急操作，立即处理")
            return True

        # 强制处理条件
        if queue_size >= optimal_batch_size * 2:  # 队列过大
            logger.info(
                f"队列过大 ({queue_size} >= {optimal_batch_size * 2})，强制处理"
            )
            return True

        if time_since_last >= self.max_wait_time:  # 等待时间过长
            logger.info(
                f"等待时间过长 ({time_since_last:.1f}s >= {self.max_wait_time}s)，强制处理"
            )
            return True

        if (
            queue_size >= optimal_batch_size and time_since_last >= 60
        ):  # 达到批大小且等待1分钟
            logger.info(
                f"达到最优批大小 ({queue_size} >= {optimal_batch_size}) 且等待超过1分钟，开始处理"
            )
            return True

        # 系统负载低时更积极处理
        if not await self.load_monitor.is_system_overloaded():
            if (
                queue_size >= optimal_batch_size // 2 and time_since_last >= 120
            ):  # 系统空闲时更频繁处理
                logger.info("系统负载低，提前处理批量操作")
                return True

        return False

    def get_processing_interval(self) -> float:
        """获取处理间隔"""
        current_hour = datetime.now().hour

        if self.off_peak_hours[0] <= current_hour <= self.off_peak_hours[1]:
            return 60.0  # 低峰期1分钟检查一次
        else:
            return 30.0  # 正常时段30秒检查一次


class IntelligentBatchManager:
    """智能批量管理器 - 统一所有 Meilisearch 操作"""

    def __init__(self):
        # 核心组件
        self.scheduler = SmartScheduler()
        self.operation_merger = AdvancedOperationMerger()
        self.load_monitor = SystemLoadMonitor()

        # 操作队列
        self.pending_operations: deque[BatchOperation] = deque()
        self.priority_queue: deque[BatchOperation] = deque()  # 高优先级队列

        # 状态管理
        self._running = False
        self._process_task: Optional[asyncio.Task] = None
        self._operation_lock = asyncio.Lock()
        self.last_process_time = time.time()

        # 统计信息
        self.stats = {
            "total_processed": 0,
            "total_batches": 0,
            "total_errors": 0,
            "total_merged": 0,
            "avg_batch_size": 0,
            "avg_processing_time": 0,
            "last_process_time": self.last_process_time,
            "queue_size": 0,
            "priority_queue_size": 0,
            "system_load": {},
        }

    async def start(self):
        """启动智能批量管理器"""
        if self._running:
            logger.warning("智能批量管理器已在运行")
            return

        self._running = True
        self._process_task = asyncio.create_task(self._processing_loop())
        logger.info("🚀 智能批量管理器已启动 - 针对大规模索引优化")

        # 记录启动时的系统状态
        system_load = await self.load_monitor.get_system_load()
        logger.info(
            f"系统状态 - CPU: {system_load['cpu']:.1f}%, "
            f"Memory: {system_load['memory']:.1f}%, "
            f"Available Memory: {system_load['available_memory_gb']:.1f}GB"
        )

    async def stop(self):
        """停止智能批量管理器"""
        logger.info("正在停止智能批量管理器...")
        self._running = False

        if self._process_task:
            self._process_task.cancel()
            try:
                await self._process_task
            except asyncio.CancelledError:
                pass

        # 处理剩余操作
        await self._process_all_pending()
        logger.info("✅ 智能批量管理器已停止")

    async def add_operation(
        self,
        operation_type: str,
        resource_key: str,
        priority: Priority = Priority.NORMAL,
        user_triggered: bool = False,
        data: Optional[Dict] = None,
    ) -> bool:
        """添加操作到批量队列"""
        try:
            # 转换操作类型
            if operation_type.upper() == "DELETE":
                op_type = OperationType.DELETE
            elif operation_type.upper() in ["INSERT", "UPDATE"]:
                op_type = OperationType.UPSERT  # 统一为UPSERT
            else:
                logger.warning(f"未知操作类型: {operation_type}")
                return False

            # 创建批量操作
            batch_op = BatchOperation(
                operation=op_type,
                resource_key=resource_key,
                timestamp=time.time(),
                priority=priority,
                user_triggered=user_triggered,
                data=data,
            )

            async with self._operation_lock:
                # 检查队列大小
                total_queue_size = len(self.pending_operations) + len(
                    self.priority_queue
                )
                max_queue_size = 10000  # 最大队列大小

                if total_queue_size >= max_queue_size:
                    logger.error(
                        f"队列已满 ({total_queue_size}/{max_queue_size})，拒绝新操作"
                    )
                    return False

                # 根据优先级添加到不同队列
                if priority in [Priority.HIGH, Priority.URGENT]:
                    self.priority_queue.append(batch_op)
                    logger.debug(f"添加高优先级操作: {op_type.value} {resource_key}")
                else:
                    self.pending_operations.append(batch_op)
                    logger.debug(f"添加普通操作: {op_type.value} {resource_key}")

                # 添加到操作合并器
                self.operation_merger.add_operation(batch_op)

                # 更新统计
                self.stats["queue_size"] = len(self.pending_operations)
                self.stats["priority_queue_size"] = len(self.priority_queue)

                return True

        except Exception as e:
            logger.error(f"添加操作失败: {e}", exc_info=True)
            return False

    async def _processing_loop(self):
        """主处理循环"""
        logger.info("智能批量处理循环已启动")

        while self._running:
            try:
                # 获取处理间隔
                interval = self.scheduler.get_processing_interval()

                # 检查是否需要处理
                total_queue_size = len(self.pending_operations) + len(
                    self.priority_queue
                )
                has_urgent = any(
                    op.priority == Priority.URGENT for op in self.priority_queue
                )

                should_process = await self.scheduler.should_process_now(
                    total_queue_size, self.last_process_time, has_urgent
                )

                if should_process and total_queue_size > 0:
                    await self._process_batch()

                # 更新系统负载统计
                self.stats["system_load"] = await self.load_monitor.get_system_load()

                # 等待下次检查
                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"处理循环异常: {e}", exc_info=True)
                await asyncio.sleep(30)  # 异常时等待30秒再继续

    async def _process_batch(self):
        """处理一个批次的操作"""
        start_time = time.time()

        try:
            # 获取最优批大小
            optimal_batch_size = await self.scheduler.get_optimal_batch_size()

            async with self._operation_lock:
                # 收集操作（优先处理高优先级队列）
                operations_to_process = []

                # 先处理高优先级操作
                while (
                    self.priority_queue
                    and len(operations_to_process) < optimal_batch_size
                ):
                    operations_to_process.append(self.priority_queue.popleft())

                # 再处理普通操作
                while (
                    self.pending_operations
                    and len(operations_to_process) < optimal_batch_size
                ):
                    operations_to_process.append(self.pending_operations.popleft())

                if not operations_to_process:
                    return

                # 更新队列统计
                self.stats["queue_size"] = len(self.pending_operations)
                self.stats["priority_queue_size"] = len(self.priority_queue)

            logger.info(f"🔄 开始处理批次: {len(operations_to_process)} 个操作")

            # 操作合并和去重
            resource_keys = {op.resource_key for op in operations_to_process}
            merged_operations = self.operation_merger.merge_operations(resource_keys)

            # 统计合并效果
            original_count = len(operations_to_process)
            merged_count = len(merged_operations)
            self.stats["total_merged"] += original_count - merged_count

            if original_count != merged_count:
                logger.info(
                    f"📊 操作合并: {original_count} → {merged_count} (节省 {original_count - merged_count} 个操作)"
                )

            # 分类处理
            delete_keys = []
            upsert_keys = []

            for resource_key, operation in merged_operations.items():
                if operation == OperationType.DELETE:
                    delete_keys.append(resource_key)
                else:
                    upsert_keys.append(resource_key)

            # 执行批量操作
            success_count = 0

            # 处理删除操作
            if delete_keys:
                success_count += await self._batch_delete(delete_keys)

            # 处理更新/插入操作
            if upsert_keys:
                success_count += await self._batch_upsert(upsert_keys)

            # 清理操作历史
            self.operation_merger.clear_history(resource_keys)

            # 更新统计信息
            processing_time = time.time() - start_time
            self._update_stats(
                len(operations_to_process), processing_time, success_count
            )

            logger.info(
                f"✅ 批次处理完成: {success_count}/{len(operations_to_process)} 成功, 耗时: {processing_time:.2f}s"
            )

        except Exception as e:
            logger.error(f"批次处理失败: {e}", exc_info=True)
            self.stats["total_errors"] += 1

    async def _batch_delete(self, resource_keys: List[str]) -> int:
        """批量删除操作"""
        try:
            logger.info(f"🗑️ 批量删除: {len(resource_keys)} 个文档")

            result = await async_meilisearch_service.delete_documents(resource_keys)
            if result:
                logger.info(
                    f"✅ 删除成功: {len(resource_keys)} 个文档，任务ID: {result.get('taskUid')}"
                )
                return len(resource_keys)
            else:
                logger.error(f"❌ 删除失败: {len(resource_keys)} 个文档")
                return 0

        except Exception as e:
            logger.error(f"批量删除异常: {e}", exc_info=True)
            return 0

    async def _batch_upsert(self, resource_keys: List[str]) -> int:
        """批量更新/插入操作"""
        try:
            logger.info(f"📝 批量更新: {len(resource_keys)} 个文档")

            # 批量查询数据库
            resources = await PanResource.filter(
                resource_key__in=resource_keys
            ).values()

            if not resources:
                logger.warning("没有找到需要更新的资源数据")
                return 0

            # 序列化文档
            documents = []
            for resource in resources:
                try:
                    doc = serialize_resource_for_meilisearch(resource)
                    documents.append(doc)
                except Exception as e:
                    logger.error(f"序列化资源失败 {resource.get('resource_key')}: {e}")

            if not documents:
                logger.warning("没有有效的文档需要更新")
                return 0

            # 提交到 Meilisearch
            result = await async_meilisearch_service.add_documents(documents)
            if result:
                logger.info(
                    f"✅ 更新成功: {len(documents)} 个文档，任务ID: {result.get('taskUid')}"
                )
                return len(documents)
            else:
                logger.error(f"❌ 更新失败: {len(documents)} 个文档")
                return 0

        except Exception as e:
            logger.error(f"批量更新异常: {e}", exc_info=True)
            return 0

    async def _process_all_pending(self):
        """处理所有待处理的操作（用于关闭时）"""
        logger.info("处理所有剩余操作...")

        while self.pending_operations or self.priority_queue:
            await self._process_batch()
            await asyncio.sleep(0.1)  # 短暂休息避免阻塞

    def _update_stats(
        self, batch_size: int, processing_time: float, success_count: int
    ):
        """更新统计信息"""
        self.stats["total_processed"] += success_count
        self.stats["total_batches"] += 1
        self.last_process_time = time.time()
        self.stats["last_process_time"] = self.last_process_time

        # 计算平均值
        if self.stats["avg_batch_size"] == 0:
            self.stats["avg_batch_size"] = batch_size
        else:
            self.stats["avg_batch_size"] = (
                self.stats["avg_batch_size"] * 0.8 + batch_size * 0.2
            )

        if self.stats["avg_processing_time"] == 0:
            self.stats["avg_processing_time"] = processing_time
        else:
            self.stats["avg_processing_time"] = (
                self.stats["avg_processing_time"] * 0.8 + processing_time * 0.2
            )

    def get_stats(self) -> Dict:
        """获取详细统计信息"""
        return {
            **self.stats,
            "running": self._running,
            "optimal_batch_size": (
                asyncio.create_task(self.scheduler.get_optimal_batch_size())
                if self._running
                else "N/A"
            ),
            "is_off_peak": self._is_off_peak_time(),
            "queue_health": self._get_queue_health(),
        }

    def _is_off_peak_time(self) -> bool:
        """检查是否为低峰期"""
        current_hour = datetime.now().hour
        return (
            self.scheduler.off_peak_hours[0]
            <= current_hour
            <= self.scheduler.off_peak_hours[1]
        )

    def _get_queue_health(self) -> str:
        """获取队列健康状态"""
        total_size = len(self.pending_operations) + len(self.priority_queue)

        if total_size == 0:
            return "空闲"
        elif total_size < 1000:
            return "正常"
        elif total_size < 5000:
            return "繁忙"
        else:
            return "过载"

    async def force_process(self) -> Dict:
        """强制处理当前队列中的所有操作"""
        logger.info("🚨 强制处理所有队列操作")

        start_time = time.time()
        initial_count = len(self.pending_operations) + len(self.priority_queue)

        await self._process_all_pending()

        processing_time = time.time() - start_time

        result = {
            "initial_queue_size": initial_count,
            "processing_time": processing_time,
            "success": True,
        }

        logger.info(
            f"✅ 强制处理完成: {initial_count} 个操作，耗时: {processing_time:.2f}s"
        )
        return result


# 创建全局智能批量管理器实例
intelligent_batch_manager = IntelligentBatchManager()
