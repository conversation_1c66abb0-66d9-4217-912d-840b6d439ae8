import asyncio
import psutil
import logging
import time
import gc
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class HealthMetrics:
    """健康指标数据类"""
    timestamp: float
    memory_rss_mb: float
    memory_vms_mb: float
    memory_percent: float
    cpu_percent: float
    open_files: int
    connections: int
    gc_collections: Dict[str, int]
    uptime_seconds: float


class HealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, check_interval: float = 60.0, memory_threshold_mb: float = 500.0):
        self.check_interval = check_interval
        self.memory_threshold_mb = memory_threshold_mb
        self.start_time = time.time()
        
        # 监控状态
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # 历史指标（保留最近100个数据点）
        self.metrics_history = []
        self.max_history_size = 100
        
        # 告警状态
        self.alerts = {
            'high_memory': False,
            'high_cpu': False,
            'too_many_files': False
        }
        
        # 获取进程对象
        try:
            self.process = psutil.Process()
        except Exception as e:
            logger.error(f"无法获取进程信息: {e}")
            self.process = None
    
    async def start(self):
        """启动健康监控"""
        if self._running or not self.process:
            return
        
        self._running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"健康监控已启动，检查间隔: {self.check_interval}秒")
    
    async def stop(self):
        """停止健康监控"""
        self._running = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("健康监控已停止")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                metrics = await self._collect_metrics()
                if metrics:
                    await self._process_metrics(metrics)
                
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康监控异常: {e}", exc_info=True)
                await asyncio.sleep(self.check_interval)
    
    async def _collect_metrics(self) -> Optional[HealthMetrics]:
        """收集系统指标"""
        try:
            # 内存信息
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            
            # 打开的文件数
            try:
                open_files = self.process.num_fds() if hasattr(self.process, 'num_fds') else len(self.process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            # 网络连接数
            try:
                connections = len(self.process.connections())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = 0
            
            # 垃圾回收统计
            gc_stats = {}
            for i in range(3):
                gc_stats[f'gen_{i}'] = gc.get_count()[i]
            
            # 运行时间
            uptime = time.time() - self.start_time
            
            metrics = HealthMetrics(
                timestamp=time.time(),
                memory_rss_mb=memory_info.rss / 1024 / 1024,
                memory_vms_mb=memory_info.vms / 1024 / 1024,
                memory_percent=memory_percent,
                cpu_percent=cpu_percent,
                open_files=open_files,
                connections=connections,
                gc_collections=gc_stats,
                uptime_seconds=uptime
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}", exc_info=True)
            return None
    
    async def _process_metrics(self, metrics: HealthMetrics):
        """处理指标数据"""
        # 添加到历史记录
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history.pop(0)
        
        # 检查告警条件
        await self._check_alerts(metrics)
        
        # 记录指标
        self._log_metrics(metrics)
    
    async def _check_alerts(self, metrics: HealthMetrics):
        """检查告警条件"""
        # 内存告警
        if metrics.memory_rss_mb > self.memory_threshold_mb:
            if not self.alerts['high_memory']:
                self.alerts['high_memory'] = True
                logger.warning(f"内存使用过高: {metrics.memory_rss_mb:.2f}MB (阈值: {self.memory_threshold_mb}MB)")
        else:
            if self.alerts['high_memory']:
                self.alerts['high_memory'] = False
                logger.info(f"内存使用恢复正常: {metrics.memory_rss_mb:.2f}MB")
        
        # CPU告警
        if metrics.cpu_percent > 80.0:
            if not self.alerts['high_cpu']:
                self.alerts['high_cpu'] = True
                logger.warning(f"CPU使用率过高: {metrics.cpu_percent:.2f}%")
        else:
            if self.alerts['high_cpu']:
                self.alerts['high_cpu'] = False
                logger.info(f"CPU使用率恢复正常: {metrics.cpu_percent:.2f}%")
        
        # 文件描述符告警
        if metrics.open_files > 1000:
            if not self.alerts['too_many_files']:
                self.alerts['too_many_files'] = True
                logger.warning(f"打开文件数过多: {metrics.open_files}")
        else:
            if self.alerts['too_many_files']:
                self.alerts['too_many_files'] = False
                logger.info(f"打开文件数恢复正常: {metrics.open_files}")
    
    def _log_metrics(self, metrics: HealthMetrics):
        """记录指标信息"""
        logger.info(
            f"系统指标 - 内存: {metrics.memory_rss_mb:.2f}MB ({metrics.memory_percent:.1f}%), "
            f"CPU: {metrics.cpu_percent:.1f}%, 文件: {metrics.open_files}, "
            f"连接: {metrics.connections}, 运行时间: {metrics.uptime_seconds:.0f}s"
        )
    
    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """获取当前指标"""
        if not self.metrics_history:
            return None
        
        latest = self.metrics_history[-1]
        return asdict(latest)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        # 计算平均值和最大值
        memory_values = [m.memory_rss_mb for m in self.metrics_history]
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        
        return {
            'total_samples': len(self.metrics_history),
            'uptime_seconds': time.time() - self.start_time,
            'memory': {
                'current_mb': memory_values[-1] if memory_values else 0,
                'avg_mb': sum(memory_values) / len(memory_values) if memory_values else 0,
                'max_mb': max(memory_values) if memory_values else 0,
                'threshold_mb': self.memory_threshold_mb
            },
            'cpu': {
                'current_percent': cpu_values[-1] if cpu_values else 0,
                'avg_percent': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                'max_percent': max(cpu_values) if cpu_values else 0
            },
            'alerts': self.alerts.copy(),
            'last_check': datetime.fromtimestamp(self.metrics_history[-1].timestamp, tz=timezone.utc).isoformat() if self.metrics_history else None
        }
    
    async def force_gc(self):
        """强制垃圾回收"""
        try:
            before_count = sum(gc.get_count())
            collected = gc.collect()
            after_count = sum(gc.get_count())
            
            logger.info(f"强制垃圾回收: 回收 {collected} 个对象，对象数从 {before_count} 减少到 {after_count}")
            return collected
        except Exception as e:
            logger.error(f"强制垃圾回收失败: {e}", exc_info=True)
            return 0


# 创建全局健康监控器实例
health_monitor = HealthMonitor(
    check_interval=60.0,  # 每分钟检查一次
    memory_threshold_mb=500.0  # 内存阈值500MB
)
