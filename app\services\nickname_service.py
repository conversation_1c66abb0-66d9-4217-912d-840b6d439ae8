"""
昵称管理服务
"""

from typing import Optional, List, Dict, Any
from tortoise.transactions import in_transaction
from datetime import datetime
import logging
import re

from app.models.user import User, UserNicknameHistory

logger = logging.getLogger(__name__)


class NicknameService:
    """昵称管理服务类"""
    
    # 敏感词列表（实际项目中应该从数据库或配置文件读取）
    SENSITIVE_WORDS = [
        "admin", "管理员", "系统", "客服", "官方", "运营", "moderator",
        "root", "administrator", "support", "service", "bot", "机器人",
        "test", "测试", "demo", "演示", "null", "undefined", "system"
    ]
    
    # 保留用户名列表
    RESERVED_USERNAMES = [
        "admin", "root", "system", "api", "www", "mail", "ftp", "ssh",
        "support", "help", "info", "contact", "about", "terms", "privacy"
    ]

    @classmethod
    async def change_nickname(
        cls,
        user: User,
        new_nickname: str,
        reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> User:
        """修改用户昵称"""
        
        # 检查修改权限
        if not user.can_change_nickname():
            next_change_date = user.get_next_nickname_change_date()
            raise ValueError(f"昵称每年只能修改一次，下次可修改时间：{next_change_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 验证昵称格式和内容
        cls._validate_nickname(new_nickname)
        
        # 检查昵称唯一性
        await cls._check_nickname_uniqueness(new_nickname, user.id)
        
        async with in_transaction():
            # 记录昵称修改历史
            await UserNicknameHistory.create(
                user=user,
                old_nickname=user.nickname,
                new_nickname=new_nickname,
                change_reason=reason or "用户主动修改",
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # 更新用户昵称
            user.nickname = new_nickname
            user.last_nickname_change = datetime.utcnow()
            user.nickname_change_count += 1
            await user.save()
            
            logger.info(f"用户 {user.username} 修改昵称: {user.nickname} -> {new_nickname}")
            
            return user

    @classmethod
    def _validate_nickname(cls, nickname: str):
        """验证昵称格式和内容"""
        
        # 长度检查
        if len(nickname) < 2:
            raise ValueError("昵称长度不能少于2个字符")
        if len(nickname) > 100:
            raise ValueError("昵称长度不能超过100个字符")
        
        # 格式检查：只允许中文、字母、数字、下划线、连字符
        if not re.match(r"^[\u4e00-\u9fa5a-zA-Z0-9_-]+$", nickname):
            raise ValueError("昵称只能包含中文、字母、数字、下划线和连字符")
        
        # 不能全是数字
        if nickname.isdigit():
            raise ValueError("昵称不能全是数字")
        
        # 不能全是特殊字符
        if re.match(r"^[_-]+$", nickname):
            raise ValueError("昵称不能全是特殊字符")
        
        # 敏感词检查
        cls._check_sensitive_words(nickname)
        
        # 保留用户名检查
        cls._check_reserved_usernames(nickname)

    @classmethod
    def _check_sensitive_words(cls, nickname: str):
        """检查敏感词"""
        nickname_lower = nickname.lower()
        
        for word in cls.SENSITIVE_WORDS:
            if word.lower() in nickname_lower:
                raise ValueError(f"昵称不能包含敏感词：{word}")

    @classmethod
    def _check_reserved_usernames(cls, nickname: str):
        """检查保留用户名"""
        nickname_lower = nickname.lower()
        
        if nickname_lower in cls.RESERVED_USERNAMES:
            raise ValueError("该昵称为系统保留，不能使用")

    @classmethod
    async def _check_nickname_uniqueness(cls, nickname: str, exclude_user_id: int):
        """检查昵称唯一性"""
        existing_user = await User.filter(nickname=nickname).exclude(id=exclude_user_id).first()
        if existing_user:
            raise ValueError("该昵称已被使用")

    @classmethod
    async def get_nickname_history(
        cls,
        user: User,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取用户昵称修改历史"""
        
        offset = (page - 1) * size
        
        # 获取总数
        total = await UserNicknameHistory.filter(user=user).count()
        
        # 获取历史记录
        history_records = await UserNicknameHistory.filter(user=user).order_by("-created_at").offset(offset).limit(size)
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
            "history": [
                {
                    "id": record.id,
                    "old_nickname": record.old_nickname,
                    "new_nickname": record.new_nickname,
                    "change_reason": record.change_reason,
                    "created_at": record.created_at
                }
                for record in history_records
            ]
        }

    @classmethod
    async def check_nickname_availability(cls, nickname: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """检查昵称可用性"""
        
        result = {
            "available": False,
            "reason": None,
            "suggestions": []
        }
        
        try:
            # 验证昵称格式
            cls._validate_nickname(nickname)
            
            # 检查唯一性
            if user_id:
                await cls._check_nickname_uniqueness(nickname, user_id)
            else:
                existing_user = await User.filter(nickname=nickname).first()
                if existing_user:
                    raise ValueError("该昵称已被使用")
            
            result["available"] = True
            
        except ValueError as e:
            result["reason"] = str(e)
            
            # 如果是重复昵称，生成建议
            if "已被使用" in str(e):
                result["suggestions"] = await cls._generate_nickname_suggestions(nickname)
        
        return result

    @classmethod
    async def _generate_nickname_suggestions(cls, base_nickname: str, count: int = 5) -> List[str]:
        """生成昵称建议"""
        suggestions = []
        
        # 生成不同的变体
        variants = [
            f"{base_nickname}_{i}" for i in range(1, count + 1)
        ]
        
        # 添加随机数字后缀
        import random
        for i in range(count):
            random_suffix = random.randint(100, 999)
            variants.append(f"{base_nickname}{random_suffix}")
        
        # 检查每个变体的可用性
        for variant in variants:
            try:
                cls._validate_nickname(variant)
                existing_user = await User.filter(nickname=variant).first()
                if not existing_user:
                    suggestions.append(variant)
                    if len(suggestions) >= count:
                        break
            except ValueError:
                continue
        
        return suggestions

    @classmethod
    async def get_nickname_statistics(cls) -> Dict[str, Any]:
        """获取昵称统计信息"""
        
        # 总修改次数
        total_changes = await UserNicknameHistory.count()
        
        # 今日修改次数
        from datetime import date
        today = date.today()
        today_changes = await UserNicknameHistory.filter(
            created_at__gte=datetime.combine(today, datetime.min.time())
        ).count()
        
        # 最活跃的修改用户（修改次数最多的前10名）
        from tortoise.functions import Count
        active_users = await User.annotate(
            change_count=Count("nickname_history")
        ).filter(change_count__gt=0).order_by("-change_count").limit(10)
        
        return {
            "total_nickname_changes": total_changes,
            "today_nickname_changes": today_changes,
            "most_active_users": [
                {
                    "username": user.username,
                    "nickname": user.nickname,
                    "change_count": user.change_count
                }
                for user in active_users
            ]
        }
