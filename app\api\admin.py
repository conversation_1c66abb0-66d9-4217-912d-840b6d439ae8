from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import Optional, List
from tortoise.exceptions import IntegrityError
from tortoise.queryset import Q
import logging
from datetime import datetime

from app.models.auth_models import (
    UserListResponse,
    UserDetailResponse,
    UserCreateRequest,
    UserUpdateRequest,
    UserStatusUpdateRequest,
    UserPasswordResetRequest,
    ApiResponse,
)
from app.models.user import User, Role
from app.core.permissions import (
    get_current_admin_user,
    RequireUserManage,
    Permissions,
    require_permission,
)
from app.services.points_service import PointsService

logger = logging.getLogger("admin-api")
router = APIRouter(tags=["管理员-用户管理"])

# 导入资源管理的子路由
from app.api.admin_resources import router as admin_resources_router

# 包含子路由 - 只包含资源管理（反馈管理和配置管理将在main.py中单独注册）
router.include_router(admin_resources_router, prefix="", tags=["管理员-资源管理"])


@router.get(
    "/users",
    response_model=dict,
    summary="获取用户列表",
    description="分页获取用户列表，支持搜索和筛选",
)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="搜索关键词（用户名/邮箱）"),
    status: Optional[str] = Query(None, description="用户状态筛选"),
    role_id: Optional[int] = Query(None, description="角色ID筛选"),
    current_user: User = Depends(RequireUserManage),
):
    """获取用户列表"""
    try:
        # 构建查询条件
        query = User.all()

        # 关键词搜索
        if keyword:
            query = query.filter(
                Q(username__icontains=keyword)
                | Q(email__icontains=keyword)
                | Q(nickname__icontains=keyword)
            )

        # 状态筛选
        if status:
            query = query.filter(status=status)

        # 角色筛选
        if role_id:
            query = query.filter(role_id=role_id)

        # 预加载关联数据
        query = query.prefetch_related("role")

        # 计算总数
        total = await query.count()

        # 分页查询
        offset = (page - 1) * size
        users = await query.offset(offset).limit(size)

        # 转换为响应模型
        user_list = []
        for user in users:
            user_data = UserListResponse.model_validate(user)
            user_list.append(user_data.model_dump())

        return {
            "status": "success",
            "data": {
                "users": user_list,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size,
                },
            },
        }

    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户列表失败"
        )


@router.get(
    "/users/{user_id}",
    response_model=UserDetailResponse,
    summary="获取用户详情",
    description="获取指定用户的详细信息",
)
async def get_user_detail(
    user_id: int, current_user: User = Depends(RequireUserManage)
):
    """获取用户详情"""
    try:
        user = await User.get_or_none(id=user_id).prefetch_related("role")

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        return UserDetailResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户详情失败"
        )


@router.post(
    "/users",
    response_model=UserDetailResponse,
    summary="创建用户",
    description="管理员创建新用户",
)
async def create_user(
    user_data: UserCreateRequest, current_user: User = Depends(RequireUserManage)
):
    """创建用户"""
    try:
        # 检查用户名和邮箱是否已存在
        existing_user = await User.filter(
            Q(username=user_data.username) | Q(email=user_data.email)
        ).first()

        if existing_user:
            if existing_user.username == user_data.username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已存在"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已被注册"
                )

        # 检查角色是否存在
        role = await Role.get_or_none(id=user_data.role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="指定的角色不存在"
            )

        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            nickname=user_data.nickname or user_data.username,
            role=role,
            status=user_data.status,
            email_verified=True,  # 管理员创建的用户默认已验证
        )
        user.set_password(user_data.password)

        await user.save()
        await user.fetch_related("role")

        # 给管理员创建的用户也发放注册积分
        try:
            await PointsService.register_bonus(user)
            logger.info(f"管理员创建的用户 {user.username} 获得注册积分奖励")
        except Exception as e:
            logger.error(f"给管理员创建的用户 {user.username} 发放注册积分失败: {e}")

        logger.info(f"管理员 {current_user.username} 创建了用户 {user.username}")

        return UserDetailResponse.model_validate(user)

    except HTTPException:
        raise
    except IntegrityError as e:
        logger.error(f"创建用户数据库错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="创建用户失败，请检查输入信息",
        )
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建用户失败"
        )


@router.put(
    "/users/{user_id}",
    response_model=UserDetailResponse,
    summary="更新用户信息",
    description="管理员更新用户信息",
)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    current_user: User = Depends(RequireUserManage),
):
    """更新用户信息"""
    try:
        user = await User.get_or_none(id=user_id).prefetch_related("role")

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 防止管理员修改自己的角色和状态
        if user.id == current_user.id:
            if user_data.role_id is not None and user_data.role_id != user.role_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="不能修改自己的角色"
                )
            if user_data.status is not None and user_data.status != user.status:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="不能修改自己的状态"
                )

        # 检查用户名和邮箱唯一性
        if user_data.username and user_data.username != user.username:
            existing = (
                await User.filter(username=user_data.username)
                .exclude(id=user_id)
                .first()
            )
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已存在"
                )

        if user_data.email and user_data.email != user.email:
            existing = (
                await User.filter(email=user_data.email).exclude(id=user_id).first()
            )
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已被注册"
                )

        # 检查角色是否存在
        if user_data.role_id:
            role = await Role.get_or_none(id=user_data.role_id)
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="指定的角色不存在"
                )
            user.role = role

        # 更新用户信息
        if user_data.username:
            user.username = user_data.username
        if user_data.email:
            user.email = user_data.email
        if user_data.nickname is not None:
            user.nickname = user_data.nickname
        if user_data.status:
            user.status = user_data.status

        await user.save()
        await user.fetch_related("role")

        logger.info(f"管理员 {current_user.username} 更新了用户 {user.username} 的信息")

        return UserDetailResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新用户信息失败"
        )


@router.delete(
    "/users/{user_id}",
    response_model=ApiResponse,
    summary="删除用户",
    description="管理员删除用户（软删除）",
)
async def delete_user(user_id: int, current_user: User = Depends(RequireUserManage)):
    """删除用户"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 防止删除自己
        if user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="不能删除自己"
            )

        # 软删除：设置状态为deleted
        user.status = "deleted"
        await user.save()

        logger.info(f"管理员 {current_user.username} 删除了用户 {user.username}")

        return ApiResponse(status="success", message="用户删除成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除用户失败"
        )


@router.post(
    "/users/{user_id}/freeze",
    response_model=ApiResponse,
    summary="冻结用户",
    description="冻结用户账户",
)
async def freeze_user(user_id: int, current_user: User = Depends(RequireUserManage)):
    """冻结用户"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 防止冻结自己
        if user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="不能冻结自己"
            )

        if user.status == "frozen":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户已被冻结"
            )

        user.status = "frozen"
        await user.save()

        # 登出用户的所有会话
        from app.core.auth import AuthService

        await AuthService.logout_all_sessions(user.id)

        logger.info(f"管理员 {current_user.username} 冻结了用户 {user.username}")

        return ApiResponse(status="success", message="用户冻结成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"冻结用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="冻结用户失败"
        )


@router.post(
    "/users/{user_id}/unfreeze",
    response_model=ApiResponse,
    summary="解冻用户",
    description="解冻用户账户",
)
async def unfreeze_user(user_id: int, current_user: User = Depends(RequireUserManage)):
    """解冻用户"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        if user.status != "frozen":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户未被冻结"
            )

        user.status = "active"
        user.unlock_account()  # 解锁账户
        await user.save()

        logger.info(f"管理员 {current_user.username} 解冻了用户 {user.username}")

        return ApiResponse(status="success", message="用户解冻成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解冻用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="解冻用户失败"
        )


@router.post(
    "/users/{user_id}/reset-password",
    response_model=ApiResponse,
    summary="重置用户密码",
    description="管理员重置用户密码",
)
async def reset_user_password(
    user_id: int,
    password_data: UserPasswordResetRequest,
    current_user: User = Depends(RequireUserManage),
):
    """重置用户密码"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 重置密码
        user.set_password(password_data.new_password)
        user.unlock_account()  # 解锁账户
        await user.save()

        # 登出用户的所有会话
        from app.core.auth import AuthService

        await AuthService.logout_all_sessions(user.id)

        logger.info(f"管理员 {current_user.username} 重置了用户 {user.username} 的密码")

        return ApiResponse(status="success", message="密码重置成功，用户需要重新登录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置用户密码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="重置用户密码失败"
        )


@router.get(
    "/users/{user_id}/sessions",
    response_model=dict,
    summary="获取用户会话列表",
    description="获取指定用户的活跃会话列表",
)
async def get_user_sessions(
    user_id: int, current_user: User = Depends(RequireUserManage)
):
    """获取用户会话列表"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        from app.models.user import UserSession

        sessions = await UserSession.filter(user_id=user_id, is_active=True).order_by(
            "-created_at"
        )

        session_list = []
        for session in sessions:
            session_data = {
                "id": session.id,
                "user_agent": session.user_agent,
                "ip_address": session.ip_address,
                "created_at": session.created_at,
                "expires_at": session.expires_at,
                "is_expired": session.is_expired(),
            }
            session_list.append(session_data)

        return {
            "status": "success",
            "data": {
                "user_id": user_id,
                "username": user.username,
                "sessions": session_list,
                "total": len(session_list),
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户会话列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户会话列表失败",
        )


@router.delete(
    "/users/{user_id}/sessions/{session_id}",
    response_model=ApiResponse,
    summary="强制下线用户会话",
    description="管理员强制下线指定用户会话",
)
async def force_logout_session(
    user_id: int, session_id: int, current_user: User = Depends(RequireUserManage)
):
    """强制下线用户会话"""
    try:
        user = await User.get_or_none(id=user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        from app.models.user import UserSession

        session = await UserSession.get_or_none(id=session_id, user_id=user_id)

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="会话不存在"
            )

        # 禁用会话
        session.is_active = False
        await session.save()

        logger.info(
            f"管理员 {current_user.username} 强制下线了用户 {user.username} 的会话 {session_id}"
        )

        return ApiResponse(status="success", message="会话已强制下线")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制下线会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="强制下线会话失败"
        )
