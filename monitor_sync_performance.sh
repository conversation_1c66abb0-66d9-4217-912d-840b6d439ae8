#!/bin/bash

# Meilisearch同步性能监控脚本
# 用于监控优化后的同步架构性能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取当前时间戳
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 检查Meilisearch任务队列状态
check_meilisearch_queue() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 检查Meilisearch任务队列..."
    
    # 获取任务统计
    local stats=$(curl -s -H "Authorization: Bearer masterKey" \
        "http://localhost:7700/tasks?limit=1" | jq -r '.total // 0' 2>/dev/null)
    
    if [ "$stats" != "null" ] && [ "$stats" != "" ]; then
        echo -e "${GREEN}总任务数: $stats${NC}"
        
        # 检查排队任务
        local enqueued=$(curl -s -H "Authorization: Bearer masterKey" \
            "http://localhost:7700/tasks?statuses=enqueued&limit=1" | jq -r '.total // 0' 2>/dev/null)
        
        # 检查处理中任务
        local processing=$(curl -s -H "Authorization: Bearer masterKey" \
            "http://localhost:7700/tasks?statuses=processing&limit=1" | jq -r '.total // 0' 2>/dev/null)
        
        echo -e "排队任务: ${YELLOW}$enqueued${NC}"
        echo -e "处理中任务: ${YELLOW}$processing${NC}"
        
        # 警告阈值
        if [ "$enqueued" -gt 100 ]; then
            echo -e "${RED}警告: 排队任务过多 ($enqueued)${NC}"
        fi
        
        if [ "$processing" -gt 10 ]; then
            echo -e "${RED}警告: 处理中任务过多 ($processing)${NC}"
        fi
    else
        echo -e "${RED}无法获取Meilisearch任务统计${NC}"
    fi
    
    echo ""
}

# 检查同步日志表状态
check_sync_log_table() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 检查同步日志表状态..."
    
    # 检查未处理记录数
    local unprocessed=$(docker exec pan_so_postgres psql -U pan_so_user -d pan_so_db -t -c \
        "SELECT COUNT(*) FROM meilisearch_sync_log WHERE processed = FALSE;" 2>/dev/null | tr -d ' ')
    
    if [ "$unprocessed" != "" ]; then
        echo -e "未处理记录: ${YELLOW}$unprocessed${NC}"
        
        if [ "$unprocessed" -gt 1000 ]; then
            echo -e "${RED}警告: 未处理记录过多 ($unprocessed)${NC}"
        fi
    else
        echo -e "${RED}无法查询同步日志表${NC}"
    fi
    
    # 检查最近的同步活动
    local recent_activity=$(docker exec pan_so_postgres psql -U pan_so_user -d pan_so_db -t -c \
        "SELECT COUNT(*) FROM meilisearch_sync_log WHERE created_at > NOW() - INTERVAL '5 minutes';" 2>/dev/null | tr -d ' ')
    
    if [ "$recent_activity" != "" ]; then
        echo -e "最近5分钟活动: ${GREEN}$recent_activity${NC} 条记录"
    fi
    
    echo ""
}

# 检查系统资源使用
check_system_resources() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 检查系统资源使用..."
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo -e "CPU使用率: ${GREEN}${cpu_usage}%${NC}"
    
    # 内存使用率
    local mem_info=$(free | grep Mem)
    local mem_total=$(echo $mem_info | awk '{print $2}')
    local mem_used=$(echo $mem_info | awk '{print $3}')
    local mem_percent=$(echo "scale=1; $mem_used * 100 / $mem_total" | bc)
    echo -e "内存使用率: ${GREEN}${mem_percent}%${NC}"
    
    # 磁盘I/O (如果iotop可用)
    if command -v iotop >/dev/null 2>&1; then
        local io_info=$(sudo iotop -b -n 1 -q | head -1)
        echo -e "磁盘I/O: ${GREEN}$io_info${NC}"
    fi
    
    echo ""
}

# 检查Docker容器状态
check_docker_containers() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 检查Docker容器状态..."
    
    # Meilisearch容器
    local meili_status=$(docker inspect pan_so_meilisearch --format='{{.State.Status}}' 2>/dev/null)
    if [ "$meili_status" = "running" ]; then
        echo -e "Meilisearch: ${GREEN}运行中${NC}"
        
        # 获取容器资源使用
        local meili_stats=$(docker stats pan_so_meilisearch --no-stream --format \
            "table {{.CPUPerc}}\t{{.MemUsage}}" | tail -n 1)
        echo -e "  资源使用: ${GREEN}$meili_stats${NC}"
    else
        echo -e "Meilisearch: ${RED}$meili_status${NC}"
    fi
    
    # PostgreSQL容器
    local pg_status=$(docker inspect pan_so_postgres --format='{{.State.Status}}' 2>/dev/null)
    if [ "$pg_status" = "running" ]; then
        echo -e "PostgreSQL: ${GREEN}运行中${NC}"
    else
        echo -e "PostgreSQL: ${RED}$pg_status${NC}"
    fi
    
    echo ""
}

# 检查PM2进程状态
check_pm2_processes() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 检查PM2进程状态..."
    
    # 检查定时同步服务
    local scheduler_status=$(pm2 jlist | jq -r '.[] | select(.name=="meilisearch-scheduler") | .pm2_env.status' 2>/dev/null)
    if [ "$scheduler_status" = "online" ]; then
        echo -e "定时同步服务: ${GREEN}运行中${NC}"
        
        # 获取内存使用
        local scheduler_memory=$(pm2 jlist | jq -r '.[] | select(.name=="meilisearch-scheduler") | .monit.memory' 2>/dev/null)
        if [ "$scheduler_memory" != "null" ] && [ "$scheduler_memory" != "" ]; then
            local memory_mb=$(echo "scale=1; $scheduler_memory / 1024 / 1024" | bc)
            echo -e "  内存使用: ${GREEN}${memory_mb}MB${NC}"
        fi
    else
        echo -e "定时同步服务: ${RED}$scheduler_status${NC}"
    fi
    
    # 检查主API服务
    local api_status=$(pm2 jlist | jq -r '.[] | select(.name=="pan-so-api") | .pm2_env.status' 2>/dev/null)
    if [ "$api_status" = "online" ]; then
        echo -e "主API服务: ${GREEN}运行中${NC}"
    else
        echo -e "主API服务: ${RED}$api_status${NC}"
    fi
    
    echo ""
}

# 性能测试
performance_test() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 执行性能测试..."
    
    # 测试搜索响应时间
    local search_start=$(date +%s.%N)
    local search_result=$(curl -s -H "Authorization: Bearer M3lPuLTZeJeA7urKBe6YN7nAPJxBZxA7Lr7kamVXW_k" \
        "http://localhost:7700/indexes/resources/search" \
        -d '{"q": "test", "limit": 10}' \
        -H "Content-Type: application/json")
    local search_end=$(date +%s.%N)
    local search_time=$(echo "$search_end - $search_start" | bc)
    
    if echo "$search_result" | jq -e '.hits' >/dev/null 2>&1; then
        echo -e "搜索响应时间: ${GREEN}${search_time}秒${NC}"
        
        if (( $(echo "$search_time > 2.0" | bc -l) )); then
            echo -e "${RED}警告: 搜索响应时间超过2秒${NC}"
        fi
    else
        echo -e "${RED}搜索测试失败${NC}"
    fi
    
    echo ""
}

# 显示最近的日志
show_recent_logs() {
    echo -e "${BLUE}[$(get_timestamp)]${NC} 最近的同步日志..."
    
    # 显示PM2日志的最后几行
    pm2 logs meilisearch-scheduler --lines 5 --nostream 2>/dev/null | tail -10
    
    echo ""
}

# 主监控函数
main() {
    echo "=========================================="
    echo "Meilisearch同步性能监控"
    echo "时间: $(get_timestamp)"
    echo "=========================================="
    
    check_meilisearch_queue
    check_sync_log_table
    check_system_resources
    check_docker_containers
    check_pm2_processes
    performance_test
    show_recent_logs
    
    echo "=========================================="
    echo "监控完成"
    echo "=========================================="
}

# 持续监控模式
continuous_monitor() {
    while true; do
        clear
        main
        echo "按 Ctrl+C 退出持续监控模式"
        sleep 30
    done
}

# 检查参数
if [ "$1" = "--continuous" ] || [ "$1" = "-c" ]; then
    continuous_monitor
else
    main
fi
