import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union

from telethon import TelegramClient, events, connection, functions, types
from telethon.tl.types import Message, MessageMediaPhoto, MessageMediaDocument
from tortoise.transactions import in_transaction

from app.crawlers.base_crawler import BaseCrawler
from app.models.pydantic_models import CrawledResource
from app.models.resource import PanResource
from app.utils.common import infer_file_type
from app.utils.config import settings
from app.utils.pan_url_parser import parse_pan_url

# 日志配置
logger = logging.getLogger("telegram-crawler")
logger.setLevel(logging.INFO)

# 从配置文件加载Telegram爬虫配置
TELEGRAM_CONF = settings.get("telegram_crawler", {})
API_ID = TELEGRAM_CONF.get("api_id")
API_HASH = TELEGRAM_CONF.get("api_hash")
SESSION_NAME = TELEGRAM_CONF.get("session_name", "telegram_session")
CHANNELS = TELEGRAM_CONF.get("channels", [])
MESSAGE_LIMIT = TELEGRAM_CONF.get("message_limit", 100)
PROXY_CONF = TELEGRAM_CONF.get("proxy", {})

# 用于匹配常见网盘链接的正则表达式
PAN_LINK_PATTERNS = [
    re.compile(r"https?://pan\.baidu\.com/s/[\w-]+(?:\?pwd=[\w-]+)?"),
    re.compile(r"https?://www\.aliyundrive\.com/s/[\w-]+"),
    re.compile(r"https?://www\.alipan\.com/s/[\w-]+"),
    re.compile(r"https?://pan\.quark\.cn/s/[\w-]+"),
    re.compile(r"https?://pan\.xunlei\.com/s/[\w-]+(?:\?pwd=[\w-]+)?"),
]


class TelegramCrawler(BaseCrawler):
    """
    通过Telethon持续监控指定Telegram频道的新消息，并自动抓取、解析、入库其中的网盘链接。
    支持消息遗漏检测和恢复机制。
    """

    def __init__(self):
        """
        初始化Telegram监控爬虫。
        """
        super().__init__(crawler_name="telegram")

        self.api_id = self.config.get("api_id")
        self.api_hash = self.config.get("api_hash")
        self.session_name = self.config.get("session_name", "telegram_session")
        self.channels = self.config.get("channels", [])

        # 消息遗漏处理相关配置
        self.enable_catch_up = self.config.get("enable_catch_up", True)
        self.periodic_check_interval = self.config.get(
            "periodic_check_interval", 300
        )  # 5分钟
        self.connection_check_interval = self.config.get(
            "connection_check_interval", 30
        )  # 30秒
        self.max_gap_recovery_attempts = self.config.get("max_gap_recovery_attempts", 3)

        # 错误恢复配置
        self.enable_aggressive_error_recovery = self.config.get(
            "enable_aggressive_error_recovery", True
        )

        # 存储频道状态信息
        self.channel_states = {}  # 存储每个频道的pts状态
        self.last_activity_time = {}  # 存储每个频道的最后活动时间
        self.gap_recovery_attempts = {}  # 存储间隙恢复尝试次数

        # 控制标志
        self.is_monitoring = False
        self.periodic_tasks = []

        if not self.api_id or not self.api_hash:
            raise ValueError("API ID 和 API Hash 不能为空，请在配置文件中正确设置。")

        # 根据官方文档正确配置代理
        proxy_config = self.config.get("proxy", {})
        proxy = None
        conn = None  # Use a different name to avoid conflict with the imported 'connection' module

        if proxy_config and proxy_config.get("enabled"):
            proxy_type = proxy_config.get("type", "socks5").lower()
            host = proxy_config.get("host")
            port = proxy_config.get("port")

            if not host or not port:
                self.logger.warning("代理已启用，但 host 或 port 未在配置文件中设置。")
            else:
                if proxy_type == "mtproxy":
                    secret = proxy_config.get("secret")
                    if not secret:
                        raise ValueError(
                            "MTProto代理已启用，但 secret 未在配置文件中设置。"
                        )

                    # Telethon 会自动处理 'dd' 或 'ee' 前缀，直接传递原始 secret 即可
                    # 遵循官方文档：为MTProto代理设置专用的connection模式和不含协议名的proxy元组
                    conn = connection.ConnectionTcpMTProxyRandomizedIntermediate
                    proxy = (host, port, secret)
                    self.logger.info(f"正在使用 MTProto 代理: {host}:{port}")
                else:
                    # 标准代理 (SOCKS/HTTP) 使用默认connection和标准proxy元组
                    username = proxy_config.get("username")
                    password = proxy_config.get("password")
                    proxy = (proxy_type, host, port, True, username, password)
                    self.logger.info(
                        f"正在使用 {proxy_type.upper()} 代理: {host}:{port}"
                    )

        self.client = TelegramClient(
            self.session_name,
            self.api_id,
            self.api_hash,
            connection=conn,
            proxy=proxy,
            auto_reconnect=True,  # 自动重连
            connection_retries=5,  # 连接重试次数
            retry_delay=1,  # 重试延迟
            catch_up=self.enable_catch_up,  # 启用catch_up
            flood_sleep_threshold=60,  # 洪水等待阈值
            request_retries=3,  # 请求重试次数
        )
        self.logger.info("🚀 Telegram客户端已初始化，启用了消息遗漏恢复机制！")
        self.logger.info(
            f"📊 配置信息: catch_up={self.enable_catch_up}, 定期检查间隔={self.periodic_check_interval}秒, 连接检查间隔={self.connection_check_interval}秒"
        )

    def _parse_message_to_resources(self, message: Message) -> List[CrawledResource]:
        """
        从单条消息中解析出所有网盘链接，并将其转换为标准化的 CrawledResource 对象列表。

        Args:
            message: Telethon 的消息对象。

        Returns:
            一个包含解析出的资源信息的 CrawledResource 对象列表。
        """
        text = message.text or message.caption or ""
        if not text:
            return []

        # 1. 使用 PAN_LINK_PATTERNS 匹配所有支持的网盘链接
        all_links = []
        for pattern in PAN_LINK_PATTERNS:
            all_links.extend(pattern.findall(text))

        if not all_links:
            return []

        # 2. 提取公共信息
        # 优先使用 "名称：" 字段，如果没有，则尝试从文本前部截取作为标题
        title_match = re.search(r"(?:名称|标题|名字)[：:](.*?)\n", text)
        title = (
            title_match.group(1).strip() if title_match else text.split("\n")[0].strip()
        )
        # 清理标题，限制长度
        title = re.sub(r"#\w+\s*", "", title).strip()[:250]

        desc_match = re.search(
            r"(?:描述|内容|简介)[：:]([\s\S]*?)(?:\n\n|链接|标签|#)", text
        )
        description = desc_match.group(1).strip() if desc_match else None

        tags_match = re.search(r"🏷 (?:标签|分类)[：:](.*?)(?:\n|⚠️|📢|$)", text)
        tags = []
        if tags_match:
            tags_str = tags_match.group(1).strip()
            tags = [tag.strip() for tag in tags_str.split("#") if tag.strip()]
        else:
            # 如果没有明确的标签行，则从全文中提取 hashtag
            tags = [tag.strip() for tag in re.findall(r"#(\w+)", text)]

        # 3. 为每个链接创建一条 CrawledResource 记录
        found_resources = []
        for link in all_links:
            parsed_url_info = parse_pan_url(link)
            if not parsed_url_info:
                self.logger.debug(f"链接无法被解析，已跳过: {link}")
                continue

            # 提取链接附带的提取码
            code_match = re.search(
                r"(?:提取码|访问码|密码)[：: ]*([a-zA-Z0-9]{4,})", text
            )
            password = (
                code_match.group(1) if code_match else parsed_url_info.get("share_pwd")
            )

            # 根据标题和标签推断文件类型
            tags_str_for_inference = " ".join(tags)
            file_type = infer_file_type(title, tags_str_for_inference)

            resource_data = CrawledResource(
                title=title,
                original_url=link,
                pan_type=parsed_url_info["pan_type_int"],
                password=password,
                description=description,
                tags=tags,
                file_type=file_type,  # 保存推断出的文件类型
                source=f"telegram_{message.chat.username or message.chat.id}",
                updated_at=message.date,
                raw_data={
                    "message_id": message.id,
                    "raw_text": text,
                },
            )
            found_resources.append(resource_data)

        if found_resources:
            self.logger.info(
                f"在频道 {found_resources[0].source} 的新消息中解析出 {len(found_resources)} 个资源: 《{title}》"
            )
        return found_resources

    async def _save_resources(self, resources: List[CrawledResource]):
        """
        将 CrawledResource 对象列表保存到数据库。
        如果资源已存在但标题发生变更，则更新标题和发布时间。
        """
        if not resources:
            return

        new_entries_count = 0
        updated_entries_count = 0
        # 对于长时运行的服务，数据库已在外部初始化，此处仅需开启事务
        async with in_transaction():
            for res in resources:
                parsed_info = parse_pan_url(res.original_url)
                if not parsed_info or not parsed_info.get("resource_key"):
                    self.logger.warning(
                        f"无法从URL解析resource_key，已跳过入库: {res.original_url}"
                    )
                    continue

                resource_key = parsed_info["resource_key"]

                # 检查资源是否已存在
                existing_resource = await PanResource.filter(
                    resource_key=resource_key
                ).first()

                if existing_resource:
                    # 资源已存在，检查标题是否发生变更
                    if existing_resource.title != res.title:
                        # 标题发生变更，更新标题和发布时间（取最新时间）
                        old_title = existing_resource.title
                        existing_resource.title = res.title

                        # 更新发布时间为最新时间（比较现有时间和新时间，取较新的）
                        if (
                            existing_resource.updated_at is None
                            or res.updated_at > existing_resource.updated_at
                        ):
                            existing_resource.updated_at = res.updated_at

                        # 保存更新
                        await existing_resource.save(
                            update_fields=["title", "updated_at"]
                        )
                        updated_entries_count += 1

                        self.logger.info(
                            f"🔄 资源标题已更新!\n"
                            f"   - 资源键: {resource_key}\n"
                            f"   - 旧标题: {old_title}\n"
                            f"   - 新标题: {res.title}\n"
                            f"   - 更新时间: {res.updated_at}\n"
                            f"   - 链接: {res.original_url}"
                        )
                    else:
                        self.logger.info(
                            f"资源已存在且标题未变更，跳过处理: {res.title} ({resource_key})"
                        )
                    continue

                # 创建新的 PanResource 实例
                new_resource = await PanResource.create(
                    resource_key=resource_key,
                    title=res.title,
                    original_url=res.original_url,
                    pan_type=res.pan_type,
                    share_pwd=res.password,
                    text_content=res.description,
                    author="97_bot",  # 作者固定为 "97_bot"
                    file_type=res.file_type,  # 保存文件类型
                    updated_at=res.updated_at,
                    is_parsed=False,  # 标记为待进一步解析
                )
                new_entries_count += 1
                # 按要求打印入库信息
                self.logger.info(
                    f"🎉 新资源成功入库!\n"
                    f"   - 标题: {new_resource.title}\n"
                    f"   - 链接: {new_resource.original_url}\n"
                    f"   - 类型: {new_resource.file_type}\n"
                    f"   - 作者: {new_resource.author}"
                )

        if new_entries_count > 0 or updated_entries_count > 0:
            self.logger.info(
                f"本次共新增 {new_entries_count} 条资源，更新 {updated_entries_count} 条资源到数据库。"
            )

    async def _check_update_gap(self, update, channel_id):
        """
        检查更新间隙并尝试恢复遗漏的消息。

        Args:
            update: 收到的更新对象
            channel_id: 频道ID
        """
        if not hasattr(update, "pts") or not hasattr(update, "pts_count"):
            return False

        current_pts = update.pts
        pts_count = getattr(update, "pts_count", 1)
        local_pts = self.channel_states.get(channel_id, 0)

        expected_pts = local_pts + pts_count

        if expected_pts < current_pts:
            # 发现间隙
            gap_size = current_pts - expected_pts
            self.logger.warning(
                f"频道 {channel_id} 发现消息间隙: 本地pts={local_pts}, "
                f"远程pts={current_pts}, 间隙大小={gap_size}"
            )

            # 检查恢复尝试次数
            attempts = self.gap_recovery_attempts.get(channel_id, 0)
            if attempts < self.max_gap_recovery_attempts:
                self.gap_recovery_attempts[channel_id] = attempts + 1
                await self._recover_channel_gap(channel_id, local_pts, current_pts)
                return True
            else:
                self.logger.error(
                    f"频道 {channel_id} 间隙恢复尝试次数已达上限 {self.max_gap_recovery_attempts}，跳过恢复"
                )

        elif expected_pts > current_pts:
            # 重复更新，忽略
            self.logger.debug(f"频道 {channel_id} 收到重复更新，已忽略")
            return True
        else:
            # 正常更新
            self.channel_states[channel_id] = current_pts
            self.gap_recovery_attempts[channel_id] = 0  # 重置恢复尝试次数

        return False

    async def _recover_channel_gap(self, channel_id, local_pts, remote_pts):
        """
        恢复频道的消息间隙。

        Args:
            channel_id: 频道ID
            local_pts: 本地pts
            remote_pts: 远程pts
        """
        try:
            self.logger.info(f"开始恢复频道 {channel_id} 的消息间隙...")

            # 获取频道实体
            channel_entity = None
            for channel_identifier in self.channels:
                try:
                    entity = await self.client.get_entity(channel_identifier)
                    if entity.id == abs(channel_id):
                        channel_entity = entity
                        break
                except Exception as e:
                    self.logger.debug(f"获取频道实体失败: {e}")

            if not channel_entity:
                self.logger.error(f"无法找到频道 {channel_id} 的实体，跳过间隙恢复")
                return

            # 检查 pts 值的有效性
            if local_pts <= 0:
                self.logger.warning(
                    f"频道 {channel_id} 的本地 pts ({local_pts}) 无效，跳过间隙恢复"
                )
                return

            # 调用getChannelDifference恢复间隙
            result = await self.client(
                functions.updates.GetChannelDifferenceRequest(
                    channel=channel_entity,
                    filter=types.ChannelMessagesFilterEmpty(),
                    pts=local_pts,
                    limit=min(100, remote_pts - local_pts),  # 限制单次恢复的消息数量
                )
            )

            # 处理恢复的消息
            recovered_count = 0
            if hasattr(result, "new_messages"):
                for message in result.new_messages:
                    if isinstance(message, Message) and message.text:
                        resources = self._parse_message_to_resources(message)
                        if resources:
                            await self._save_resources(resources)
                            recovered_count += len(resources)

            # 更新本地状态
            if hasattr(result, "pts"):
                self.channel_states[channel_id] = result.pts

            self.logger.info(
                f"频道 {channel_id} 间隙恢复完成，恢复了 {recovered_count} 个资源"
            )

        except Exception as e:
            # 使用统一的错误处理方法
            channel_entity = None
            for channel_identifier in self.channels:
                try:
                    entity = await self.client.get_entity(channel_identifier)
                    if entity.id == abs(channel_id):
                        channel_entity = entity
                        break
                except:
                    continue

            self._handle_telegram_error(e, channel_entity, "恢复频道间隙")

    async def _new_message_handler(
        self, event: Union[events.NewMessage.Event, events.MessageEdited.Event]
    ):
        """
        处理新消息或消息编辑事件的回调函数，包含消息遗漏检测。
        """
        try:
            message = event.message
            chat = await message.get_chat()
            chat_name = getattr(chat, "username", chat.id)
            chat_id = chat.id

            # 更新频道最后活动时间
            self.last_activity_time[chat_id] = datetime.now()

            # 检查更新间隙（仅对频道消息进行检测）
            if hasattr(event, "original_update") and self.enable_catch_up:
                gap_detected = await self._check_update_gap(
                    event.original_update, chat_id
                )
                if gap_detected:
                    self.logger.info(f"频道 {chat_name} 检测到间隙，已触发恢复机制")

            # 根据您的要求，先打印（记录）收到的原始消息
            self.logger.info(
                f"监控到来自频道 '{chat_name}' 的消息 (ID: {message.id})，原文如下:\n"
                f"-------------------- MESSAGE START --------------------\n"
                f"{message.text}\n"
                f"-------------------- MESSAGE END --------------------"
            )

            # 解析消息并将其转换为 CrawledResource 列表
            resources = self._parse_message_to_resources(message)

            # 如果解析出资源，则保存到数据库
            if resources:
                await self._save_resources(resources)
            else:
                self.logger.info(
                    f"来自频道 '{chat_name}' 的消息 (ID: {message.id}) 中未发现可解析的网盘链接。"
                )
        except Exception as e:
            self.logger.error(f"处理新消息时发生未知错误: {e}", exc_info=True)

    async def _periodic_channel_check(self):
        """
        定期检查频道是否有遗漏的消息，特别是对于大型频道。
        """
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.periodic_check_interval)

                if not self.is_monitoring:
                    break

                self.logger.debug("开始定期频道检查...")

                for channel_identifier in self.channels:
                    try:
                        entity = await self.client.get_entity(channel_identifier)
                        channel_id = entity.id

                        # 检查频道是否长时间没有活动
                        last_activity = self.last_activity_time.get(channel_id)
                        if last_activity:
                            time_since_activity = datetime.now() - last_activity
                            if time_since_activity > timedelta(
                                minutes=15
                            ):  # 15分钟无活动
                                self.logger.info(
                                    f"频道 {getattr(entity, 'username', entity.id)} "
                                    f"已 {time_since_activity.total_seconds()/60:.1f} 分钟无活动，执行主动检查"
                                )
                                await self._check_channel_for_new_messages(entity)
                        else:
                            # 首次检查
                            await self._check_channel_for_new_messages(entity)

                    except Exception as e:
                        self.logger.error(
                            f"定期检查频道 {channel_identifier} 时发生错误: {e}"
                        )

            except asyncio.CancelledError:
                self.logger.info("定期频道检查任务被取消")
                break
            except Exception as e:
                self.logger.error(f"定期频道检查发生未知错误: {e}", exc_info=True)

    async def _check_channel_for_new_messages(self, channel_entity):
        """
        主动检查频道是否有新消息。

        Args:
            channel_entity: 频道实体
        """
        try:
            channel_id = channel_entity.id
            local_pts = self.channel_states.get(channel_id, 0)

            # 如果 pts 为 0 或空，先尝试获取频道的当前状态
            if local_pts == 0:
                try:
                    # 使用 getMessages 获取最新消息来初始化 pts
                    messages = await self.client.get_messages(channel_entity, limit=10)
                    if messages:
                        # 处理获取到的消息
                        processed_count = 0
                        for message in messages:
                            if isinstance(message, Message) and message.text:
                                resources = self._parse_message_to_resources(message)
                                if resources:
                                    await self._save_resources(resources)
                                    processed_count += len(resources)

                        self.logger.info(
                            f"频道 {getattr(channel_entity, 'username', channel_id)} pts 为空，"
                            f"已处理 {len(messages)} 条最新消息，发现 {processed_count} 个资源"
                        )
                        # 设置一个小的 pts 值，避免下次检查时再次为 0
                        self.channel_states[channel_id] = 1
                        return
                except Exception as e:
                    self.logger.warning(
                        f"初始化频道 {getattr(channel_entity, 'username', channel_id)} 状态失败: {e}"
                    )
                    return

            # 获取频道差异
            result = await self.client(
                functions.updates.GetChannelDifferenceRequest(
                    channel=channel_entity,
                    filter=types.ChannelMessagesFilterEmpty(),
                    pts=local_pts,
                    limit=50,  # 限制检查的消息数量
                )
            )

            # 处理新消息
            new_messages_count = 0
            if hasattr(result, "new_messages") and result.new_messages:
                for message in result.new_messages:
                    if isinstance(message, Message) and message.text:
                        resources = self._parse_message_to_resources(message)
                        if resources:
                            await self._save_resources(resources)
                            new_messages_count += len(resources)

                        # 记录消息（与正常处理器保持一致）
                        self.logger.info(
                            f"定期检查发现来自频道 '{getattr(channel_entity, 'username', channel_id)}' "
                            f"的消息 (ID: {message.id})，原文如下:\n"
                            f"-------------------- MESSAGE START --------------------\n"
                            f"{message.text}\n"
                            f"-------------------- MESSAGE END --------------------"
                        )

            # 更新本地状态
            if hasattr(result, "pts"):
                self.channel_states[channel_id] = result.pts

            # 更新最后活动时间
            self.last_activity_time[channel_id] = datetime.now()

            if new_messages_count > 0:
                self.logger.info(
                    f"定期检查频道 {getattr(channel_entity, 'username', channel_id)} "
                    f"发现 {new_messages_count} 个新资源"
                )

        except Exception as e:
            # 使用统一的错误处理方法
            self._handle_telegram_error(e, channel_entity, "主动检查频道")

    async def _monitor_connection(self):
        """
        监控连接状态并处理断线重连。
        """
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.connection_check_interval)

                if not self.is_monitoring:
                    break

                if not self.client.is_connected():
                    self.logger.warning("检测到连接断开，尝试重连...")
                    try:
                        await self.client.connect()
                        if self.enable_catch_up:
                            await self.client.catch_up()
                        self.logger.info("重连成功，已执行catch_up")
                    except Exception as e:
                        self.logger.error(f"重连失败: {e}")

            except asyncio.CancelledError:
                self.logger.info("连接监控任务被取消")
                break
            except Exception as e:
                self.logger.error(f"连接监控发生未知错误: {e}", exc_info=True)

    async def start_monitoring(self):
        """
        【持续监控】的核心逻辑：连接客户端，注册处理器，并一直运行。
        集成了消息遗漏检测和恢复机制。
        """
        # 增加 Telethon 库自身的日志级别，以便观察详细的连接和事件信息
        logging.getLogger("telethon").setLevel(logging.INFO)

        try:
            # .start() 会自动处理连接、登录和会话保存
            # 如果未登录，它会自动在命令行发起交互式登录流程
            await self.client.start()

            self.logger.info("Telegram客户端已成功连接并授权。")

            # 启动时执行catch_up以获取离线期间的更新
            if self.enable_catch_up:
                self.logger.info("正在执行启动时的catch_up操作...")
                try:
                    await self.client.catch_up()
                    self.logger.info("启动时catch_up操作完成")
                except Exception as e:
                    self.logger.warning(f"启动时catch_up操作失败: {e}")

        except Exception as e:
            self.logger.error(f"连接Telegram时发生错误: {e}", exc_info=True)
            self.logger.error(
                "【常见问题提示】: \n"
                "1. 如果是首次运行, 请在有命令行的环境中运行 `python run_telegram_monitor.py` 来完成交互式登录授权, 生成session文件。\n"
                "2. 检查 `config.yaml` 中的 `api_id` 和 `api_hash` 是否正确。\n"
                "3. 检查代理设置是否正确, 以及代理服务是否可用。\n"
                "4. 如果提示数据库或其他奇怪错误, 可尝试删除目录下的 `*.session` 文件后重新登录。"
            )
            return

        # 新增测试代码：验证并打印所有需要监控的频道信息
        try:
            self.logger.info("正在验证频道访问权限并获取频道信息...")
            accessible_channels = []
            for channel_identifier in self.channels:
                try:
                    # 使用 get_entity 获取频道的详细信息
                    entity = await self.client.get_entity(channel_identifier)
                    channel_name = getattr(
                        entity, "title", getattr(entity, "username", entity.id)
                    )
                    self.logger.info(
                        f"✅ 成功识别频道: '{channel_name}' (ID: {entity.id})"
                    )
                    accessible_channels.append(entity)

                    # 初始化频道状态
                    await self._initialize_channel_state(entity)
                    self.last_activity_time[entity.id] = datetime.now()
                    self.gap_recovery_attempts[entity.id] = 0

                except Exception as e:
                    self.logger.error(
                        f"❌ 无法访问频道 '{channel_identifier}': {e}. \n"
                        f"   请检查：1. 频道用户名或链接是否正确。 2. 您是否已加入该频道。 3. 该频道是否为公开频道或您有权限访问。"
                    )

            if not accessible_channels:
                self.logger.error(
                    "致命错误：配置的所有频道均无法访问，监控服务无法启动。请检查日志中的错误提示并修正您的 `config.yaml` 文件。"
                )
                return

            # 设置监控状态
            self.is_monitoring = True

            # 使用验证后可访问的频道实体来注册事件处理器，这比使用字符串更可靠
            self.client.add_event_handler(
                self._new_message_handler, events.NewMessage(chats=accessible_channels)
            )
            # 新增：同时监听消息编辑事件，兼容通过编辑发布内容的频道
            self.client.add_event_handler(
                self._new_message_handler,
                events.MessageEdited(chats=accessible_channels),
            )

            # 启动后台任务
            self.periodic_tasks = [
                asyncio.create_task(self._periodic_channel_check()),
                asyncio.create_task(self._monitor_connection()),
            ]

            self.logger.info(
                f"已启动对 {len(accessible_channels)} 个可访问频道的持续监控 (同时监听新消息和消息编辑)。"
            )
            self.logger.info("消息遗漏检测和恢复机制已启用。")
            self.logger.info("服务正在运行，等待新消息...")

        except Exception as e:
            self.logger.error(f"在验证频道或启动监听时发生意外错误: {e}", exc_info=True)
            return

        try:
            # 持续运行，直到客户端断开连接
            await self.client.run_until_disconnected()
        finally:
            # 清理后台任务
            await self._cleanup_tasks()

    async def _cleanup_tasks(self):
        """
        清理后台任务。
        """
        self.is_monitoring = False

        for task in self.periodic_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    self.logger.error(f"清理后台任务时发生错误: {e}")

        self.periodic_tasks.clear()
        self.logger.info("后台任务已清理完成")

    async def _initialize_channel_state(self, channel_entity):
        """
        初始化频道状态，获取有效的 pts 值。

        Args:
            channel_entity: 频道实体
        """
        try:
            channel_id = channel_entity.id

            # 尝试获取频道的最新消息来初始化状态
            try:
                messages = await self.client.get_messages(channel_entity, limit=1)
                if messages:
                    # 如果有消息，设置一个较小的初始 pts 值
                    # 这样可以避免 PersistentTimestampEmptyError
                    self.channel_states[channel_id] = 1
                    self.logger.debug(
                        f"频道 {getattr(channel_entity, 'username', channel_id)} 状态已初始化"
                    )
                else:
                    # 如果没有消息，设置为 0
                    self.channel_states[channel_id] = 0
                    self.logger.debug(
                        f"频道 {getattr(channel_entity, 'username', channel_id)} 无消息，状态设为 0"
                    )
            except Exception as e:
                # 如果获取消息失败，设置为 0
                self.channel_states[channel_id] = 0
                self.logger.debug(
                    f"初始化频道 {getattr(channel_entity, 'username', channel_id)} 状态失败: {e}"
                )

        except Exception as e:
            self.logger.error(f"初始化频道状态时发生错误: {e}", exc_info=True)

    def _handle_telegram_error(self, error, channel_entity=None, operation="操作"):
        """
        统一处理 Telegram 相关错误。

        Args:
            error: 异常对象
            channel_entity: 频道实体（可选）
            operation: 操作描述

        Returns:
            bool: 是否应该重试操作
        """
        error_str = str(error)

        if (
            "PersistentTimestampEmptyError" in error_str
            or "Persistent timestamp empty" in error_str
        ):
            if channel_entity and self.enable_aggressive_error_recovery:
                channel_id = channel_entity.id
                channel_name = getattr(channel_entity, "username", channel_id)
                self.logger.warning(
                    f"频道 {channel_name} 在{operation}时出现持久化时间戳为空错误，重置频道状态"
                )
                # 重置频道状态
                self.channel_states[channel_id] = 0
                self.gap_recovery_attempts[channel_id] = 0
                return True  # 建议重试
            else:
                self.logger.warning(f"{operation}时出现持久化时间戳为空错误: {error}")
                return False

        elif "FloodWaitError" in error_str:
            # 处理频率限制错误
            self.logger.warning(f"{operation}时遇到频率限制: {error}")
            return False  # 不建议立即重试

        elif "ChannelPrivateError" in error_str:
            # 频道私有或无权限
            channel_name = (
                getattr(channel_entity, "username", "unknown")
                if channel_entity
                else "unknown"
            )
            self.logger.error(f"频道 {channel_name} 无权限访问: {error}")
            return False

        else:
            # 其他错误
            channel_name = (
                getattr(channel_entity, "username", "unknown")
                if channel_entity
                else "unknown"
            )
            self.logger.error(
                f"频道 {channel_name} {operation}时发生错误: {error}", exc_info=True
            )
            return False

    async def close(self):
        """断开与Telegram的连接并清理资源。"""
        self.logger.info("正在关闭Telegram监控服务...")

        # 清理后台任务
        await self._cleanup_tasks()

        # 断开客户端连接
        if self.client.is_connected():
            await self.client.disconnect()
            self.logger.info("Telegram客户端已断开连接。")

        # 清理状态信息
        self.channel_states.clear()
        self.last_activity_time.clear()
        self.gap_recovery_attempts.clear()

        self.logger.info("Telegram监控服务已完全关闭。")

    async def crawl(self, **kwargs) -> List[CrawledResource]:
        """对于Telegram爬虫，一次性抓取不是其主要功能，但可以保留一个简单的实现用于测试或特定场景。"""
        self.logger.warning(
            "Telegram爬虫主要设计为持续监控模式，'crawl'方法仅用于有限抓取。"
        )
        limit = kwargs.get("limit", 10)  # 默认只抓取最新的10条
        all_resources = []

        try:
            await self.client.connect()
            for channel in self.channels:
                self.logger.info(f"开始一次性抓取频道: {channel}，消息上限: {limit}...")
                async for message in self.client.iter_messages(channel, limit=limit):
                    if isinstance(message, Message) and message.text:
                        parsed_resources = self._parse_message_to_resources(message)
                        if parsed_resources:
                            all_resources.extend(parsed_resources)
            self.logger.info(f"一次性抓取完成，共找到 {len(all_resources)} 个资源。")
        finally:
            await self.close()

        # 对于一次性抓取，也可以选择直接入库
        await self._save_resources(all_resources)

        return all_resources


# 全局实例化 TelegramCrawler (将被移除)
# telegram_crawler = TelegramCrawler()

# 启动脚本的 __main__ 块 (将被移除)
