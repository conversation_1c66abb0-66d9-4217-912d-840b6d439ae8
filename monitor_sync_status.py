#!/usr/bin/env python3
"""
实时监控Meilisearch同步状态
用于检查同步服务是否正常工作
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(ROOT_DIR)

from app.core.tortoise_config import TORTOISE_ORM
from app.models.resource import PanResource
from app.utils.config import settings
from tortoise import Tortoise, connections
import aiohttp


class SyncStatusMonitor:
    """同步状态监控器"""
    
    def __init__(self):
        self.meili_config = settings.get("meilisearch", {})
        self.host = self.meili_config.get("host", "http://127.0.0.1:7700")
        self.api_key = self.meili_config.get("api_key", "masterKey")
        self.index_name = self.meili_config.get("index_name", "resources")
        
    async def check_meilisearch_tasks(self) -> Dict[str, Any]:
        """检查Meilisearch任务队列状态"""
        print("🔍 检查Meilisearch任务队列...")
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {self.api_key}"}
                
                # 获取任务统计
                tasks_url = f"{self.host}/tasks"
                async with session.get(tasks_url, headers=headers) as response:
                    if response.status == 200:
                        tasks_data = await response.json()
                        total_tasks = tasks_data.get("total", 0)
                        
                        # 获取不同状态的任务数量
                        status_counts = {}
                        for status in ["enqueued", "processing", "succeeded", "failed"]:
                            status_url = f"{self.host}/tasks?statuses={status}&limit=1"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_data = await status_response.json()
                                    status_counts[status] = status_data.get("total", 0)
                        
                        # 获取最近的任务
                        recent_url = f"{self.host}/tasks?limit=5"
                        recent_tasks = []
                        async with session.get(recent_url, headers=headers) as recent_response:
                            if recent_response.status == 200:
                                recent_data = await recent_response.json()
                                recent_tasks = recent_data.get("results", [])
                        
                        result = {
                            "total_tasks": total_tasks,
                            "status_counts": status_counts,
                            "recent_tasks": recent_tasks,
                            "queue_health": "healthy" if status_counts.get("enqueued", 0) < 100 else "warning"
                        }
                        
                        print(f"   总任务数: {total_tasks}")
                        print(f"   排队中: {status_counts.get('enqueued', 0)}")
                        print(f"   处理中: {status_counts.get('processing', 0)}")
                        print(f"   已成功: {status_counts.get('succeeded', 0)}")
                        print(f"   已失败: {status_counts.get('failed', 0)}")
                        
                        if status_counts.get("failed", 0) > 0:
                            print(f"⚠️  发现 {status_counts['failed']} 个失败任务")
                        
                        return result
                    else:
                        return {"status": "error", "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            print(f"❌ 检查任务队列失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def check_sync_log_table(self) -> Dict[str, Any]:
        """检查同步日志表状态"""
        print("🔍 检查同步日志表...")
        
        try:
            conn = connections.get("default")
            
            # 检查是否存在同步日志表
            table_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'meilisearch_sync_log'
            );
            """
            
            result = await conn.execute_query(table_exists_query)
            table_exists = result[1][0]['exists'] if result and result[1] else False
            
            if not table_exists:
                print("   ⚠️  同步日志表不存在，可能使用的是其他同步方式")
                return {"table_exists": False}
            
            # 获取未处理记录数
            unprocessed_query = "SELECT COUNT(*) as count FROM meilisearch_sync_log WHERE processed = FALSE"
            unprocessed_result = await conn.execute_query(unprocessed_query)
            unprocessed_count = unprocessed_result[1][0]['count'] if unprocessed_result and unprocessed_result[1] else 0
            
            # 获取最近的同步活动
            recent_activity_query = """
            SELECT COUNT(*) as count 
            FROM meilisearch_sync_log 
            WHERE created_at > NOW() - INTERVAL '5 minutes'
            """
            recent_result = await conn.execute_query(recent_activity_query)
            recent_count = recent_result[1][0]['count'] if recent_result and recent_result[1] else 0
            
            # 获取最近的错误
            error_query = """
            SELECT operation, resource_key, error_message, created_at
            FROM meilisearch_sync_log 
            WHERE processed = FALSE AND error_message IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 5
            """
            error_result = await conn.execute_query(error_query)
            recent_errors = error_result[1] if error_result and error_result[1] else []
            
            result = {
                "table_exists": True,
                "unprocessed_count": unprocessed_count,
                "recent_activity_count": recent_count,
                "recent_errors": recent_errors,
                "sync_health": "healthy" if unprocessed_count < 1000 else "warning"
            }
            
            print(f"   未处理记录: {unprocessed_count}")
            print(f"   最近5分钟活动: {recent_count}")
            print(f"   最近错误数: {len(recent_errors)}")
            
            if unprocessed_count > 1000:
                print(f"⚠️  未处理记录过多: {unprocessed_count}")
            
            if recent_errors:
                print("⚠️  发现最近的同步错误:")
                for error in recent_errors[:3]:
                    print(f"     - {error.get('resource_key')}: {error.get('error_message')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 检查同步日志表失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        print("🔍 检查数据新鲜度...")
        
        try:
            # 检查数据库中最新的记录
            latest_db_record = await PanResource.all().order_by('-created_at').first()
            
            if not latest_db_record:
                return {"status": "no_data"}
            
            # 检查Meilisearch中是否有这条最新记录
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {self.api_key}"}
                search_url = f"{self.host}/indexes/{self.index_name}/search"
                search_data = {
                    "q": latest_db_record.resource_key,
                    "filter": f"resource_key = '{latest_db_record.resource_key}'",
                    "limit": 1
                }
                
                async with session.post(search_url, json=search_data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        found_in_meili = len(result.get("hits", [])) > 0
                    else:
                        found_in_meili = False
            
            # 计算数据延迟
            now = datetime.now()
            if latest_db_record.created_at:
                data_age = now - latest_db_record.created_at.replace(tzinfo=None)
                data_age_minutes = data_age.total_seconds() / 60
            else:
                data_age_minutes = 0
            
            result = {
                "latest_db_record": {
                    "resource_key": latest_db_record.resource_key,
                    "title": latest_db_record.title,
                    "created_at": latest_db_record.created_at
                },
                "found_in_meilisearch": found_in_meili,
                "data_age_minutes": data_age_minutes,
                "freshness_status": "fresh" if data_age_minutes < 60 else "stale"
            }
            
            print(f"   最新记录: {latest_db_record.resource_key}")
            print(f"   创建时间: {latest_db_record.created_at}")
            print(f"   在Meilisearch中: {'是' if found_in_meili else '否'}")
            print(f"   数据年龄: {data_age_minutes:.1f} 分钟")
            
            if not found_in_meili:
                print("⚠️  最新记录未在Meilisearch中找到，可能存在同步延迟")
            
            return result
            
        except Exception as e:
            print(f"❌ 检查数据新鲜度失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def check_service_processes(self) -> Dict[str, Any]:
        """检查相关服务进程状态"""
        print("🔍 检查服务进程状态...")
        
        try:
            import subprocess
            
            # 检查PM2进程
            pm2_status = {}
            try:
                result = subprocess.run(['pm2', 'jlist'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    import json
                    pm2_data = json.loads(result.stdout)
                    for process in pm2_data:
                        if 'meilisearch' in process.get('name', '').lower():
                            pm2_status[process['name']] = {
                                'status': process['pm2_env']['status'],
                                'memory': process['monit']['memory'],
                                'cpu': process['monit']['cpu']
                            }
            except Exception as e:
                pm2_status = {"error": str(e)}
            
            # 检查Docker容器
            docker_status = {}
            try:
                result = subprocess.run(['docker', 'ps', '--filter', 'name=meilisearch', '--format', 'json'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and result.stdout.strip():
                    for line in result.stdout.strip().split('\n'):
                        if line:
                            container_info = json.loads(line)
                            docker_status[container_info['Names']] = {
                                'status': container_info['Status'],
                                'image': container_info['Image']
                            }
            except Exception as e:
                docker_status = {"error": str(e)}
            
            result = {
                "pm2_processes": pm2_status,
                "docker_containers": docker_status
            }
            
            print(f"   PM2进程: {len(pm2_status)} 个相关进程")
            print(f"   Docker容器: {len(docker_status)} 个相关容器")
            
            return result
            
        except Exception as e:
            print(f"❌ 检查服务进程失败: {e}")
            return {"status": "error", "error": str(e)}


async def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Meilisearch同步状态实时监控")
    print("=" * 60)
    
    monitor = SyncStatusMonitor()
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 执行各项检查
        tasks_status = await monitor.check_meilisearch_tasks()
        sync_log_status = await monitor.check_sync_log_table()
        freshness_status = await monitor.check_data_freshness()
        service_status = await monitor.check_service_processes()
        
        # 生成总体健康评估
        health_issues = []
        
        if tasks_status.get("status_counts", {}).get("failed", 0) > 10:
            health_issues.append("Meilisearch任务失败过多")
        
        if sync_log_status.get("unprocessed_count", 0) > 1000:
            health_issues.append("同步日志积压过多")
        
        if not freshness_status.get("found_in_meilisearch", True):
            health_issues.append("最新数据未同步")
        
        overall_health = "healthy" if not health_issues else "warning"
        
        # 生成监控报告
        report = {
            "check_time": datetime.now().isoformat(),
            "overall_health": overall_health,
            "health_issues": health_issues,
            "meilisearch_tasks": tasks_status,
            "sync_log_status": sync_log_status,
            "data_freshness": freshness_status,
            "service_processes": service_status
        }
        
        # 保存报告
        report_file = f"sync_monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 监控报告已保存到: {report_file}")
        
        # 输出总体状态
        print("\n" + "=" * 60)
        print("📋 总体状态评估")
        print("=" * 60)
        
        if overall_health == "healthy":
            print("✅ 同步服务运行正常")
        else:
            print("⚠️  发现以下问题:")
            for issue in health_issues:
                print(f"   - {issue}")
        
        print("\n🔧 建议的检查步骤:")
        print("1. 定期运行此监控脚本")
        print("2. 如发现问题，运行 python check_migration_status.py 进行详细检查")
        print("3. 如发现缺失数据，运行 python find_missing_data.py 查找具体缺失记录")
        print("4. 必要时重启同步服务或运行完整同步")
        
    except Exception as e:
        print(f"❌ 监控过程中发生错误: {e}")
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
