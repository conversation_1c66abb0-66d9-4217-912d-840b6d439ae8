import asyncio
import aiohttp
import logging
import time
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
from app.utils.config import settings

logger = logging.getLogger(__name__)


class AsyncMeiliSearchService:
    """异步 Meilisearch 服务，支持连接池和批处理操作"""

    def __init__(self):
        # 从配置加载Meilisearch参数
        meili_conf = settings.get("meilisearch", {})
        self.host = meili_conf.get("host", "http://127.0.0.1:7700")
        self.api_key = meili_conf.get("api_key", "masterKey")
        self.index_name = meili_conf.get("index_name", "resources")
        self.session: Optional[aiohttp.ClientSession] = None
        self._session_lock = asyncio.Lock()

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建 HTTP 会话"""
        if self.session is None or self.session.closed:
            async with self._session_lock:
                if self.session is None or self.session.closed:
                    connector = aiohttp.TCPConnector(
                        limit=100,  # 连接池大小
                        limit_per_host=30,  # 每个主机的连接数
                        ttl_dns_cache=300,  # DNS缓存时间
                        use_dns_cache=True,
                        keepalive_timeout=30,
                        enable_cleanup_closed=True,
                    )
                    timeout = aiohttp.ClientTimeout(total=30, connect=10)
                    headers = {}
                    if self.api_key:
                        headers["Authorization"] = f"Bearer {self.api_key}"

                    self.session = aiohttp.ClientSession(
                        connector=connector, timeout=timeout, headers=headers
                    )
        return self.session

    async def close(self):
        """关闭 HTTP 会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def search(self, query: str, limit: int = 2000) -> List[str]:
        """
        使用Meilisearch进行检索，返回候选资源ID列表
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/indexes/{self.index_name}/search"
            data = {
                "q": query,
                "limit": limit,
                "attributesToRetrieve": ["resource_key"],
            }

            async with session.post(url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return [hit["resource_key"] for hit in result.get("hits", [])]
                else:
                    logger.error(f"Meilisearch 搜索失败: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Meilisearch 搜索异常: {e}", exc_info=True)
            return []

    async def add_documents(self, docs: List[Dict[str, Any]]) -> Optional[Dict]:
        """
        异步批量新增或更新资源到索引
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/indexes/{self.index_name}/documents"

            async with session.post(url, json=docs) as response:
                if response.status in [200, 202]:
                    result = await response.json()
                    logger.info(
                        f"成功提交 {len(docs)} 个文档到 Meilisearch，任务ID: {result.get('taskUid')}"
                    )
                    return result
                else:
                    error_text = await response.text()
                    logger.error(
                        f"Meilisearch 添加文档失败: {response.status}, {error_text}"
                    )
                    return None
        except Exception as e:
            logger.error(f"Meilisearch 添加文档异常: {e}", exc_info=True)
            return None

    async def delete_documents(self, resource_keys: List[str]) -> Optional[Dict]:
        """
        异步批量删除指定资源
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/indexes/{self.index_name}/documents/delete-batch"

            async with session.post(url, json=resource_keys) as response:
                if response.status in [200, 202]:
                    result = await response.json()
                    logger.info(
                        f"成功删除 {len(resource_keys)} 个文档，任务ID: {result.get('taskUid')}"
                    )
                    return result
                else:
                    error_text = await response.text()
                    logger.error(
                        f"Meilisearch 删除文档失败: {response.status}, {error_text}"
                    )
                    return None
        except Exception as e:
            logger.error(f"Meilisearch 删除文档异常: {e}", exc_info=True)
            return None

    async def delete_document(self, resource_key: str) -> Optional[Dict]:
        """
        异步删除单个文档
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/indexes/{self.index_name}/documents/{resource_key}"

            async with session.delete(url) as response:
                if response.status in [200, 202]:
                    result = await response.json()
                    logger.info(
                        f"成功删除文档 {resource_key}，任务ID: {result.get('taskUid')}"
                    )
                    return result
                else:
                    error_text = await response.text()
                    logger.error(
                        f"Meilisearch 删除文档失败: {response.status}, {error_text}"
                    )
                    return None
        except Exception as e:
            logger.error(f"Meilisearch 删除文档异常: {e}", exc_info=True)
            return None

    async def get_task_status(self, task_uid: int) -> Optional[Dict]:
        """
        获取任务状态
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/tasks/{task_uid}"

            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"获取任务状态失败: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取任务状态异常: {e}", exc_info=True)
            return None

    async def add_documents_chunked(
        self, docs: List[Dict[str, Any]], chunk_size: int = 1000
    ) -> List[Optional[Dict]]:
        """
        分块批量添加文档 - 针对大批量操作优化
        """
        results = []
        total_docs = len(docs)

        logger.info(f"🚀 开始分块处理: {total_docs} 个文档，每块 {chunk_size} 个")

        for i in range(0, total_docs, chunk_size):
            chunk = docs[i : i + chunk_size]
            chunk_num = i // chunk_size + 1
            total_chunks = (total_docs + chunk_size - 1) // chunk_size

            logger.info(f"📦 处理第 {chunk_num}/{total_chunks} 块: {len(chunk)} 个文档")

            try:
                result = await self.add_documents(chunk)
                results.append(result)

                # 在块之间添加短暂延迟，避免过载
                if i + chunk_size < total_docs:
                    await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"处理第 {chunk_num} 块时出错: {e}")
                results.append(None)

        successful_chunks = sum(1 for r in results if r is not None)
        logger.info(f"✅ 分块处理完成: {successful_chunks}/{len(results)} 块成功")

        return results

    async def wait_for_task(self, task_uid: int, timeout: int = 300) -> bool:
        """
        等待任务完成
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                status = await self.get_task_status(task_uid)
                if not status:
                    await asyncio.sleep(5)
                    continue

                task_status = status.get("status")

                if task_status == "succeeded":
                    logger.info(f"✅ 任务 {task_uid} 完成成功")
                    return True
                elif task_status == "failed":
                    error = status.get("error", {})
                    logger.error(f"❌ 任务 {task_uid} 失败: {error}")
                    return False
                elif task_status in ["enqueued", "processing"]:
                    logger.debug(f"⏳ 任务 {task_uid} 状态: {task_status}")
                    await asyncio.sleep(5)
                else:
                    logger.warning(f"⚠️ 任务 {task_uid} 未知状态: {task_status}")
                    await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"检查任务状态时出错: {e}")
                await asyncio.sleep(5)

        logger.warning(f"⏰ 任务 {task_uid} 等待超时 ({timeout}s)")
        return False

    async def get_index_stats(self) -> Optional[Dict]:
        """
        获取索引统计信息
        """
        try:
            session = await self._get_session()
            url = f"{self.host}/indexes/{self.index_name}/stats"

            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"获取索引统计失败: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取索引统计异常: {e}", exc_info=True)
            return None


# 创建异步服务实例
async_meilisearch_service = AsyncMeiliSearchService()


@asynccontextmanager
async def meilisearch_session():
    """Meilisearch 会话上下文管理器"""
    try:
        yield async_meilisearch_service
    finally:
        # 在应用关闭时清理会话
        pass  # 会话会在应用关闭时统一清理
