{"apps": [{"name": "pan-so-api", "script": "python3", "args": "-m app.main", "cwd": "/root/pan-so-backend", "instances": 1, "autorestart": true, "watch": false, "max_memory_restart": "800M", "log_date_format": "YYYY-MM-DD HH:mm:ss", "restart_delay": 3000, "env": {"PYTHONPATH": "/root/pan-so-backend"}}, {"name": "telegram-monitor", "script": "run_telegram_monitor.py", "interpreter": "python3", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "aisoua-crawler", "script": "python3", "args": "-m app.tasks.aisoua_crawler_runner", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "database-backup", "script": "backup_database.sh", "cwd": "/root/pan-so-backend", "interpreter": "bash", "cron_restart": "0 3 */3 * *", "autorestart": false, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "celery-beat", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app beat -l info --pidfile=./celery-beat.pid", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-kdocs", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -P gevent -c 1 -Q kdocs_queue -n kdocs_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-<PERSON><PERSON><PERSON><PERSON>", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -P gevent -c 1 -Q xuebapan_queue -n xuebapan_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-submission", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 2 -Q submission_queue -n submission_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": false, "restart_delay": 5000, "max_memory_restart": "800M", "log_date_format": "YYYY-MM-DD HH:mm:ss", "cron_restart": "0 1 * * *"}, {"name": "worker-submission-stopper", "script": "pm2", "args": "stop worker-submission", "cwd": "/root/pan-so-backend", "autorestart": false, "cron_restart": "0 7 * * *", "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-<PERSON><PERSON><PERSON>", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 1 -Q duanju_queue -n duanju_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-seo", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 1 -Q seo_queue -n seo_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "meilisearch-scheduler", "script": "python3", "args": "-m app.tasks.scheduled_meilisearch_sync", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "max_memory_restart": "512M", "log_date_format": "YYYY-MM-DD HH:mm:ss", "env": {"PYTHONPATH": "/root/pan-so-backend"}}]}