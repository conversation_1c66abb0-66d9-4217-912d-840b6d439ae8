import httpx
import logging
import asyncio
from typing import List, Dict, Any, Optional
import re
import time
from urllib.parse import urljoin, quote
from functools import wraps
import lxml.html
from cachetools import TTLCache
from bs4 import BeautifulSoup, SoupStrainer
from app.utils.config import settings
from app.utils.common import parse_publish_time
from curl_cffi import requests

logger = logging.getLogger("panku8-crawler")
logger.setLevel(logging.INFO)

# 从配置文件获取爬虫配置
CRAWLER_CONF = settings.get("panku8_crawler", {})
BASE_URL = CRAWLER_CONF.get("base_url", "https://www.panku8.com")
TIMEOUT = CRAWLER_CONF.get("timeout", 10)  # 请求超时时间
MAX_RETRIES = CRAWLER_CONF.get("max_retries", 3)  # 最大重试次数
RETRY_DELAY = CRAWLER_CONF.get("retry_delay", 1.0)  # 重试延迟基础时间
CONCURRENT_LIMIT = CRAWLER_CONF.get("concurrent_limit", 6)  # 并发限制
USE_FINGERPRINT = CRAWLER_CONF.get("use_fingerprint", True)  # 是否使用浏览器指纹
VIP_COOKIE = CRAWLER_CONF.get("vip_cookie")

# 缓存配置
CACHE_CONF = settings.get("cache", {})
SEARCH_CACHE_SIZE = CACHE_CONF.get("resource_cache", {}).get("maxsize", 1000)
SEARCH_CACHE_TTL = CACHE_CONF.get("resource_cache", {}).get("ttl", 3600)
LINK_CACHE_SIZE = CACHE_CONF.get("resource_cache", {}).get("maxsize", 1000)
LINK_CACHE_TTL = CACHE_CONF.get("resource_cache", {}).get("ttl", 7200)


def timing_decorator(func):
    """装饰器：用于记录函数执行时间，仅在INFO级别记录"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"执行 {func.__name__} 用时: {elapsed_time:.4f}秒")
        return result

    return wrapper


class Panku8Crawler:
    """爬取panku8.com网站的网盘资源数据"""

    def __init__(self):
        self.base_url = BASE_URL
        # 更新为实际有效的请求头
        self.headers = {
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-language": "zh-CN,zh;q=0.9",
            "accept-encoding": "gzip, deflate, br, zstd",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "same-origin",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "referer": self.base_url,
            # 注意：cookie应该在请求时传入，而不是硬编码
        }
        # 优化缓存大小和过期时间
        self.search_cache = TTLCache(maxsize=SEARCH_CACHE_SIZE, ttl=SEARCH_CACHE_TTL)
        self.link_cache = TTLCache(maxsize=LINK_CACHE_SIZE, ttl=LINK_CACHE_TTL)
        self.status_cache = TTLCache(maxsize=SEARCH_CACHE_SIZE, ttl=SEARCH_CACHE_TTL)
        self.timing_stats = {}
        self.share_link_headers = {
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "referer": "https://panku8.com/s/%E5%8F%A3%E5%8F%A3",
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "document",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        }
        self.search_pan_types = CRAWLER_CONF.get("search_pan_type", [1, 2, 3, 4])

        # 使用标准httpx客户端（当不使用指纹浏览器时）
        self.client = None
        if not USE_FINGERPRINT:
            self.client = httpx.AsyncClient(
                timeout=TIMEOUT,
                follow_redirects=True,
                limits=httpx.Limits(max_keepalive_connections=10, max_connections=20),
                http2=True,
                verify=False,
            )

        self.request_semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)
        logger.info(f"已初始化爬虫HTTP客户端，使用浏览器指纹: {USE_FINGERPRINT}")

    async def initialize(self):
        """初始化HTTP客户端会话"""
        if not USE_FINGERPRINT and self.client is None:
            self.client = httpx.AsyncClient(
                timeout=TIMEOUT,
                follow_redirects=True,
                limits=httpx.Limits(max_keepalive_connections=10, max_connections=20),
                http2=True,
                verify=False,
            )
            logger.info("已初始化标准httpx客户端")

    async def close(self):
        """关闭HTTP客户端连接池"""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("已关闭爬虫HTTP客户端会话")

    def _get_browser_from_headers(self, headers: Dict[str, str]) -> str:
        """从headers中提取浏览器信息

        Args:
            headers: 请求头字典

        Returns:
            浏览器标识字符串，如 "chrome110", "chrome120", "safari16" 等
        """
        # 默认使用 safari16 (匹配iPhone UA)
        default_browser = "safari16"

        if not headers:
            return default_browser

        # 从 User-Agent 中提取浏览器信息
        user_agent = headers.get("user-agent", "").lower()

        # 优先检查Safari移动版本
        if "iphone" in user_agent and "safari" in user_agent:
            safari_match = re.search(r"version/(\d+)", user_agent)
            if safari_match:
                version = safari_match.group(1)
                if int(version) >= 16:
                    return "safari16"
                elif int(version) >= 15:
                    return "safari15"
                else:
                    return "safari16"  # 默认使用最新版本
            return "safari16"  # 无法确定版本时默认使用safari16

        # 检查Edge/Chrome
        if "edg/" in user_agent:
            edge_match = re.search(r"edg/(\d+)", user_agent)
            if edge_match:
                version = edge_match.group(1)
                if int(version) >= 110:
                    return "chrome110"  # Edge基于Chromium，使用chrome指纹
                else:
                    return "chrome100"

        # 提取 Chrome 版本
        chrome_match = re.search(r"chrome/(\d+)", user_agent)
        if chrome_match:
            version = chrome_match.group(1)
            # 根据版本号选择合适的浏览器指纹
            if int(version) >= 120:
                return "chrome120"
            elif int(version) >= 110:
                return "chrome110"
            elif int(version) >= 100:
                return "chrome100"
            elif int(version) >= 90:
                return "chrome90"

        # 提取 Firefox 版本
        firefox_match = re.search(r"firefox/(\d+)", user_agent)
        if firefox_match:
            version = firefox_match.group(1)
            if int(version) >= 120:
                return "firefox120"
            elif int(version) >= 110:
                return "firefox110"
            elif int(version) >= 100:
                return "firefox100"

        return default_browser

    async def _make_request(self, method, url, **kwargs):
        """统一处理HTTP请求，使用信号量限制并发，根据配置选择请求方式"""
        async with self.request_semaphore:
            start_time = time.time()
            headers = kwargs.get("headers", self.headers)
            _use_fixed_headers = kwargs.pop("_use_fixed_headers", False)

            # 处理请求URL，添加必要的参数
            if "://" not in url:
                url = f"https://{url}" if not url.startswith("http") else url

            # 添加必要的Referer头
            if "referer" not in {k.lower(): v for k, v in headers.items()}:
                domain = re.search(r"https?://([^/]+)", url)
                if domain:
                    headers["referer"] = f"https://{domain.group(1)}"

            # 确保Host头部与请求域名一致
            domain = re.search(r"https?://([^/]+)", url)
            if domain:
                headers["host"] = domain.group(1)

            logger.info(f"请求头: {headers}")
            # 当前采用的模式
            mode = "curl_cffi" if USE_FINGERPRINT else "httpx"
            logger.info(f"当前采用的模式: {mode}")

            # 重试逻辑
            for attempt in range(MAX_RETRIES + 1):
                try:
                    if USE_FINGERPRINT:
                        # 使用curl_cffi发送请求（浏览器指纹伪装）
                        browser = self._get_browser_from_headers(headers)
                        logger.debug(f"使用浏览器指纹: {browser}")

                        # 处理不同的HTTP方法
                        if method.lower() == "get":
                            response = requests.get(
                                url,
                                headers=headers,
                                impersonate=browser,
                                timeout=TIMEOUT,
                                verify=False,
                                max_redirects=5,
                            )
                        elif method.lower() == "post":
                            data = kwargs.get("data")
                            json_data = kwargs.get("json")
                            if json_data:
                                logger.info(f"POST JSON数据: {json_data}")
                                response = requests.post(
                                    url,
                                    headers=headers,
                                    json=json_data,
                                    impersonate=browser,
                                    timeout=TIMEOUT,
                                    verify=False,
                                    max_redirects=5,
                                )
                            else:
                                logger.info(f"POST 表单数据: {data}")
                                response = requests.post(
                                    url,
                                    headers=headers,
                                    data=data,
                                    impersonate=browser,
                                    timeout=TIMEOUT,
                                    verify=False,
                                    max_redirects=5,
                                )
                        else:
                            raise ValueError(f"不支持的HTTP方法: {method}")

                        # 转换为httpx.Response格式以保持API一致性
                        result = httpx.Response(
                            status_code=response.status_code,
                            headers=response.headers,
                            content=response.content,
                            request=httpx.Request(method, url),
                        )
                        logger.info(
                            f"[curl_cffi] 获取响应成功，状态码: {result.status_code}, 内容长度: {len(result.content)}"
                        )

                        # 记录响应内容的前200个字符(调试用)
                        content_preview = (
                            result.text[:200]
                            if hasattr(result, "text")
                            else "无法获取文本内容"
                        )
                        logger.info(f"[curl_cffi] 响应内容预览: {content_preview}...")
                    else:
                        # 使用标准httpx客户端
                        if self.client is None:
                            await self.initialize()

                        # 移除curl_cffi特有的参数
                        kwargs_copy = kwargs.copy()
                        if "impersonate" in kwargs_copy:
                            del kwargs_copy["impersonate"]
                        if "max_redirects" in kwargs_copy:
                            del kwargs_copy["max_redirects"]

                        # 如果不是使用固定头，则走之前的标准化header逻辑
                        if not _use_fixed_headers:
                            # 在httpx模式下使用固定格式的headers
                            user_agent = headers.get("user-agent") or headers.get(
                                "User-Agent"
                            )
                            fixed_headers = {
                                "User-Agent": user_agent
                                or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                                "Referer": self.base_url,
                                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                            }

                            # 如果URL中包含域名，则更新Referer
                            domain = re.search(r"https?://([^/]+)", url)
                            if domain:
                                fixed_headers["Referer"] = f"https://{domain.group(1)}"

                            # 更新headers到kwargs
                            kwargs_copy["headers"] = fixed_headers
                        else:
                            # 直接使用传入的headers
                            kwargs_copy["headers"] = headers

                        # 记录请求数据
                        if method.lower() == "post" and "json" in kwargs_copy:
                            logger.info(f"POST JSON数据: {kwargs_copy['json']}")
                        elif method.lower() == "post" and "data" in kwargs_copy:
                            logger.info(f"POST 表单数据: {kwargs_copy['data']}")

                        logger.info(f"[httpx] 开始发送请求: {url}")
                        result = await getattr(self.client, method.lower())(
                            url, **kwargs_copy
                        )
                        logger.info(
                            f"[httpx] 获取响应成功，状态码: {result.status_code}, 内容长度: {len(result.content)}"
                        )

                    elapsed = time.time() - start_time
                    logger.info(f"请求完成: {url}")
                    logger.info(f"状态码: {result.status_code}")
                    logger.info(f"请求耗时: {elapsed:.2f}秒")

                    return result

                except Exception as e:
                    if attempt < MAX_RETRIES:
                        delay = RETRY_DELAY * (attempt + 1)  # 指数退避
                        logger.warning(
                            f"请求失败，{delay}秒后重试 ({attempt+1}/{MAX_RETRIES}): {url}, 错误: {str(e)}"
                        )
                        await asyncio.sleep(delay)
                        continue
                    logger.error(f"请求失败，已达最大重试次数: {url}, 错误: {str(e)}")
                    raise

    @timing_decorator
    async def get_share_link(
        self, detail_url: str, user_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """获取分享链接信息 - 优化实现方式

        Args:
            detail_url: 详情页URL
            user_headers: 用户的请求头信息(此方法中被忽略)
        """
        # 检查缓存
        if detail_url in self.link_cache:
            cached_result = self.link_cache[detail_url]
            cached_result["from_cache"] = True
            logger.info(f"使用缓存的分享链接结果: {detail_url}")
            return cached_result

        try:
            start_time = time.time()
            # 使用固定的请求头
            request_headers = self.share_link_headers.copy()
            if VIP_COOKIE:
                request_headers["cookie"] = VIP_COOKIE

            # 使用统一的请求方法
            request_start = time.time()
            logger.info(f"开始获取分享链接: {detail_url}")
            response = await self._make_request(
                "get",
                detail_url,
                headers=request_headers,
                timeout=TIMEOUT,
                _use_fixed_headers=True,
            )
            request_time = time.time() - request_start

            logger.info(
                f'响应状态码: {response.status_code}, 内容类型: {response.headers.get("content-type", "unknown")}'
            )
            logger.info(f"请求用时: {request_time:.4f}秒")

            if response.status_code != 200:
                logger.error(f"获取分享链接失败: {response.status_code}")
                # 记录详细信息以便调试
                logger.debug(f"请求URL: {detail_url}")
                logger.debug(f"请求头: {request_headers}")
                logger.debug(f"响应内容: {response.text[:500]}...")
                return {
                    "status": "error",
                    "message": f"请求失败: {response.status_code}",
                }

            # 使用SoupStrainer优化解析，只解析包含分享链接的元素
            parse_start = time.time()
            logger.info("开始解析响应内容查找分享链接")

            share_strainer = SoupStrainer(class_="text-break text-decoration-underline")
            soup = BeautifulSoup(response.text, "lxml", parse_only=share_strainer)
            logger.info(f"解析后的soup元素: {soup}")

            # 获取分享链接
            share_url = None
            link_element = soup.find(class_="text-break text-decoration-underline")
            if link_element and link_element.get("href"):
                share_url = link_element.get("href")
                logger.info(f"通过BeautifulSoup找到分享链接: {share_url}")

            # 如果BeautifulSoup解析失败，再尝试使用正则表达式
            if not share_url:
                logger.info("BeautifulSoup未找到链接，尝试使用正则表达式")
                pattern = r'<a[^>]*?class="text-break text-decoration-underline"[^>]*?href="([^"]*?)"'
                match = re.search(pattern, response.text)
                if match:
                    share_url = match.group(1)
                    logger.info(f"通过正则表达式找到分享链接: {share_url}")

            # 最后尝试使用lxml解析
            if not share_url:
                logger.info("正则表达式未找到链接，尝试使用lxml解析")
                try:
                    tree = lxml.html.fromstring(response.text)
                    link_element = tree.xpath(
                        '//a[contains(@class, "-textbreak") and contains(@class, "text-decoration-underline")]'
                    )
                    if (
                        link_element
                        and len(link_element) > 0
                        and link_element[0].get("href")
                    ):
                        share_url = link_element[0].get("href")
                        logger.info(f"通过lxml找到分享链接: {share_url}")
                    else:
                        logger.info("lxml未找到匹配的链接元素")
                except Exception as e:
                    logger.error(f"lxml解析异常: {str(e)}")

            parse_time = time.time() - parse_start
            logger.info(
                f"解析分享链接用时: {parse_time:.4f}秒, 是否找到链接: {share_url is not None}"
            )

            # 如果都没有找到分享链接，再尝试其他方式寻找
            if not share_url:
                # 记录页面内容的一部分，帮助调试
                content_sample = (
                    response.text[:500] + "..." + response.text[-500:]
                    if len(response.text) > 1000
                    else response.text
                )
                # logger.info(f"页面内容示例:\n{response.text}")

                # 查找所有链接，也许能找到线索
                all_links = re.findall(r'<a[^>]*?href="([^"]*?)"[^>]*?>', response.text)
                if all_links:
                    logger.info(f"页面中的所有链接: {all_links[:10]}...")

                result = {
                    "status": "error",
                    "message": "未找到分享链接",
                    "from_cache": False,
                }
                self.link_cache[detail_url] = result
                return result

            # 判断链接类型
            pan_type = 0
            if "pan.baidu.com" in share_url:
                pan_type = 1
            elif "pan.quark.cn" in share_url:
                pan_type = 2

            # 返回统一的结果格式
            total_time = time.time() - start_time
            result = {
                "status": "success",
                "share_url": share_url,
                "pan_type": pan_type,
                "message": "获取分享链接成功",
                "from_cache": False,
                "time_stats": {
                    "total_time": f"{total_time:.4f}秒",
                    "request_time": f"{request_time:.4f}秒",
                    "parse_time": f"{parse_time:.4f}秒",
                },
            }
            # 缓存结果（移除时间统计信息以减少缓存大小）
            cache_result = {k: v for k, v in result.items() if k != "time_stats"}
            self.link_cache[detail_url] = cache_result
            logger.info(f"已缓存分享链接结果: {detail_url}")
            return result

        except Exception as e:
            logger.error(f"获取分享链接异常: {str(e)}")
            return {
                "status": "error",
                "message": f"获取异常: {str(e)}",
                "from_cache": False,
            }

    @timing_decorator
    async def _search_single_type(
        self,
        keyword: str,
        pan_type: Optional[int],
        page: int,
        limit: int,
        headers: Optional[Dict[str, str]],
    ) -> Dict[str, Any]:
        """按指定类型搜索网盘资源（内部方法）"""
        # 构建缓存键
        cache_key = f"{keyword}_{pan_type}_{page}_{limit}"

        # 检查缓存
        if cache_key in self.search_cache:
            logger.info(f"使用缓存的搜索结果: {keyword}, 网盘类型: {pan_type}")
            cached_result = self.search_cache[cache_key]
            # 为缓存结果添加缓存标记
            cached_result["from_cache"] = True
            return cached_result

        try:
            start_time = time.time()
            logger.info(f"开始搜索: {keyword}, 网盘类型: {pan_type}, 第{page}页")

            # 构建URL格式: /s/关键词/网盘类型?page=页码
            url = f"{self.base_url}/s/{quote(keyword)}"

            # 无论pan_type为何值（包括0），都拼接到url
            if pan_type is not None:
                url += f"/{pan_type}"
            url += f"/{page}"
            logger.info(f"查询的url: {url}")
            # 使用连接池进行请求
            request_headers = self.headers.copy()

            # 设置请求头：先使用默认头部，然后根据优先级用用户头部覆盖
            if headers:
                # 提取关键的浏览器指纹相关头部
                important_headers = [
                    "user-agent",
                    "accept",
                    "accept-language",
                    "accept-encoding",
                    "sec-ch-ua",
                    "sec-ch-ua-mobile",
                    "sec-ch-ua-platform",
                    "upgrade-insecure-requests",
                    "sec-fetch-site",
                    "sec-fetch-mode",
                    "sec-fetch-user",
                    "sec-fetch-dest",
                ]

                # 遍历并应用重要的浏览器指纹相关头部
                for header in important_headers:
                    header_lower = header.lower()
                    for key, value in headers.items():
                        if key.lower() == header_lower:
                            request_headers[key] = value
                            break

            request_start = time.time()
            response = await self._make_request("get", url, headers=request_headers)
            request_time = time.time() - request_start
            logger.info(
                f'响应状态码: {response.status_code}, 内容类型: {response.headers.get("content-type", "unknown")}'
            )
            logger.info(f"请求用时: {request_time:.4f}秒")

            if response.status_code != 200:
                logger.error(f"搜索请求失败: {response.status_code}")
                return {
                    "status": "error",
                    "message": f"搜索请求失败: {response.status_code}",
                    "results": [],
                    "from_cache": False,
                }

            # 提取搜索结果总数
            count_start = time.time()
            total_count = await self._extract_total_count(response.text)
            count_time = time.time() - count_start
            logger.info(f"搜索总数: {total_count}, 提取用时: {count_time:.4f}秒")

            # 解析搜索结果
            parse_start = time.time()
            results = await self._parse_search_results(response.text, limit, pan_type)
            parse_time = time.time() - parse_start
            logger.info(f"解析搜索结果用时: {parse_time:.4f}秒")

            # 计算总时间
            total_time = time.time() - start_time
            # 获取基本结果后直接返回，不进行额外处理
            result_data = {
                "status": "success",
                "message": "搜索成功",
                "keyword": keyword,
                "page": page,
                "results": results,
                "total": len(results),
                "totals": total_count,
                "from_cache": False,
                "timing_stats": {
                    "total_time": f"{total_time:.4f}秒",
                    "request_time": f"{request_time:.4f}秒",
                    "parse_time": f"{parse_time:.4f}秒",
                    "count_time": f"{count_time:.4f}秒",
                },
            }
            logger.info(f"panku8搜索条数: {len(results)}")
            # 存入缓存（移除时间统计信息以减少缓存大小）
            cache_result = {k: v for k, v in result_data.items() if k != "timing_stats"}
            self.search_cache[cache_key] = cache_result
            return result_data

        except Exception as e:
            logger.error(f"搜索过程中发生异常: {str(e)}")
            return {
                "status": "error",
                "message": f"搜索异常: {str(e)}",
                "results": [],
                "from_cache": False,
            }

    @timing_decorator
    async def search(
        self,
        keyword: str,
        pan_type: Optional[int] = None,
        page: int = 1,
        limit: int = 10,
        user_agent: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """搜索网盘资源

        参数:
            keyword: 搜索关键词
            pan_type: 网盘类型 (0=配置的所有网盘, 1=百度网盘, 2=夸克网盘, 3=阿里网盘, 4=迅雷网盘).
                      如果指定类型，会检查是否在配置允许的范围内。
            page: 页码
            limit: 每页结果数
            user_agent: 用户的User-Agent(向后兼容)
            headers: 用户的完整请求头，优先级高于user_agent

        返回:
            搜索结果
        """
        # pan_type=0 或 None 表示搜索所有已配置的网盘类型
        if pan_type is None or pan_type == 0:
            logger.info(f"开始搜索所有已配置的网盘类型: {self.search_pan_types}")
            tasks = [
                self._search_single_type(keyword, p_type, page, limit, headers)
                for p_type in self.search_pan_types
            ]
            search_results = await asyncio.gather(*tasks)

            all_results = []
            total_count = 0
            for result in search_results:
                if result.get("status") == "success":
                    all_results.extend(result.get("results", []))
                    total_count += result.get("totals", 0)

            # 基于detail_url去重
            unique_results = list(
                {item["detail_url"]: item for item in all_results}.values()
            )

            # 按更新时间降序排序
            def sort_key(item):
                return item.get("updated_at", "")

            unique_results.sort(key=sort_key, reverse=True)

            final_results = unique_results[:limit]

            return {
                "status": "success",
                "message": "多网盘组合搜索成功",
                "keyword": keyword,
                "page": page,
                "results": final_results,
                "total": len(final_results),
                "totals": total_count,  # 注意：此总数是各网盘搜索结果总数的累加，可能不精确
                "from_cache": False,  # 组合搜索不使用缓存
            }
        # 搜索指定的单个网盘类型
        else:
            if pan_type not in self.search_pan_types:
                logger.warning(
                    f"请求的网盘类型 {pan_type} 不在配置的支持范围内 {self.search_pan_types}"
                )
                return {
                    "status": "error",
                    "message": f"不支持的网盘类型: {pan_type}",
                    "results": [],
                    "from_cache": False,
                    "total": 0,
                    "totals": 0,
                }

            logger.info(f"开始搜索指定网盘类型: {pan_type}")
            # 为了向后兼容user_agent参数
            if not headers and user_agent:
                headers = {"User-Agent": user_agent}

            # 调用单个网盘搜索并获取结果
            single_result = await self._search_single_type(
                keyword, pan_type, page, limit, headers
            )
            return single_result

    @timing_decorator
    async def _parse_search_results(
        self, html_content: str, limit: int, pan_type: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """解析搜索结果HTML内容 - 优化实现方式"""
        results = []
        try:
            # 使用SoupStrainer过滤只解析必要的HTML部分，提高解析效率
            resource_strainer = SoupStrainer(class_="resource-item")
            soup = BeautifulSoup(html_content, "lxml", parse_only=resource_strainer)

            # 网盘类型映射
            pan_type_map = {
                "百度网盘": 1,
                "夸克网盘": 2,
                "阿里云盘": 3,
                "迅雷网盘": 4,
            }

            # 查找所有搜索结果项
            result_items = soup.find_all(class_="resource-item")

            if result_items:
                # 使用BeautifulSoup解析
                for item in result_items[:limit]:
                    try:
                        # 提取链接
                        href = item.get("href", "")
                        if href and not href.startswith("http"):
                            href = urljoin(self.base_url, href)

                        # 提取资源ID
                        resource_id = ""
                        if href:
                            # 匹配 /res/a01 或 /res/a02 后面的ID，支持特殊符号
                            match = re.search(r"/res/a\d{2}([a-zA-Z0-9_-]+)", href)
                            if match:
                                resource_id = match.group(1)

                        # pan_type为0时，自动解析class=text-success的网盘类型
                        item_pan_type = pan_type
                        if pan_type == 0:
                            # 查找class=text-success的span文本
                            pan_type_text = None
                            text_success_span = item.select_one("span.text-success")
                            if text_success_span:
                                pan_type_text = text_success_span.get_text(strip=True)
                            if pan_type_text:
                                # 如果网盘类型不存在于pan_type_map中，则跳过此条数据
                                if pan_type_text not in pan_type_map:
                                    logger.info(f"跳过未知网盘类型: {pan_type_text}")
                                    continue
                                item_pan_type = pan_type_map.get(pan_type_text)
                            else:
                                item_pan_type = 0
                        # 否则直接用传入的pan_type
                        # 根据网盘类型拼接original_url
                        original_url = ""
                        if resource_id:
                            if item_pan_type == 1:  # 百度网盘
                                # original_url = f"https://pan.baidu.com/s/{resource_id}"
                                original_url = (
                                    ""  # 百度网盘需要使用get_share_link方法获取
                                )
                            elif item_pan_type == 2:  # 夸克网盘
                                original_url = f"https://pan.quark.cn/s/{resource_id}"
                            elif item_pan_type == 3:  # 阿里云盘
                                original_url = f"https://www.alipan.com/s/{resource_id}"
                            elif item_pan_type == 4:  # 迅雷网盘
                                original_url = (
                                    ""  # 迅雷网盘需要使用get_share_link方法获取
                                )

                        # 提取标题
                        title_element = item.select_one("h2")
                        title = (
                            title_element.text.strip() if title_element else "未知标题"
                        )

                        # 提取更新时间
                        update_time = ""
                        refresh_span = item.select_one("i.icon-refresh + span")
                        if refresh_span and refresh_span.text:
                            update_time = refresh_span.text.strip()

                        # 提取文本内容  <div class="fs-7 text-secondary mt-1">[<span class="highlight">神印王座</span>][更至161集][WEB-MKV/1.1G][国语配音/中文字幕][4K-2160P]六千年前，魔神皇枫秀与七十二根魔神柱从天而降，所有生物沾染魔神柱散发的气息，立刻会变异成魔族生物，人类...</div>
                        text_content = ""
                        text_content_element = item.select_one(
                            "div.fs-7.text-secondary.mt-1"
                        )
                        if text_content_element:
                            text_content = text_content_element.text.strip()

                        # 使用parse_publish_time处理时间并统一为updated_at字段
                        parsed_time = parse_publish_time(update_time)
                        results.append(
                            {
                                "title": title,
                                "detail_url": href,
                                "resource_id": resource_id,
                                "updated_at": parsed_time,
                                "pan_type": item_pan_type,
                                "original_url": original_url,
                                "text_content": text_content,
                            }
                        )
                    except Exception as e:
                        logger.error(f"解析搜索结果项时发生异常: {str(e)}")
                        continue
            else:
                # 尝试使用lxml直接解析，速度更快
                tree = lxml.html.fromstring(html_content)
                cards = tree.xpath('//a[contains(@class, "resource-item")]')
                logger.info(f"解析到的资源卡片数量: {len(cards)}")
                for card in cards[:limit]:
                    try:
                        # 链接
                        href = card.get("href", "")
                        if href and not href.startswith("http"):
                            href = urljoin(self.base_url, href)
                            logger.info("走逻辑1")

                        # 提取资源ID
                        resource_id = ""
                        if href:
                            # 匹配 /res/a01 或 /res/a02 后面的ID，支持特殊符号
                            match = re.search(r"/res/a\d{2}([a-zA-Z0-9_-]+)", href)
                            logger.info("走逻辑2")
                            if match:
                                resource_id = match.group(1)
                                logger.info("走逻辑3")

                        # pan_type为0时，自动解析class=text-success的网盘类型
                        item_pan_type = pan_type
                        if pan_type == 0:
                            pan_type_text = None
                            text_success_spans = card.xpath(
                                './/span[contains(@class, "text-success")]/text()'
                            )
                            if text_success_spans:
                                pan_type_text = text_success_spans[0].strip()
                            if pan_type_text:
                                # 如果网盘类型不存在于pan_type_map中，则跳过此条数据
                                if pan_type_text not in pan_type_map:
                                    logger.info(f"跳过未知网盘类型: {pan_type_text}")
                                    continue
                                item_pan_type = pan_type_map.get(pan_type_text)
                            else:
                                item_pan_type = 0
                        # 否则直接用传入的pan_type

                        # 根据网盘类型拼接original_url
                        original_url = ""
                        if resource_id:
                            if item_pan_type == 1:  # 百度网盘
                                # original_url = f"https://pan.baidu.com/s/{resource_id}"
                                original_url = (
                                    ""  # 百度网盘需要使用get_share_link方法获取
                                )
                            elif item_pan_type == 2:  # 夸克网盘
                                original_url = f"https://pan.quark.cn/s/{resource_id}"
                            elif item_pan_type == 3:  # 阿里云盘
                                original_url = f"https://www.alipan.com/s/{resource_id}"
                            elif item_pan_type == 4:  # 迅雷网盘
                                original_url = (
                                    ""  # 迅雷网盘需要使用get_share_link方法获取
                                )

                        # 提取标题
                        h2 = card.xpath(".//h2")[0] if card.xpath(".//h2") else None
                        title = ""
                        if h2 is not None:
                            title = "".join(h2.itertext()).strip()

                        # 更新时间（icon-refresh后面的span文本）
                        update_time = ""
                        refresh_span = card.xpath(
                            './/i[contains(@class, "icon-refresh")]/following-sibling::span[1]'
                        )
                        if refresh_span and refresh_span[0].text:
                            update_time = refresh_span[0].text.strip()

                        # 提取文本内容  <div class="fs-7 text-secondary mt-1">[<span class="highlight">神印王座</span>][更至161集][WEB-MKV/1.1G][国语配音/中文字幕][4K-2160P]六千年前，魔神皇枫秀与七十二根魔神柱从天而降，所有生物沾染魔神柱散发的气息，立刻会变异成魔族生物，人类...</div>
                        text_content = ""
                        text_content_element = card.xpath(
                            './/div[contains(@class, "fs-7 text-secondary mt-1")]'
                        )
                        if text_content_element:
                            text_content = text_content_element[0].text.strip()

                        # 使用parse_publish_time处理时间并统一为updated_at字段
                        parsed_time = parse_publish_time(update_time)
                        results.append(
                            {
                                "title": title,
                                "detail_url": href,
                                "resource_id": resource_id,
                                "updated_at": parsed_time,
                                "pan_type": item_pan_type,
                                "original_url": original_url,
                                "text_content": text_content,
                            }
                        )
                    except Exception:
                        continue
        except Exception as e:
            logger.error(f"解析资源卡片失败: {str(e)}")
        return results

    def _extract_datetime_from_element(self, element, title="", is_lxml=False):
        """从元素中提取日期时间

        Args:
            element: lxml元素
            title: 资源标题，仅用于日志记录
            is_lxml: 是否为lxml元素

        Returns:
            提取到的日期时间字符串
        """
        publish_time = ""

        # 获取元素的HTML字符串
        if is_lxml:
            elem_html = lxml.html.tostring(element, encoding="unicode")
        else:
            elem_html = str(element)

        # 方法0：直接在HTML中查找日期时间格式
        date_patterns = [
            r"(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})",  # 2025-05-01 23:23
            r"(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})",  # 2025/05/01 23:23
            r"(\d{4}-\d{2}-\d{2})",  # 2025-05-01
            r"(\d{4}/\d{2}/\d{2})",  # 2025/05/01
        ]

        for pattern in date_patterns:
            date_matches = re.findall(pattern, elem_html)
            if date_matches:
                publish_time = date_matches[0]
                return publish_time

        # 方法1: 通过icon-refresh图标查找时间
        if is_lxml:
            # lxml方式
            # 1.1: 查找refresh图标后的span
            time_elements = element.xpath(
                './/i[contains(@class, "icon-refresh")]/following-sibling::span/text()'
            )
            if time_elements and time_elements[0].strip():
                publish_time = time_elements[0].strip()
                return publish_time

            # 1.2: 查找包含refresh图标的div
            refresh_divs = element.xpath(
                './/div[contains(@class, "align-items-center")]'
            )
            for div in refresh_divs:
                div_html = lxml.html.tostring(div, encoding="unicode")
                if "icon-refresh" in div_html:
                    # 先查找格式化的日期时间
                    for pattern in date_patterns:
                        matches = re.findall(pattern, div_html)
                        if matches:
                            publish_time = matches[0]
                            return publish_time

                    # 尝试获取div中的所有文本
                    div_texts = div.xpath(".//text()")
                    for text in div_texts:
                        text = text.strip()
                        if re.match(r"\d{4}-\d{2}-\d{2}", text):
                            publish_time = text
                            return publish_time
        else:
            # 使用正则表达式替代BeautifulSoup的方式
            # 提取含有icon-refresh和日期时间的部分
            refresh_pattern = r'<i[^>]*class="[^"]*icon-refresh[^"]*"[^>]*>\s*</i>\s*<span[^>]*>(.*?)</span>'
            refresh_match = re.search(refresh_pattern, elem_html)
            if refresh_match:
                refresh_text = refresh_match.group(1).strip()
                if refresh_text:
                    publish_time = refresh_text
                    return publish_time

            # 查找div class="d-flex align-items-center"中的日期
            date_div_pattern = r'<div[^>]*class="[^"]*d-flex[^"]*align-items-center[^"]*"[^>]*>(.*?)</div>'
            date_div_matches = re.findall(date_div_pattern, elem_html)
            for div_html in date_div_matches:
                if "icon-refresh" in div_html:
                    # 尝试直接找日期
                    for pattern in date_patterns:
                        date_matches = re.findall(pattern, div_html)
                        if date_matches:
                            publish_time = date_matches[0]
                            return publish_time

        # 方法2: 使用正则表达式模式匹配
        patterns = [
            # 匹配 <i class="icon-refresh"></i><span>2025-05-01 23:23</span>
            r"icon-refresh[^>]*></i>\s*<span>([^<]+)</span>",
            # 匹配任何形式的 "2025-05-01 23:23"
            r">(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})<",
            # 匹配任何形式的 "2025/05/01 23:23"
            r">(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})<",
            # 匹配 <span>2025-05-01</span>
            r"<span[^>]*>(\d{4}-\d{2}-\d{2})</span>",
        ]

        for pattern in patterns:
            match = re.search(pattern, elem_html)
            if match:
                publish_time = match.group(1).strip()
                return publish_time

        return publish_time

    async def _extract_total_count(self, html_content: str) -> int:
        """从HTML内容中提取搜索结果总数"""
        try:
            # 只使用lxml提取搜索结果总数
            tree = lxml.html.fromstring(html_content)

            # 尝试精确匹配具有特定class的div和span
            spans = tree.xpath(
                '//div[contains(@class, "d-flex") and contains(@class, "align-items-center")]/span[@class="text-danger"]/text()'
            )
            if spans and spans[0].strip().isdigit():
                return int(spans[0].strip())

            # 备用方案：查找任何带有"text-danger"类的span
            spans = tree.xpath('//span[contains(@class, "text-danger")]/text()')
            for span_text in spans:
                if span_text.strip().isdigit():
                    return int(span_text.strip())

            # 最后尝试：查找包含"搜索结果共"文本的div中的数字
            divs = tree.xpath('//div[contains(text(), "搜索结果共")]')
            for div in divs:
                div_text = lxml.html.tostring(div, encoding="unicode", method="text")
                numbers = re.findall(r"\d+", div_text)
                if numbers:
                    return int(numbers[0])

            # 如果都未找到，返回0
            return 0
        except Exception as e:
            logger.error(f"提取搜索结果总数异常: {str(e)}")
            return 0


# 创建单例实例
panku8_crawler = Panku8Crawler()


# 测试诊断
async def run_diagnostic_test():
    """运行诊断测试"""
    try:
        logger.info("开始运行诊断测试...")
        start_time = time.time()

        result = await panku8_crawler.search("龙叔", pan_type=2, limit=30, page=2)
        # 并发获取所有result['results']内的分享链接
        detail_urls = [item["detail_url"] for item in result.get("results", [])]
        share_links = await asyncio.gather(
            *[panku8_crawler.get_share_link(url) for url in detail_urls]
        )
        print(share_links)

        total_time = time.time() - start_time
        logger.info(f"诊断测试完成，总耗时: {total_time:.4f}秒")
        logger.info(f"性能统计: {result.get('timing_stats', {})}")

        return result
    finally:
        # 确保连接池被关闭
        await panku8_crawler.close()
