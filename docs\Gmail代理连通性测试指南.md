# Gmail代理连通性测试指南

## 概述

本指南提供了三个测试工具，用于验证您的代理服务配置是否能够正常访问Gmail SMTP服务。这些工具使用您现有的`proxy_service`配置，无需修改任何现有代码。

## 测试工具说明

### 1. 快速Gmail测试 (`tools/quick_gmail_test.py`)

**用途**: 快速测试Gmail SMTP连通性  
**特点**: 简单直接，适合日常检查

```bash
python tools/quick_gmail_test.py
```

**测试内容**:
- ✅ 直连Gmail SMTP测试（作为对比）
- ✅ 通过代理连接Gmail SMTP
- ✅ STARTTLS加密握手测试
- ✅ 自动结果汇总和建议

### 2. 综合Gmail代理测试 (`tools/test_gmail_proxy_connectivity.py`)

**用途**: 详细的Gmail连通性测试  
**特点**: 功能全面，提供详细诊断信息

```bash
python tools/test_gmail_proxy_connectivity.py
```

**测试模式**:
1. **综合测试** - 完整的连通性检查
2. **仅测试代理获取** - 验证代理服务API
3. **仅测试直连** - 检查本地网络状况

**测试内容**:
- 📋 代理服务配置检查
- 🔗 基础Socket连接测试
- 📧 SMTP协议测试
- 🔒 TLS加密测试
- 📊 详细的错误诊断

### 3. 代理服务测试 (`tools/test_proxy_service.py`)

**用途**: 测试代理服务本身是否正常工作  
**特点**: 专注于代理服务功能验证

```bash
python tools/test_proxy_service.py
```

**测试功能**:
- 🔧 显示代理服务配置
- 🌐 测试代理API连通性
- 📡 测试代理获取功能
- 🔗 测试代理访问Google
- 📊 多代理获取测试

## 使用流程建议

### 第一步：检查代理服务
```bash
python tools/test_proxy_service.py
```
选择 "1. 综合测试" 确保代理服务正常工作。

### 第二步：快速Gmail测试
```bash
python tools/quick_gmail_test.py
```
快速验证Gmail连通性。

### 第三步：详细诊断（如需要）
```bash
python tools/test_gmail_proxy_connectivity.py
```
如果快速测试失败，使用此工具进行详细诊断。

## 配置要求

确保您的 `app/config.yaml` 中有正确的代理服务配置：

```yaml
proxy_service:
  enabled: true
  api_server_address: "http://uu-proxy.com/api/"
  token_id: "NMS3ZW3M3F"
  size: 1
  schemes: "socks5"
  support_https: "true"
  restime_within_ms: 2000
  format: "json"
  retry_count: 1
  timeout: 10
```

## 测试结果解读

### ✅ 成功情况

**快速测试输出示例**:
```
✅ 代理服务已启用
✅ 获取到代理: socks5://user:<EMAIL>:1080
✅ Gmail SMTP连接成功！
🎉 代理可以正常访问Gmail邮件服务

📊 测试结果汇总:
   直连Gmail: ❌ 不可用
   代理连接: ✅ 可用

🎯 建议: 可以在邮件服务中启用代理配置
```

### ❌ 失败情况及解决方案

#### 1. 代理服务未启用
```
❌ 代理服务未启用，请检查配置
```
**解决方案**: 在配置文件中设置 `proxy_service.enabled: true`

#### 2. 无法获取代理
```
❌ 无法获取代理
```
**解决方案**: 
- 检查API服务地址是否正确
- 验证Token ID是否有效
- 确认网络连接正常

#### 3. 代理连接失败
```
❌ Gmail SMTP连接失败: Connection refused
```
**解决方案**:
- 检查代理服务器状态
- 尝试不同的代理协议类型
- 验证代理服务器是否支持SMTP

#### 4. SMTP协议错误
```
✅ 基础连接成功，但SMTP协议可能有问题
```
**解决方案**:
- 检查代理服务器的SMTP支持
- 尝试使用不同端口（587或465）
- 联系代理服务提供商

## 常见问题

### Q: 直连和代理都失败怎么办？
A: 
1. 检查本地网络连接
2. 确认防火墙设置
3. 验证DNS解析是否正常
4. 尝试使用不同的网络环境

### Q: 代理获取成功但连接失败？
A:
1. 代理可能不支持SMTP协议
2. 代理服务器可能有地域限制
3. 尝试获取多个代理进行测试

### Q: 测试成功但实际邮件发送失败？
A:
1. 检查Gmail认证配置
2. 确认应用专用密码设置
3. 验证邮件服务配置是否正确

## 集成到邮件服务

如果测试成功，您可以在邮件服务中启用代理：

```yaml
email:
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-app-password"
  
  proxy:
    enabled: true
    type: "socks5"  # 根据测试结果选择
    host: "127.0.0.1"  # 使用代理服务提供的地址
    port: 1080         # 使用代理服务提供的端口
```

## 自动化测试

您可以将这些测试集成到CI/CD流程中：

```bash
# 在部署脚本中添加
python tools/quick_gmail_test.py
if [ $? -eq 0 ]; then
    echo "Gmail连通性测试通过"
else
    echo "Gmail连通性测试失败，请检查配置"
    exit 1
fi
```

## 监控建议

建议定期运行这些测试以监控代理服务状态：

```bash
# 每小时运行一次快速测试
0 * * * * cd /path/to/project && python tools/quick_gmail_test.py >> /var/log/gmail_test.log 2>&1
```

## 技术支持

如果遇到问题：

1. 查看详细的错误日志
2. 运行综合测试获取诊断信息
3. 检查代理服务提供商的状态页面
4. 联系技术支持并提供测试结果
