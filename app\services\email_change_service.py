"""
邮箱更改服务
"""

from typing import Optional, Dict, Any
from tortoise.transactions import in_transaction
from datetime import datetime, timedelta, timezone
import secrets
import hashlib
import logging

from app.models.user import User, EmailChangeRequest
from app.core.email import email_service

logger = logging.getLogger(__name__)


class EmailChangeService:
    """邮箱更改服务类"""

    @classmethod
    async def request_email_change(
        cls, user: User, new_email: str, password: str, ip_address: Optional[str] = None
    ) -> EmailChangeRequest:
        """请求更改邮箱"""

        # 验证当前密码
        if not user.verify_password(password):
            raise ValueError("密码不正确")

        # 检查新邮箱是否与当前邮箱相同
        if new_email.lower() == user.email.lower():
            raise ValueError("新邮箱不能与当前邮箱相同")

        # 检查新邮箱是否已被其他用户使用
        existing_user = await User.filter(email=new_email).exclude(id=user.id).first()
        if existing_user:
            raise ValueError("该邮箱已被其他用户使用")

        # 检查是否有未完成的邮箱更改请求
        pending_request = await EmailChangeRequest.filter(
            user=user,
            is_verified=False,
            token_expires_at__gt=datetime.now(timezone.utc),
        ).first()

        if pending_request:
            # 如果是相同的新邮箱，重新发送验证邮件
            if pending_request.new_email == new_email:
                await cls._send_verification_email(pending_request)
                return pending_request
            else:
                # 如果是不同的新邮箱，取消之前的请求
                pending_request.is_verified = False
                await pending_request.save()

        async with in_transaction():
            # 生成验证令牌
            verification_token = secrets.token_urlsafe(32)
            token_hash = hashlib.sha256(verification_token.encode()).hexdigest()

            # 创建邮箱更改请求
            change_request = await EmailChangeRequest.create(
                user=user,
                old_email=user.email,
                new_email=new_email,
                verification_token=token_hash,
                token_expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
                ip_address=ip_address,
            )

            # 发送验证邮件
            await cls._send_verification_email(change_request, verification_token)

            logger.info(
                f"用户 {user.username} 请求更改邮箱: {user.email} -> {new_email}"
            )

            return change_request

    @classmethod
    async def verify_email_change(
        cls, token: str, ip_address: Optional[str] = None
    ) -> User:
        """验证邮箱更改"""

        # 计算令牌哈希
        token_hash = hashlib.sha256(token.encode()).hexdigest()

        # 查找邮箱更改请求
        change_request = (
            await EmailChangeRequest.filter(
                verification_token=token_hash,
                is_verified=False,
                token_expires_at__gt=datetime.now(timezone.utc),
            )
            .select_related("user")
            .first()
        )

        if not change_request:
            raise ValueError("验证令牌无效或已过期")

        # 再次检查新邮箱是否已被使用
        existing_user = (
            await User.filter(email=change_request.new_email)
            .exclude(id=change_request.user.id)
            .first()
        )
        if existing_user:
            raise ValueError("该邮箱已被其他用户使用")

        async with in_transaction():
            # 更新用户邮箱
            user = change_request.user
            old_email = user.email
            user.email = change_request.new_email
            await user.save()

            # 标记请求为已验证
            change_request.is_verified = True
            change_request.verified_at = datetime.now(timezone.utc)
            await change_request.save()

            # 发送确认邮件到新邮箱
            await cls._send_confirmation_email(user, old_email)

            logger.info(
                f"用户 {user.username} 成功更改邮箱: {old_email} -> {user.email}"
            )

            return user

    @classmethod
    async def _send_verification_email(
        cls,
        change_request: EmailChangeRequest,
        verification_token: Optional[str] = None,
    ):
        """发送验证邮件"""

        if not verification_token:
            # 如果没有提供令牌，说明是重新发送，需要生成新令牌
            verification_token = secrets.token_urlsafe(32)
            token_hash = hashlib.sha256(verification_token.encode()).hexdigest()
            change_request.verification_token = token_hash
            change_request.token_expires_at = datetime.now(timezone.utc) + timedelta(
                hours=24
            )
            await change_request.save()

        try:
            await email_service.send_email_change_verification(
                to_email=change_request.new_email,
                username=change_request.user.username,
                old_email=change_request.old_email,
                new_email=change_request.new_email,
                verify_token=verification_token,
            )
        except Exception as e:
            logger.error(f"发送邮箱更改验证邮件失败: {e}")
            raise Exception("发送验证邮件失败，请稍后重试")

    @classmethod
    async def _send_confirmation_email(cls, user: User, old_email: str):
        """发送邮箱更改确认邮件"""

        try:
            # 发送到新邮箱
            await email_service.send_email_change_confirmation(
                to_email=user.email,
                username=user.username,
                old_email=old_email,
                new_email=user.email,
            )

            # 发送通知到旧邮箱
            await email_service.send_email_change_notification(
                to_email=old_email,
                username=user.username,
                old_email=old_email,
                new_email=user.email,
            )
        except Exception as e:
            logger.error(f"发送邮箱更改确认邮件失败: {e}")
            # 不抛出异常，因为邮箱已经更改成功

    @classmethod
    async def cancel_email_change_request(cls, user: User, request_id: int):
        """取消邮箱更改请求"""

        change_request = await EmailChangeRequest.filter(
            id=request_id, user=user, is_verified=False
        ).first()

        if not change_request:
            raise ValueError("邮箱更改请求不存在或已完成")

        # 标记为已取消（通过设置过期时间为过去时间）
        change_request.token_expires_at = datetime.now(timezone.utc) - timedelta(
            hours=1
        )
        await change_request.save()

        logger.info(
            f"用户 {user.username} 取消了邮箱更改请求: {change_request.old_email} -> {change_request.new_email}"
        )

    @classmethod
    async def get_email_change_history(
        cls, user: User, page: int = 1, size: int = 20
    ) -> Dict[str, Any]:
        """获取邮箱更改历史"""

        offset = (page - 1) * size

        # 获取总数
        total = await EmailChangeRequest.filter(user=user).count()

        # 获取历史记录
        history_records = (
            await EmailChangeRequest.filter(user=user)
            .order_by("-created_at")
            .offset(offset)
            .limit(size)
        )

        # 计算总页数
        pages = (total + size - 1) // size

        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
            "history": [
                {
                    "id": record.id,
                    "old_email": record.old_email,
                    "new_email": record.new_email,
                    "is_verified": record.is_verified,
                    "verified_at": record.verified_at,
                    "created_at": record.created_at,
                    "expires_at": record.token_expires_at,
                }
                for record in history_records
            ],
        }

    @classmethod
    async def get_pending_email_changes(cls, user: User) -> Dict[str, Any]:
        """获取用户待处理的邮箱更改请求"""

        pending_requests = await EmailChangeRequest.filter(
            user=user,
            is_verified=False,
            token_expires_at__gt=datetime.now(timezone.utc),
        ).order_by("-created_at")

        return {
            "pending_requests": [
                {
                    "id": request.id,
                    "old_email": request.old_email,
                    "new_email": request.new_email,
                    "created_at": request.created_at,
                    "expires_at": request.token_expires_at,
                }
                for request in pending_requests
            ]
        }
