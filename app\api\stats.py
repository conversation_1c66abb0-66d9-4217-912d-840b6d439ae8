from fastapi import APIRouter, HTTPException, Depends, Query
from app.services.baidu_pan_service import baidu_pan_service
from app.services.quark_pan_service import quark_pan_service
from app.services.xunlei_pan_service import xunlei_pan_service
from app.services.aliyun_service import aliyun_pan_service
from app.crawlers.panku8_crawler import panku8_crawler
from app.utils.common import format_time
from app.utils.cache import ttl_cache
from app.models.resource import PanResource
from app.models.submission import SubmissionTask
from app.models.enums import TaskStatus
from datetime import datetime, timedelta
from app.middleware.verify import verify_origin
from tortoise import Tortoise
from typing import Optional
import time
import logging

logger = logging.getLogger("stats-api")
router = APIRouter(tags=["stats"])


# API路由
@router.get("/", summary="API路由", description="API路由")
async def root():
    return {"message": "网盘搜索API服务正常运行"}


@router.get(
    "/resource_stats",
    dependencies=[Depends(verify_origin)],
    summary="获取资源统计信息",
    description="获取资源统计信息，包括总资源数、昨日新增和待处理任务数量",
)
@ttl_cache(ttl=14400, maxsize=100)  # 4小时缓存，最多100个条目
async def get_resource_stats():
    """获取资源统计信息，包括总资源数、昨日新增和待处理任务数量"""
    try:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = today - timedelta(days=1)

        # 获取资源统计
        total_count = await PanResource.all().count()
        yesterday_count = await PanResource.filter(
            created_at__gte=yesterday, created_at__lt=today
        ).count()

        # 获取待处理任务数量（状态为 ACCEPTED 或 PROCESSING 的任务）
        pending_tasks_count = await SubmissionTask.filter(
            status__in=[TaskStatus.ACCEPTED, TaskStatus.PROCESSING]
        ).count()

        return {
            "status": "success",
            "total": total_count,
            "yesterday": yesterday_count,
            "pending_tasks": pending_tasks_count,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取资源统计信息失败: {str(e)}")


@router.get(
    "/check_service",
    dependencies=[Depends(verify_origin)],
    summary="检查网盘服务状态",
    description="检查网盘服务状态",
)
async def check_service():
    """检查网盘服务状态"""
    try:
        result = {
            "status": "success",
            "services": {},
            "check_time": format_time(datetime.now().timestamp()),
        }
        # 检查百度网盘状态
        try:
            baidu_check = await baidu_pan_service.check_cookie_valid(account_index=0)
            if baidu_check.get("status") == "success":
                result["services"]["baidu"] = {
                    "status": "正常",
                    "message": "Cookie有效",
                    "username": baidu_check.get("username", "未知用户"),
                    "remaining_space": f"{baidu_check.get('remaining_space_gb', 0)}GB",
                }
            else:
                result["services"]["baidu"] = {
                    "status": "异常",
                    "message": baidu_check.get("message", "Cookie已失效"),
                }
        except Exception as e:
            result["services"]["baidu"] = {
                "status": "异常",
                "message": f"检查异常: {str(e)}",
            }
        # 检查夸克网盘状态
        try:
            quark_check = await quark_pan_service.check_cookie_valid(account_index=0)
            if quark_check.get("status") == "success":
                result["services"]["quark"] = {
                    "status": "正常",
                    "message": "Cookie有效",
                    "username": quark_check.get("username", "未知用户"),
                    "remaining_space": f"{quark_check.get('remaining_space_gb', 0)}GB",
                }
            else:
                result["services"]["quark"] = {
                    "status": "异常",
                    "message": quark_check.get("message", "Cookie已失效"),
                }
        except Exception as e:
            result["services"]["quark"] = {
                "status": "异常",
                "message": f"检查异常: {str(e)}",
            }

        # 检查爬虫服务状态
        try:
            test_result = await panku8_crawler.search("测试", limit=1)
            crawler_status = test_result.get("status") == "success"
            result["services"]["crawler"] = {
                "status": "正常" if crawler_status else "异常",
                "message": (
                    "爬虫服务正常"
                    if crawler_status
                    else f"爬虫异常: {test_result.get('message')}"
                ),
            }
        except Exception as e:
            result["services"]["crawler"] = {
                "status": "异常",
                "message": f"爬虫异常: {str(e)}",
            }
        all_normal = all(
            service.get("status") == "正常" for service in result["services"].values()
        )
        result["all_services_normal"] = all_normal
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查服务状态异常: {str(e)}")


@router.get(
    "/db_status",
    dependencies=[Depends(verify_origin)],
    summary="检查数据库连接状态",
    description="检查数据库连接状态",
)
async def db_status():
    """
    检查数据库连接状态
    """
    try:
        connection = Tortoise.get_connection("default")
        if connection:
            resource_count = await PanResource.all().count()
            return {
                "status": "success",
                "message": "数据库连接正常",
                "resource_count": resource_count,
                "connection_info": {
                    "type": connection.__class__.__name__,
                    "db_path": "sqlite://db.sqlite3",
                    "connected": True,
                },
            }
        else:
            return {
                "status": "error",
                "message": "数据库连接对象不存在",
                "connected": False,
            }
    except Exception as e:
        return {
            "status": "error",
            "message": f"检查数据库状态异常: {str(e)}",
            "connected": False,
        }


@ttl_cache(ttl=1800, maxsize=500)  # 30分钟缓存，最多500个条目
async def _check_resource_status_cached(
    share_url: str, pan_type: int, resource_id: Optional[str] = None
) -> dict:
    """检查网盘资源状态的缓存版本（内部函数）"""
    start_time = time.time()

    try:
        # 先从数据库获取资源信息
        resource = None
        if resource_id:
            resource = await PanResource.filter(resource_key=resource_id).first()
            if resource and not share_url:
                share_url = resource.original_url

        # 百度和迅雷网盘的特殊处理
        if pan_type == 1 or pan_type == 4:
            # 先判断是否有original_url，如果没有再通过爬虫获取
            if not share_url or not (resource and resource.original_url):
                # 没有原始链接，需要通过爬虫获取
                if pan_type == 1:
                    detail_url = f"https://panku8.com/res/a01{resource_id}"
                elif pan_type == 4:
                    detail_url = f"https://panku8.com/res/a04{resource_id}"
                # 使用默认请求头
                default_headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }
                share_result = await panku8_crawler.get_share_link(
                    detail_url, default_headers
                )
                logger.info(share_result)
                if share_result.get("status") != "success":
                    logger.error(f"获取分享链接失败: {share_result.get('message')}")
                    return {
                        "status": "error",
                        "valid": False,
                        "message": share_result.get("message", "获取分享链接失败"),
                        "share_url": None,
                        "check_time": format_time(time.time()),
                    }
                share_url = share_result.get("share_url", "")
                if not share_url:
                    return {
                        "status": "error",
                        "valid": False,
                        "message": "获取到的分享链接为空",
                        "share_url": None,
                        "check_time": format_time(time.time()),
                    }
                # 更新数据库 original_url 字段
                try:
                    if not resource:
                        resource = await PanResource.filter(
                            resource_key=resource_id
                        ).first()
                    if resource:
                        resource.original_url = share_url
                        await resource.save(update_fields=["original_url"])
                except Exception:
                    pass  # 不中断流程

        # 检查资源状态
        if pan_type == 1:
            status_result = await baidu_pan_service.check_resource_status(share_url)
        elif pan_type == 2:
            status_result = await quark_pan_service.check_resource_status(share_url)
        elif pan_type == 3:
            logger.info(f"阿里网盘检查资源状态: {share_url}")
            status_result = await aliyun_pan_service.check_resource_status(share_url)
        elif pan_type == 4:
            logger.info(f"迅雷网盘检查资源状态: {share_url}")
            status_result = await xunlei_pan_service.check_resource_status(share_url)
        else:
            return {"valid": False, "message": "不支持的网盘类型"}

        # 检查并更新数据库中的资源状态
        try:
            resource_key = resource_id
            if resource_key:
                resource = await PanResource.filter(resource_key=resource_key).first()
                if resource:
                    resource.verified_status = (
                        "valid" if status_result.get("valid") else "invalid"
                    )
                    # 如果资源失效，删除该资源
                    if not status_result.get("valid"):
                        await resource.delete()
                        logger.info(f"删除资源: {resource_key}")
                    else:
                        await resource.save()
        except Exception:
            pass  # 继续处理，不中断检查流程

        total_time = time.time() - start_time
        logger.info(f"资源状态检查完成，耗时: {total_time:.2f}秒")

        return {
            "status": "success",
            "valid": status_result.get("valid", False),
            "message": status_result.get("message", "未知状态"),
            "share_url": share_url,
            "check_time": format_time(time.time()),
        }
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"检查资源状态过程中发生异常: {str(e)}, 耗时: {total_time:.2f}秒")
        raise HTTPException(
            status_code=500, detail=f"检查资源状态过程中发生错误: {str(e)}"
        )


@router.get(
    "/check_resource_status",
    dependencies=[Depends(verify_origin)],
    summary="检查网盘资源状态",
    description="检查网盘资源状态是否有效",
)
async def check_resource_status(
    share_url: str = Query(None, description="网盘分享链接"),
    pan_type: Optional[int] = Query(
        None, description="网盘类型: 1=百度网盘, 2=夸克网盘, 3=阿里云盘, 4=迅雷网盘"
    ),
    resource_id: Optional[str] = Query(None, description="资源ID"),
):
    """检查网盘资源状态是否有效"""
    if not share_url and not resource_id:
        raise HTTPException(status_code=400, detail="分享链接和resource_id不能同时为空")

    if pan_type is None:
        raise HTTPException(status_code=400, detail="网盘类型不能为空")

    # 调用缓存版本的函数
    return await _check_resource_status_cached(share_url, pan_type, resource_id)
