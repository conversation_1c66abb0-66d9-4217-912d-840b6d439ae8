import urllib.request
import urllib.parse
import json


def get_expiretime(API_SERVER_ADDRESS, TOKEN_ID):
    query = {"id": TOKEN_ID}
    url = API_SERVER_ADDRESS + "get_expiretime?" + urllib.parse.urlencode(query)
    res_data = urllib.request.urlopen(url)
    return res_data.read().decode("utf-8")


def get_proxies(
    API_SERVER_ADDRESS,
    TOKEN_ID,
    SIZE,
    SCHEMES,
    SUPPORT_HTTPS,
    RESTIME_WITHIN_MS,
    FORMAT,
):
    query = {
        "id": TOKEN_ID,
        "size": SIZE,
        "schemes": SCHEMES,
        "support_https": SUPPORT_HTTPS,
        "restime_within_ms": RESTIME_WITHIN_MS,
        "format": FORMAT,
    }
    url = API_SERVER_ADDRESS + "get_proxies?" + urllib.parse.urlencode(query)
    res_data = urllib.request.urlopen(url)
    return res_data.read().decode("utf-8")


API_SERVER_ADDRESS = "http://uu-proxy.com/api/"
TOKEN_ID = "NMS3ZW3M3F"
SIZE = 2
SCHEMES = "socks5"
SUPPORT_HTTPS = "true"
RESTIME_WITHIN_MS = 2000
FORMAT = "json"

if __name__ == "__main__":
    res = get_expiretime(API_SERVER_ADDRESS, TOKEN_ID)
    print("过期时间：")
    print(res)

    res = get_proxies(
        API_SERVER_ADDRESS,
        TOKEN_ID,
        SIZE,
        SCHEMES,
        SUPPORT_HTTPS,
        RESTIME_WITHIN_MS,
        FORMAT,
    )
    print("获取代理：")
    print(res)
